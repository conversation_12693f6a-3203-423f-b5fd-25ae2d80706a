#
# Be sure to run `pod lib lint TVKPlayerForMac.podspec' to ensure this is a
# valid spec before submitting.
#

Pod::Spec.new do |s|
  s.name             = 'TVKPlayerForMac'
  s.version          = '1.0.0'
  s.summary          = 'Tencent Video Player SDK for macOS'

  s.description      = <<-DESC
  TVKPlayerForMac is a video player framework for macOS applications, 
  providing comprehensive video playback capabilities with various features 
  including audio effects, video effects, and subtitle support.
                       DESC

  s.homepage         = 'https://v.qq.com/'
  s.license          = { :type => 'Copyright', :text => 'Copyright © Tencent. All rights reserved.' }
  s.author           = { 'Tencent' => '<EMAIL>' }
  s.source           = { :http => '' }

  s.platform = :osx
  s.osx.deployment_target = '10.12'
  
  s.vendored_frameworks = 'TVKPlayerForMac.xcframework'
  s.resource = 'TVKPlayerForMac.xcframework/kmm_tvk_res.bundle'
  
  s.frameworks = 'AppKit', 'CoreMedia', 'AVFoundation'
  s.library = 'c++'
  
  s.requires_arc = true
  s.pod_target_xcconfig = { 'VALID_ARCHS' => 'arm64 x86_64' }
end