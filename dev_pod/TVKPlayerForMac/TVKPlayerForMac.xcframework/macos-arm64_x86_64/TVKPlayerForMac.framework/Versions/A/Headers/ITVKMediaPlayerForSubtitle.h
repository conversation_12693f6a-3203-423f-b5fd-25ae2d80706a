//
//  ITVKMediaPlayerForSubtitle.h
//  TXVPlayer
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/10/23.
//  Copyright © 2019 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "ITVKMediaPlayer.h"

@class TXVSubTitleModel;

@protocol ITVKMediaPlayerForSubtitle <NSObject, ITVKMediaPlayer>

/**
 * 切换字幕结果
 * @param isLoad 是否字幕初始加载，否则是手动切换
 */
- (void)onSwitchSubTitle:(TXVSubTitleModel *)switchedSubtilte isLoad:(BOOL)isLoad error:(NSError *)error;

@end
