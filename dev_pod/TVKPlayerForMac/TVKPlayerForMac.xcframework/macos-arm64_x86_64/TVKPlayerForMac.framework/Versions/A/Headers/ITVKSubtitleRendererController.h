/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITVKSubtitleRendererController.h
 * @brief    字幕渲染控制器协议. 用于设置字幕渲染显示参数和控制是否渲染显示等
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/12/26
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKSubtitleRenderParams.h"
#import "TVKSubtitleRenderInfo.h"

NS_ASSUME_NONNULL_BEGIN

/// 字幕绘制信息回调
@protocol ITVKSubtitleRendererControllerDelegate <NSObject>

- (void)onSubtitleRendered:(TVKSubtitleRenderInfo *)subtitleRenderInfo;

@end


/// @brief 描述见文件头
@protocol ITVKSubtitleRendererController <NSObject>

/// 字幕绘制信息回调
@property (nonatomic, weak) id<ITVKSubtitleRendererControllerDelegate> delegate;

/// 控制字幕是否渲染显示
/// @param enable 是否开启字幕显示
- (void)enableRender:(BOOL)enable;

/// 设置字幕渲染显示控制参数
/// @param renderParam   渲染控制参数
- (void)setRenderParam:(TVKSubtitleRenderParams *)renderParam;

/// 获取当前字幕渲染显示控制参数
- (TVKSubtitleRenderParams *)renderParam;

@end

NS_ASSUME_NONNULL_END
