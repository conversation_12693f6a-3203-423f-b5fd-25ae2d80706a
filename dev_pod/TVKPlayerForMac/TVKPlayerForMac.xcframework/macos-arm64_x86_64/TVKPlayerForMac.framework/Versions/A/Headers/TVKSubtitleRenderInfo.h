/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TVKSubtitleRenderInfo.h
 * @brief    字幕绘制信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/11/13
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 文本段信息
@interface TVKTextSegment : NSObject
/// 纯文本内容
@property (nonatomic, copy) NSString *text;
/// 该段文本颜色，ARGB格式，默认0xFFFFFFFF
@property (nonatomic, assign) int color;
/// 该段文本绘制区域
@property (nonatomic, assign) CGRect rect;

@end

/// 整条字幕绘制信息
@interface TVKSubtitleRenderInfo : NSObject
/// 文本段列表
@property (nonatomic, copy) NSArray<TVKTextSegment *> *textSegments;

@end

NS_ASSUME_NONNULL_END
