/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKVODPlayInfo.h
 * @brief    点播播放信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2019/9/12
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "TVKAudioTrackModel.h"
#import "TVKAudioEffectModel.h"
#import "TVKMediaPlayInfo.h"
#import "TVKVODADInfo.h"
#import "TVKVODExtraInfo.h"
#import "TVKVODMasterDirectInfo.h"
#import "TVKVodAdDot.h"
#import "TVKVodSimulatedLiveInfo.h"
#import "TVKDarkLogoInfo.h"

/// 多网卡条件下优质域名地址
/// 字段参考文档:https://tapd.woa.com/qqvideo_prj/markdown_wikis/show/#1210114481001144857
@interface TVKVideoMultiNetUrlInfo : NSObject
/// cdn地址
@property (nonatomic, copy) NSString *url;
/// cdn编号
@property (nonatomic, assign) int vt;

@end

/**
 * 点播播放信息
 */
@interface TVKVODPlayInfo : TVKMediaPlayInfo

/**
 * 播放预览图片信息
 */
@property (atomic, strong) NSArray<TVKMovieThumbInfo *> *thumbInfoArray;

/**
 * 用于判断是限播还是杜比试看。(历史原因，丑陋的实现)
 */
@property (nonatomic, assign) int exem;

/**
 * subtitle信息
 */
@property (nonatomic, strong) NSArray<TVKSubTitleModel *> *subTitleModelArray;

/**
 * 当前使用的字幕
 */
@property (nonatomic, strong) TVKSubTitleModel *currentSubTitle;

/**
 * 播放的起始时间点
 */
@property (nonatomic, assign) NSTimeInterval startPosition;

/**
 * 播放跳过片尾时间点
 */
@property (nonatomic, assign) NSTimeInterval skipEndPosition;

/**
 * 视频长度，节点root.vl.vi.td
 */
@property (nonatomic, assign) NSTimeInterval duration;

/**
 * 付费状态, 0:未检查, 1:单片付费, 2:包月付费, -1:未付费, -2:未登录，节点root.vl.vi.ch
 */
@property (nonatomic, assign) int chargeState;

/**
 * 点播试看开始时间
 */
@property (nonatomic, assign) NSTimeInterval vodPreviewStartPositionSec;

/**
 * 点播试看时长
 */
@property (nonatomic, assign) NSTimeInterval vodPreviewDurationSec;

/**
 * 取值0, 1, 2, 3, 5, 6, 130, 131, 133, 134
 * 注意：当视频状态为2时, 视频可以播放, 其他状态都不可播放；视频状态8为收费状态
 */
@property (nonatomic, assign) int videoState;

/**
 是否是秒播换链
 */
@property (nonatomic, assign) BOOL isQuickPlay;

/**
 * 视频下载类型，1：http，2：p2p 3:HLS 8:m3u8直出
 */
@property (atomic, assign) NSInteger dltype;

/**
 * 节点root.vl.vi.lnk
 */
@property (nonatomic, copy) NSString *link;

/**
 * 分片数，节点root.vl.vi.cl.fc
 */
@property (nonatomic, assign) int clipCount;

/**
 * 首次加载对应的音视频码率(单位:kByte/s) 用作上报, 节点root.vl.vi.br
 */
@property (nonatomic, assign) int rate;

/**
 * 媒资付费状态，仅在秒播时使用
 */
@property (nonatomic, assign) int mediaState;

/**
 * 仅用于数据上报
 */
@property (atomic, assign) int videoType;

/**
 * 宽高比
 */
@property (nonatomic, assign) float aspectRatio;

/**
 * m3u8字符串，用于m3u8直出
 */
@property (nonatomic, copy) NSString *m3u8;

/**
 * 视频文件大小
 */
@property (nonatomic, assign) int64_t fileSize;

/**
 * 当前视频的音轨信息
 */
@property (nonatomic, strong) TVKAudioTrackModel *currentAudioTrack;

/**
 * 当前视频的音轨列表
 */
@property (nonatomic, copy) NSArray<TVKAudioTrackModel *> *audioTrackList;

/// 音效列表
@property (atomic, copy) NSArray<TVKAudioEffectModel *> *audioEffectList;

/**
 * 是否是快发版本视频，快发版本视频是非完整版视频
 */
@property (nonatomic, assign) BOOL fVideo;

/**
 * 一些扩展信息，主要用于数据上报等
 */
@property (nonatomic, strong) TVKVODExtraInfo *extraInfo;

/**
 * 广告信息
 */
@property (nonatomic, strong) TVKVODADInfo *adInfo;

/**
 * 后台控制是否需要客户端缓存
 * 0:否
 * 1:客户端控制
 */
@property (nonatomic, assign) BOOL cached;

/// 点播视频安全防护字段，0表示无需防护，1表示播放器的本地代理防护
@property (atomic, assign) int pttn;

/// 点播加密类型，播放器不理解
@property (nonatomic, strong) NSString *encryption;

/// 视频是否强加密
@property (nonatomic, assign) BOOL strongEncryption;

/// 嵌套master直出信息
@property (nonatomic, strong) TVKVODMasterDirectInfo *masterDirectInfo;

/// 当前播放视频是否是嵌套m3u8资源
@property (nonatomic, assign, readonly, getter=isNestM3u8AVSVideoSource) BOOL nestM3u8AVSVideoSource;

/// 自适应清晰度允许切换的最大码率值(单位bps)
@property (nonatomic, assign) int maxBitrateBps;

/// vinfo bandwidthlevel http/p2p下载带宽等级, 仅腾讯视频有用
/// 1:使用率在0到40%; 2:使用率在40%到80%; 3:使用率在80%到100%; 4:使用率超过100%.
@property (nonatomic, assign) int bandwidthLevel;

/// 透传给下载的策略扩展字段，不理解
@property (nonatomic, strong) NSString *strategyParamExtInfo;

/// vvip时实际播的纯净片源的vid
@property (nonatomic, copy) NSString *pureVid;

/// 非vvip的压片广告时间信息
@property (nonatomic, strong) NSMutableArray<TVKVodAdDot *> *embeddedAdDots;

/// 文件名称
@property (nonatomic, copy) NSString *fileName;

/// 硬字幕位置信息，字幕最低点位置在视频高度上的比例
@property (nonatomic, assign) float subtitleNadir;

/// 硬字幕位置信息，字幕最高点位置在视频高度上的比例
@property (nonatomic, assign) float subtitlePeak;

/// 投屏设置，透传出去 tvk不理解
@property (nonatomic, copy) NSString *castSet;

/// 表示包含创意中插广告的视频文件总时长。单位Ms
@property (nonatomic, assign) int64_t totalDurationMs;

/// cdn播放体验等级.透传给下载，播放器无需理解
@property (nonatomic, copy) NSString *playExperienceLevel;

/// 轮询上报URL
@property (nonatomic, copy) NSString *pollingReportUrl;

/// 动效水印信息URL
@property (nonatomic, copy) NSString *animationUrl;

/// root.vl.vi.multi_net_ul 节点 多网卡cdn备份域名地址相关信息
@property (nonatomic, copy) NSArray<TVKVideoMultiNetUrlInfo *> *multiNetUrlInfos;

/// 伪直播信息
@property (nonatomic, strong) TVKVodSimulatedLiveInfo *simulatedLiveInfo;

/// 暗水印信息
@property (nonatomic, strong) TVKDarkLogoInfo *darkLogoInfo;

/// 压流广告的点位信息 json字符串 透传给APP
@property (nonatomic, copy) NSString *dotInfo;

/// 特性字段，不理解，透传
@property (nonatomic, assign) int feature;

@end
