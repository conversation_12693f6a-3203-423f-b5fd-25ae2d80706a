/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKUrlAsset.h
 * @brief    Url资源类,主要用于外部url播放
 * <AUTHOR> chen
 * @version  1.0.0
 * @date     2020/12/7
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKAssetBase.h"

/// @brief Url资源类,主要用于外部url播放
@interface TVKUrlAsset : TVKAssetBase

/// 需要播放的外部播放地址
@property (nonatomic, copy, readonly) NSURL *url;

/// 构造方法
/// @param url 外部链接或本地文件地址
- (instancetype)initWithUrl:(NSURL *)url;

@end
