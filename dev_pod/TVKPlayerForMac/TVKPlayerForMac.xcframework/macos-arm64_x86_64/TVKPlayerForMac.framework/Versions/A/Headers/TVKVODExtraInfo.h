//
//  TVKVODExtraInfo.h
//  TVKPlayer
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/10/17.
//  Copyright © 2019 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// @brief vod 额外信息
@interface TVKVODExtraInfo : NSObject
/// vkey有效期 单位s
@property (nonatomic, assign) int ct;
/// root.vl.vi.keyid, HLS keyID
@property (nonatomic, copy) NSString *keyID;
/// root.vl.vi.base，加密用密钥
@property (nonatomic, copy) NSString *base;
/// root.vl.vi.tm，UNIX时间戳，单位：秒
@property (nonatomic, assign) int64_t tm;
/// 资源有效截止时间戳，单位：秒
@property (nonatomic, assign) int64_t retValidTimeSec;
/// root.vl.vi.fmd5
@property (nonatomic, copy) NSString *fMD5;
/// ABTest测试分组id.用于上报
@property (atomic, copy) NSString *tstId;
/// ABTest bucket id，由app传入，由vinfo返回
@property (nonatomic, copy) NSString *testBucket;
/// root.vl.vi.type 用作上报
@property (nonatomic, assign) NSInteger type;
/// root.ip，客户端IP
@property (nonatomic, copy) NSString *ip;
/// root.vl.vi.enc，加密标识，0：无，1：客户端加密，2：cdn加密
@property (nonatomic, assign) int enc;
/// getvinfo返回的整个xml，离线下载给下载组件用
@property (nonatomic, strong) NSString *xmlString;      

@end

NS_ASSUME_NONNULL_END
