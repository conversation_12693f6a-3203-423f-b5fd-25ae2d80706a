/*****************************************************************************
 * @copyright Copyright (C), 1998-2025, Tencent Tech. Co., Ltd.
 * @file     TVKPlayerSyncStrategy.h
 * @brief    播放器同步器的策略配置 用于定义同步期间pts差值采用何种策略去做同步，倍数或者是seek
 * <AUTHOR>
 * @version  1.0.0
 * @date     2025/3/13
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

///@brief 定义同步的动作类型，枚举类型
typedef NS_ENUM(NSInteger, TVKPlayerSyncActionType) {
    /// 调整播放速率
    TVKPlayerSyncActionFloatSpeedRate = 0,
    /// 执行seek
    TVKPlayerSyncActionVoidSeek = 1,
};

/// @brief 同步动作
@interface TVKPlayerSyncAction : NSObject
/// 动作类型
@property (nonatomic, assign, readonly) TVKPlayerSyncActionType type;
/// 动作对应的参数
@property (nonatomic, strong, readonly) id param;

/// @brief 禁用默认的构造方法
- (instancetype)init NS_UNAVAILABLE;

/// @brief 构造方法，适用于初始化有参情况，如action type为调整播放速率的构造
/// @param action 动作类型
/// @param param 动作对应的参数
- (instancetype)initWithActionType:(TVKPlayerSyncActionType)action param:(id)param;

/// @brief 构造方法，适用于初始化无参情况，如action type为执行seek的构造
/// @param action 动作类型
- (instancetype)initWithActionType:(TVKPlayerSyncActionType)action;
@end

/// @brief 播放器同步器的策略
@interface TVKPlayerSyncStrategy : NSObject

/// @brief获取策略表，可重写该方法修改策略表
///
/// 默认的配置表如下
/// 根据播放位置与目标位置的时间差（单位：毫秒）触发相应调整操作：
///
/// --- 落后调整（负时间差值）---
/// @note 值对应触发阈值，动作按最小满足条件执行
///   - -200ms  : 1.05倍速播放
///   - -600ms  : 1.1倍速播放
///   - -1000ms : 1.2倍速播放
///   - -2000ms : 1.4倍速播放
///   - -4000ms : 1.6倍速播放
///   - -8000ms : 执行Seek同步
///
/// --- 超前调整（正时间差值）---
/// @note 值对应触发阈值，动作按最小满足条件执行
///   + 200ms  : 0.95倍速播放
///   + 600ms  : 0.9倍速播放
///   + 1000ms : 0.8倍速播放
///   + 2000ms : 0.7倍速播放
///   + 4000ms : 0.6倍速播放
///   + 8000ms : 执行Seek同步
///
/// @see TVKPlayerSyncActionType
/// @see TVKPlayerSyncAction
- (NSDictionary<NSNumber *, TVKPlayerSyncAction *> *)strategyMap;
@end

NS_ASSUME_NONNULL_END

