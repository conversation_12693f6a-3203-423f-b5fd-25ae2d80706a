/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TVKDarkLogoInfo.h
 * @brief    暗水印后台vinfo返回信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/4/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class TVKLogoFlickerInfo;

/// 暗水印后台vinfo返回信息
@interface TVKDarkLogoInfo : NSObject

/// 水印的持续时长, 单位：毫秒 （暂时未使用）
@property (nonatomic, assign) NSInteger durationMs;

/// 水印的开始时间, 单位：毫秒 （暂时未使用）
@property (nonatomic, assign) NSInteger startTimeMs;

/// 水印显示的默认透明度。取值范围0.0f～100.0f
@property (nonatomic, assign) CGFloat defaultAlpha;

/// 水印图片的base64字符串，需解密后生成图片
@property (nonatomic, copy) NSString *logo;

/// 水印闪烁信息
@property (nonatomic, strong) TVKLogoFlickerInfo *flickerInfo;

@end

/// 暗水印闪烁信息
@interface TVKLogoFlickerInfo : NSObject

/// 每次水印闪烁的持续时长, 单位：毫秒
@property (nonatomic, assign) NSInteger durationMs;

/// 每次水印闪烁的间隔时长, 单位：毫秒
@property (nonatomic, assign) NSInteger gapMs;

/// 水印闪烁时显示的透明度。取值范围0.0f～100.0f
@property (nonatomic, assign) CGFloat alpha;

@end

NS_ASSUME_NONNULL_END
