/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TVKOnlineVodQuickPlayAsset.h
 * @brief    秒播资源类型，主要用于腾讯视频点播秒播播放
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/8/6
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKAssetBase.h"

/// @brief Vid资源类型，主要用于腾讯视频Vid播放
@interface TVKOnlineVodQuickPlayAsset : TVKAssetBase
/// 秒播字符串
@property (nonatomic, copy) NSString *preVid;

/// 专辑id
@property (nonatomic, copy) NSString *cid;

/// 栏目id(lid).非必填字断，如果没有则无须设置
@property (nonatomic, copy) NSString *lid;

/// 初始化方法
/// @param preVid 秒播字符串
- (instancetype)initWithPreVid:(NSString *)preVid cid:(NSString *)cid;


@end

