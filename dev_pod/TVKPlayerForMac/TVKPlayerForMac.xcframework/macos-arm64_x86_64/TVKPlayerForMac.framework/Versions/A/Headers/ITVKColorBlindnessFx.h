/*****************************************************************************
 * @copyright Copyright (C), 1998-2021, Tencent Tech. Co., Ltd.
 * @file     ITVKColorBlindnessFx.h
 * @brief    色盲接口
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/2/19
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITVKVideoFx.h"

/// 色盲矫正类型
/// 注意！！！这些定的所有枚举均对外，千万不要改现有枚举对应的值，这会导致依赖老版本tvk的sdk和依赖新版本tvk的工程一起编译运行后走到错误的逻辑中。
/// 别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!
typedef NS_ENUM(NSUInteger, TVKColorBlindFxType) {
    // 红色盲矫正
    TVKColorBlindFxTypeProtanopia,
    // 绿色盲矫正
    TVKColorBlindnessFxTypeDeuteranopia,
    // 蓝黄色盲矫正
    TVKColorBlindnessFxTypeTritanopia
};

/**
 * 色盲特效接口类
 */
@protocol ITVKColorBlindnessFx <ITVKVideoFx>

/**
 * 设置色盲的类别
 * @param type 色盲类别
 */
- (void)setColorBlindnessType:(TVKColorBlindFxType)type;

@end
