/*****************************************************************************
* @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
* @file     TVKVODFreeFlowInfo.m
* @brief   免流信息的数据结构 1: 包含了后台返回的信息 2：包含了用户输入的免流参数  3:提供了最终免流状态的查询方法
* <AUTHOR>
* @version  1.0.0
* @date     2020/01/10
* @license  GNU General Public License (GPL)
 *****************************************************************************/


#import <Foundation/Foundation.h>
#import "TVKPlayerDefine.h"

NS_ASSUME_NONNULL_BEGIN
/// @brief 免流逻辑
@interface TVKFreeFlowInfo : NSObject

/**
 后台返回的免流信息
 取值为 "0" ：代表vinfo信息不是免流，需要注意url地址的拼接
 取值为 “1” ： 代表vinfo信息是免流，   返回的url地址直接使用，不需要拼接
 取值为其他：非法值，为了兼容性，采用客户端判断
 */
@property (nonatomic, assign) NSUInteger freeul;

/**
 用户输入的免流信息，携带给后台来决策
 赋值时机：vinfo parse的时机
 范例：联通免流
 unicom：联通免流参数（字符串）
 unicomtype：联通免流类型（数字）
 (!env.arg.unicom.empty() || env.arg.unicomType >= 0) 为免流
 内部控制ispid, 0:联通免流，1：电信免流，2：移动免流
 联通免流通过unicomType区分具体免流类型
 */
@property (nonatomic, copy) NSDictionary<NSString *, NSString *> *freeFlowParam;

/**
 用户输入的免流信息状态，该变量不参与任何逻辑，只是为了记录和日志输出
 TVKFreeFlowTypeNone = 0,     // 不免流
 TVKFreeFlowTypeUnicom = 1,   // 联通免流
 TVKFreeFlowTypeMobile = 2,   // 移动免流
 TVKFreeFlowTypeTelecom = 3,  // 电信免流
 */
@property (nonatomic, assign) TVKFreeFlowType freeFlowType;

/**
 最终描述此次的vinfo携带的url是否是免流地址
 由于存在灰度和后台刚发版本的过渡阶段，暂时不完全依赖后台的值，
 而是遵循以下的判断规则
 1：如果后台免流信任机制开关(TVK_PLAYER_CONFIG.KEY_use_server_freeflow)关闭，代表后台功能未上线或者后台出问题
   采用老方案，判断用户输入的freeFlowParam，如果空，代表非免流， 非空：免流
 2：如果后台免流信任机制开关(TVK_PLAYER_CONFIG.KEY_use_server_freeflow)打开，代表后台功能上线，运行稳定
  后台返回1：直接认定为免流
  后台返回0：直接认定为非免流
  后台返回其他：回退为客户端判定方案1
 */
@property (nonatomic, assign, readonly) BOOL isFree;

@end

NS_ASSUME_NONNULL_END
