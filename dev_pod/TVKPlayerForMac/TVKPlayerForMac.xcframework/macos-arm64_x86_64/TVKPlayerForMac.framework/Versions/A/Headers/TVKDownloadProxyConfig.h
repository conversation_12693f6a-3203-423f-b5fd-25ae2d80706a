/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TVKDownloadProxyConfig.h
 * @brief    下载组件的配置文件定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2022/7/13
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

static const int kTVKInvalidProxyServiceType = -1;

/// @brief 同头文件描述
@interface TVKDownloadProxyConfig : NSObject

/// service type, 下载侧将会按照service type来开辟内存和磁盘空间
/// 小于等于0的数据，将被视作非法值，请找下载团队申请
@property (nonatomic, assign) NSInteger serviceType;

/// 缓存目录,来存储一些size较小的配置信息
/// 需要保证文件夹存在并可以访问，否则将使用下载内部默认的策略
@property (nonatomic, strong) NSString *cacheDir;

/// 数据目录,用来存储下载数据
/// 需要保证文件夹存在并可以访问，否则将使用下载内部默认的策略
@property (nonatomic, strong) NSString *dataDir;

/// 最大的磁盘使用大小，用于磁盘的使用限制，单位MB
/// 小于等于0的值将会视作非法值
@property (nonatomic, assign) long long maxUseStorageMB;

/// 是否缓存到磁盘,即是否开启边下边播
@property (nonatomic, assign) BOOL vodCachedEnable;

@end

NS_ASSUME_NONNULL_END
