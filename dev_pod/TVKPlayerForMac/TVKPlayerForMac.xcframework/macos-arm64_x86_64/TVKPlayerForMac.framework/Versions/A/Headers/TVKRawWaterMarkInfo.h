/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKRawWaterMarkInfo.h
 Author      : liyukuan
 Version     : 1.0
 Date        : 17/3/6
 Description :
 History     : 17/3/6 初始版本
 ***********************************************************/

#import <Foundation/Foundation.h>
#import <CoreGraphics/CGGeometry.h>

/// @brief 遮标水印：当前水印的原始位置，有些时候需要遮挡，这里承载需要遮挡的区域信息
@interface TVKRawWaterMarkBlockInfo : NSObject
/// 遮标水印位置
@property (nonatomic, assign, readonly) CGRect blockPosition;
/// 是否遮挡logo
@property (nonatomic, assign, readonly) BOOL isShow;
/// 初始化方法
- (instancetype)initWithDic:(NSDictionary *)dict;
@end

/// @brief 水印信息
@interface TVKRawWaterMarkInfo : NSObject
/// 水印位置
@property (nonatomic, assign) CGRect position;
/// 水印图片Md5
@property (nonatomic, copy) NSString *md5;
/// 图片地址
@property (nonatomic, copy) NSString *url;
/// 用来计算水印位置，r = (min(视频宽，视频高)/rw)，水印实际位置 = 后台返回水印位置 * r
@property (nonatomic, assign) int rw;

- (id)initWithDic:(NSDictionary *)dict;

@end

/// @brief 点播
@interface TVKVODWaterMarkInfo : TVKRawWaterMarkInfo
/// https 地址
@property (nonatomic, copy) NSString *httpsUrl;
/// 水印id
@property (nonatomic, assign) NSInteger waterMarkId;
/// 透明属性
@property (nonatomic, assign) NSInteger alpha;

+ (TVKVODWaterMarkInfo *)vodWaterMarkInfoWithDict:(NSDictionary *)dict;

+ (NSArray<__kindof TVKVODWaterMarkInfo *> *)vodWaterMarkInfoArrayWithArray:(NSArray *)array;

@end

/// @brief 直播
@interface TVKLiveWaterMarkInfo : TVKRawWaterMarkInfo
/// 是否已经显示
@property (nonatomic, assign) BOOL isShow;
/// https 地址
@property (nonatomic, copy) NSString *httpsUrl;
/// 透明属性
@property (nonatomic, assign) NSInteger alpha;

+ (TVKLiveWaterMarkInfo *)liveWaterMarkInfoWithDict:(NSDictionary *)dict;

@end

/// @brief 动态水印信息，基类
@interface TVKActionWaterMarkScene : NSObject
/// 开始显示时间
@property (nonatomic, assign) int inTime;
/// 结束显示时间
@property (nonatomic, assign) int outTime;
/// 这里的start是指repeat次数的起始次数
@property (nonatomic, assign) int start;
/// 这里的end是指repeat次数的结束次数
@property (nonatomic, assign) int end;
/// 水印信息
@property (nonatomic, strong) NSArray<__kindof TVKRawWaterMarkInfo *> *waterMarkInfos;

@end

/// @brief 目前仅有点播有动态水印
@interface TVKVODActionWaterMarkScene : TVKActionWaterMarkScene

+ (TVKVODActionWaterMarkScene *)actionWaterMarkSceneWithDict:(NSDictionary *)dict;

@end

typedef NS_ENUM(NSUInteger, TVKActionWaterMarkRunMode) {
    TVKActionWaterMarkRunModeDefault                = 0,
    TVKActionWaterMarkRunModeRelativeToPlayPosition = 1,
    TVKActionWaterMarkRunModeRelativeToPlayTime     = 2,
};

/// @brief 动态水印信息
@interface TVKActionWaterMarkModel : NSObject
/// 每个周期时长
@property (nonatomic, assign) int duration;
/// 周期内开始时间
@property (nonatomic, assign) int start;
/// 用来计算水印位置，r = (min(视频宽，视频高)/rw)，水印实际位置 = 后台返回水印位置 * r
@property (nonatomic, assign) int rw;
/// 重复次数
@property (nonatomic, assign) int repeat;
/// 见TVKActionWaterMarkRunMode的定义
@property (nonatomic, assign) int runMode;
/// 动态水印场景
@property (nonatomic, strong) NSArray<__kindof TVKActionWaterMarkScene *> *actionWaterMarkScenes;

+ (TVKActionWaterMarkModel *)actionWaterMarkModelWithDict:(NSDictionary *)dict;

@end

/// @brief 漂浮水印信息
@interface TVKFloatWaterMarkModel : NSObject
/// 登录态漂浮水印显示flag。0不使用漂浮水印，1使用漂浮水印
@property (nonatomic, assign) int fwFlag;
/// 登录态漂浮水印需要展示的name名称，通过vuid转成的rtxname。这里水印跟其他水印不同，只有文字，需要由app自己来绘制
@property (nonatomic, copy) NSString *uKey;

/// 解析漂浮水印
+ (TVKFloatWaterMarkModel *)floatWaterMarkModelWithDict:(NSDictionary *)dict;

@end

/// @brief 水印信息
@interface TVKWaterMarkModel : NSObject

/// 动态水印地址
@property (nonatomic, copy) NSString *actionUrl;
/// 动态水印信息
@property (nonatomic, strong) TVKActionWaterMarkModel *actionWaterMarkModel;
/// 静态水印信息
@property (nonatomic, strong) NSArray<__kindof TVKRawWaterMarkInfo *> *waterInfos;
/// 静态水印遮标水印信息
@property (nonatomic, strong) NSArray<__kindof TVKRawWaterMarkBlockInfo *> *waterBlockInfos;

/// 漂浮水印.这个水印跟其他水印不一样，只有文字，播放器仅做协议解析，由app进行绘制
@property (nonatomic, strong) TVKFloatWaterMarkModel *floatWaterMarkModel;

@end
