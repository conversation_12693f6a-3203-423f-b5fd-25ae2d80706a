/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TVKSubtitleText.h
 * @brief    字幕文本
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/6/6
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import<Foundation/Foundation.h>

/// @brief 描述见文件头
@interface TVKSubtitleText : NSObject

/// 当前输出的字幕的开始时间
@property (nonatomic, assign) int64_t startTimeMs;

/// 字幕文本字符串，utf-8编码。
/// 对于同一路字幕，如果一个时间点同时命中了两段字幕文本，也是一个一个输出。
/// 对于选择了多路字幕的情况，多个轨道按照选择顺序依次输出
@property (nonatomic, copy) NSString *text;

/// 当前输出的字幕的轨道id，该轨道id即 ITPPlayer#getTrackInfo 返回的数组下标。
@property (nonatomic, assign) int trackID;

// TODO(lennonchen, 2024.6.6): 还需要补充 文本字幕的样式等属性

@end
