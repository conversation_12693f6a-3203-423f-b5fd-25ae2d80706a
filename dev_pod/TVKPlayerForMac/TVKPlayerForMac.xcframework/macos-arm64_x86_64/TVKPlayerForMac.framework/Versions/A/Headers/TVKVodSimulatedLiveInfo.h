/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TVKVodSimulatedLiveFeature.h
 * @brief    伪直播后台vinfo返回信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/11/22
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 伪直播后台vinfo返回信息
@interface TVKVodSimulatedLiveInfo : NSObject

/// 当前vid 播放位置, 单位：秒
@property (nonatomic, assign) NSTimeInterval currentPositionSec;

/// 轮播的完整vid列表
@property (nonatomic, strong) NSArray<NSString *> *vidList;

/// 伪直播的chid
@property (nonatomic, copy) NSString *chid;

/// 伪直播的扩展信息 透传给app
@property (nonatomic, copy) NSString *extInfo;

@end

NS_ASSUME_NONNULL_END
