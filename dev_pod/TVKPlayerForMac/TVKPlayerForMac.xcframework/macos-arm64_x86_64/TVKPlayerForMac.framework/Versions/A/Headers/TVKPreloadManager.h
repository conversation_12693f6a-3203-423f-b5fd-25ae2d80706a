/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TVKPreloadManager.h
 * @brief    预加载接口定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2022/9/07
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKPreloadDefine.h"

@class TVKMediaInfo;
@class TVKUserInfo;

/// @brief 同头文件描述
@interface TVKPreloadManager : NSObject

/// 预加载下一个视频, 在真正要播放该视频时，mediaInfo、userInfo要和这里保持一致，否则达不到加速效果
/// @param mediaInfo 输入视频相关信息
/// @param userInfo 输入用户相关信息
/// @param preloadParam 预加载参数，非必填
/// @param preloadDelegate 预加载数据回调，需要获取预加载回调需要设置
/// @return 标识此次预加载的id
+ (int)preloadWithMediaInfo:(TVKMediaInfo *)mediaInfo
                   userInfo:(TVKUserInfo *)userInfo
               preloadParam:(TVKPreloadParam *)preloadParam
            preloadDelegate:(id<ITVKPreloadDelegate>)preloadDelegate;

/// 停止预加载
/// @param preloadId 预加载任务id
+ (void)stopPreloadWithPreloadId:(int)preloadId;

@end
