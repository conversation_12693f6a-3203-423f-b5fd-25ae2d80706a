/************************************************************
 * @copyright Copyright (C), 1998-2025, Tencent Tech. Co., Ltd.
 * @file     TVKPictureSRProcessResult.h
 * @brief    图片超分处理结果
 * <AUTHOR>
 * @version  1.0.0
 * @date     2025/02/19
 * @license     GNU General Public License (GPL)
 ***********************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 图片超分处理结果
@interface TVKPictureSRProcessResult : NSObject

/// 是否处理成功
@property (nonatomic, assign) BOOL processSuccess;

#if TARGET_OS_OSX
/// 超分后图片，processSuccess为NO时，resultImage为nil
@property (nonatomic, strong, nullable) NSImage *resultImage;
#else
/// 超分后图片，processSuccess为NO时，resultImage为nil
@property (nonatomic, strong, nullable) UIImage *resultImage;
#endif

/// 图片名，由业务方传入，不作为唯一性标识，仅用于回传给业务
@property (nonatomic, copy) NSString *imageName;

/// 总耗时，单位毫秒ms（包括在处理队列中等待的时间，处理耗时）
@property (nonatomic, assign) NSTimeInterval totalTimeMs;

/// 处理耗时，单位毫秒ms（超分处理器处理图片的耗时）
@property (nonatomic, assign) NSTimeInterval processTimeMs;

@end

NS_ASSUME_NONNULL_END
