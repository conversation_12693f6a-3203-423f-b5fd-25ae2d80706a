//
//  TVKDebugDefine.h
//  TVKPlayer
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/10/31.
//  Copyright © 2019 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSUInteger, TVKPlayerTypeConfig) {
    TVKPlayerTypeConfigDefault, // 使用播放器内部的默认配置
    TVKPlayerTypeConfigSysPlayerPrior, // 系统播放器优先
    TVKPlayerTypeConfigSelfPlayerPrior, // 自研播放器优先
};

typedef NS_ENUM(NSUInteger, TVKMediaFormatConfig) {
    TVKMediaFormatConfigDefault, // 使用播放器内部的默认配置
    TVKMediaFormatConfigMp4, // 分片MP4
    TVKMediaFormatConfigWholeMp4, // 整片MP4
    TVKMediaFormatConfigHLS, // HLS
};

typedef NS_ENUM(NSUInteger, TVKPlayModeConfig) {
    TVKPlayModeConfigDefault, // 使用播放器内部的默认配置
    TVKPlayModeConfigNative, // 使用原生播放，不使用代理组件播放
    TVKPlayModeConfigP2P, // 使用代理组件播放
};

typedef NS_ENUM(NSUInteger, TVKSelfPlayerDecodeModeConfig) {
    TVKSelfPlayerDecodeModeConfigDefault, // 使用播放器内部的默认配置
    TVKSelfPlayerDecodeModeConfigHard, // 硬解
    TVKSelfPlayerDecodeModeConfigSoft, // 软解
};

typedef NS_ENUM(NSUInteger, TVKRenderTypeConfig) {
    TVKRenderTypeConfigDefault, // 使用播放器内部的默认配置
    TVKRenderTypeConfigOpenGL, // opengl
    TVKRenderTypeConfigMetal, // metal
    TVKRenderTypeConfigDisplayLayer,    // 使用DisplayLayer渲染视频
};

typedef NS_ENUM(NSUInteger, TVKGetVInfoEnvConfig) {
    TVKGetVInfoEnvConfigDefault, // 使用播放器内部的默认配置
    TVKGetVInfoEnvConfigNormal, // 正式环境
    TVKGetVInfoEnvConfigTest, // 测试环境
};

typedef NS_ENUM(NSUInteger, TVKSuperResolutionConfig) {
    TVKSuperResolutionConfigDefault, // 使用播放器内部的默认配置
    TVKSuperResolutionConfigNormal, // 正常模式
    TVKSuperResolutionConfigClose, // 关闭超分
};

typedef NS_ENUM(NSUInteger, TVKDebugConfigValueType) {
    TVKDebugConfigValueTypeInt, // config类型为int类型
    TVKDebugConfigValueTypeString, // config类型为NSString类型
};
