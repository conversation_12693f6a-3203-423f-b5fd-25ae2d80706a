/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TVKAudioFrameBuffer.h
 * @brief    音频帧数据定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/12/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/**
 音频格式定义：包含了以下信息：采样位数、整形或浮点型、有符号或无符号、交错模式「interleaved」 或 非交错模式「planar」

 - TPAudioSampleFormatNONE: 未定义
 - TPAudioSampleFormatU8: unsigned 8 bits
 - TPAudioSampleFormatS16: signed 16 bits
 - TPAudioSampleFormatS32: signed 32 bits
 - TPAudioSampleFormatFLT: float 32 bits
 - TPAudioSampleFormatDBL: double 64 bits
 - TPAudioSampleFormatU8P: unsigned 8 bits, planar
 - TPAudioSampleFormatS16P: signed 16 bits, planar
 - TPAudioSampleFormatS32P: signed 32 bits, planar
 - TPAudioSampleFormatFLTP: float 32 bits, planar
 - TPAudioSampleFormatDBLP: double 64 bits, planar
 - TVKAudioSampleFormatS64: signed 64 bits,
 - TVKAudioSampleFormatS64P: signed 64 bits, planar
 */
typedef NS_ENUM(NSInteger, TVKAudioSampleFormat) {
    TVKAudioSampleFormatNONE = -1,
    TVKAudioSampleFormatU8   = 0,
    TVKAudioSampleFormatS16  = 1,
    TVKAudioSampleFormatS32  = 2,
    TVKAudioSampleFormatFLT  = 3,
    TVKAudioSampleFormatDBL  = 4,
    
    TVKAudioSampleFormatU8P  = 5,
    TVKAudioSampleFormatS16P = 6,
    TVKAudioSampleFormatS32P = 7,
    TVKAudioSampleFormatFLTP = 8,
    TVKAudioSampleFormatDBLP = 9,
    TVKAudioSampleFormatS64  = 10,
    TVKAudioSampleFormatS64P = 11
};

/// 音频声道布局
typedef NS_ENUM(NSInteger, TVKAudioChannelLayout) {
    TVKAudioChannelStero        = 0x00000003,
    TVKAudioChannelMono         = 0x00000004,
};

/// 音频帧数据定义
@interface TVKAudioFrameBuffer : NSObject

/**
 音频sample数据
 当数据为非planar时，data[0]存放数据，
 当数据为planar时，data[0] 至 data[channels-1] 存放每个声道的数据
 */
@property (nonatomic, assign) uint8_t **data;

/**
 音频sample数据大小
 当数据为非planar时，size[0]存放数据长度
 当数据为planar时，size[0] 至 size[channels-1] 存放每个声道的size
 */
@property (nonatomic, assign) int *size;

/**
 音频的采样率
 */
@property (nonatomic, assign) unsigned int sampleRate;

/**
 音频的channel layout，见 TPAudioChannelLayout.h 中的定义
 */
@property (nonatomic, assign) uint64_t channelLayout;

/**
 音频格式
 */
@property (nonatomic, assign) TVKAudioSampleFormat format;

/**
 当前sample的PTS
 */
@property (nonatomic, assign) int64_t ptsMs;

/**
 * 每个声道的采样数
 */
@property (nonatomic, assign) int nbSamples;

/**
 * 声道数
 */
@property (nonatomic, assign) int channels;

/**
 额外字段，用于扩展
 */
@property (nonatomic, assign) int flags;



@end


