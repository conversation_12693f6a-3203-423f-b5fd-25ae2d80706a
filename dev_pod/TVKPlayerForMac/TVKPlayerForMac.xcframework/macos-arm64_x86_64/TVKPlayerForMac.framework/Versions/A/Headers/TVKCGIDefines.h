/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKCGIDefines.h
 * @brief    CGI公共类型生声明
 * <AUTHOR>
 * @version  1.0.0
 * @date     2019/9/12
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 注意！！！该文件中定义的所有枚举均对外，千万不要改现有枚举对应的值，这会导致依赖老版本tvk的sdk和依赖新版本tvk的工程一起编译运行后走到错误的逻辑中。
/// 别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!

/// 设备hevc综合能力值key
static const NSString *kSpHevcFps = @"sphevcfps";
/// 参考文档https://tapd.woa.com/qqvideo_prj/markdown_wikis/show/#1210114481001144857
static NSString * const kTVKMediaSceneKey = @"mediascene";
static NSString * const kSpVvcFps = @"spvvcfps";

/// 点播在某一编码格式下  分辨率和帧率对应的能力值（new）https://tapd.woa.com/qqvideo_prj/markdown_wikis/show/#1210114481001146481
/// sphevcfps
typedef NS_OPTIONS(uint64_t, TVKResolutionFpsCapabilityValue) {
    TVKCapabilityValueSD30Fps            = 1ULL << 0, // 支持播放Sd清晰度30帧率视频，下面以此类推
    TVKCapabilityValueHD30Fps            = 1ULL << 1,
    TVKCapabilityValueSHD30Fps           = 1ULL << 2,
    TVKCapabilityValueFHD30Fps           = 1ULL << 3,
    TVKCapabilityValueUHD30Fps           = 1ULL << 4,
    
    TVKCapabilityValueTieFHD30Fps        = 1ULL << 5,
    
    TVKCapabilityValueHDRvividFHD30Fps   = 1ULL << 6,
    TVKCapabilityValueHDRvividUHD30Fps   = 1ULL << 7,
    
    TVKCapabilityValueTieUHD30Fps        = 1ULL << 8,
    
    TVKCapabilityValue10BitFHD30Fps      = 1ULL << 9,
    TVKCapabilityValue10BitUHD30Fps      = 1ULL << 10,

    TVKCapabilityValueSD60Fps            = 1ULL << 11,
    TVKCapabilityValueHD60Fps            = 1ULL << 12,
    TVKCapabilityValueSHD60Fps           = 1ULL << 13,
    TVKCapabilityValueFHD60Fps           = 1ULL << 14,
    TVKCapabilityValueUHD60Fps           = 1ULL << 15,

    TVKCapabilityValueTieFHD60Fps		 = 1ULL << 16,

    TVKCapabilityValueHDRvividFHD60Fps   = 1ULL << 17,
    TVKCapabilityValueHDRvividUHD60Fps   = 1ULL << 18,
    
    TVKCapabilityValueTieUHD60Fps        = 1ULL << 19,
    
    TVKCapabilityValue10BitFHD60Fps      = 1ULL << 20,
    TVKCapabilityValue10BitUHD60Fps      = 1ULL << 21,

    TVKCapabilityValueSD90Fps            = 1ULL << 22,
    TVKCapabilityValueHD90Fps            = 1ULL << 23,
    TVKCapabilityValueSHD90Fps           = 1ULL << 24,
    TVKCapabilityValueFHD90Fps           = 1ULL << 25,
    TVKCapabilityValueUHD90Fps           = 1ULL << 26,

	TVKCapabilityValueTieFHD90Fps		 = 1ULL << 27,

    TVKCapabilityValueHDRvividFHD90Fps   = 1ULL << 28,
    TVKCapabilityValueHDRvividUHD90Fps   = 1ULL << 29,
        
    TVKCapabilityValueTieUHD90Fps        = 1ULL << 30,
    
    TVKCapabilityValue10BitFHD90Fps      = 1ULL << 31,
    TVKCapabilityValue10BitUHD90Fps      = 1ULL << 32,
    
    TVKCapabilityValueSD120Fps           = 1ULL << 33,
    TVKCapabilityValueHD120Fps           = 1ULL << 34,
    TVKCapabilityValueSHD120Fps          = 1ULL << 35,
    TVKCapabilityValueFHD120Fps          = 1ULL << 36,
    TVKCapabilityValueUHD120Fps          = 1ULL << 37,

	TVKCapabilityValueTieFHD120Fps		 = 1ULL << 38,

    TVKCapabilityValueHDRvividFHD120Fps  = 1ULL << 39,
    TVKCapabilityValueHDRvividUHD120Fps  = 1ULL << 40,

    TVKCapabilityValueTieUHD120Fps       = 1ULL << 41,
    
    TVKCapabilityValue10BitFHD120Fps     = 1ULL << 42,
    TVKCapabilityValue10BitUHD120Fps     = 1ULL << 43,
};

// spvvcfps
typedef NS_OPTIONS(uint64_t, TVKSpVvcFpsValue) {
    TVKSpVvcFpsValueSD30Fps            = 1ULL << 0, // 支持播放Sd清晰度30帧率视频，下面以此类推
    TVKSpVvcFpsValueHD30Fps            = 1ULL << 1,
    TVKSpVvcFpsValueSHD30Fps           = 1ULL << 2,
    TVKSpVvcFpsValueFHD30Fps           = 1ULL << 3,
    TVKSpVvcFpsValueUHD30Fps           = 1ULL << 4,
    TVKSpVvcFpsValueTieFHD30Fps        = 1ULL << 5,
    TVKSpVvcFpsValueHDRvividFHD30Fps   = 1ULL << 6,
    TVKSpVvcFpsValueHDRvividUHD30Fps   = 1ULL << 7,
    TVKSpVvcFpsValueTieUHD30Fps        = 1ULL << 8,
    TVKSpVvcFpsValue30FpsPlaceHolder1      = 1ULL << 9, // 预留1
    TVKSpVvcFpsValue30FpsPlaceHolder2      = 1ULL << 10, // 预留2

    TVKSpVvcFpsValueSD60Fps            = 1ULL << 11,
    TVKSpVvcFpsValueHD60Fps            = 1ULL << 12,
    TVKSpVvcFpsValueSHD60Fps           = 1ULL << 13,
    TVKSpVvcFpsValueFHD60Fps           = 1ULL << 14,
    TVKSpVvcFpsValueUHD60Fps           = 1ULL << 15,
    TVKSpVvcFpsValueTieFHD60Fps         = 1ULL << 16,
    TVKSpVvcFpsValueHDRvividFHD60Fps   = 1ULL << 17,
    TVKSpVvcFpsValueHDRvividUHD60Fps   = 1ULL << 18,
    TVKSpVvcFpsValueTieUHD60Fps        = 1ULL << 19,
    TVKSpVvcFpsValue60FpsPlaceHolder1      = 1ULL << 20, // 预留1
    TVKSpVvcFpsValue60FpsPlaceHolder2      = 1ULL << 21, // 预留2

    TVKSpVvcFpsValueSD90Fps            = 1ULL << 22,
    TVKSpVvcFpsValueHD90Fps            = 1ULL << 23,
    TVKSpVvcFpsValueSHD90Fps           = 1ULL << 24,
    TVKSpVvcFpsValueFHD90Fps           = 1ULL << 25,
    TVKSpVvcFpsValueUHD90Fps           = 1ULL << 26,
    TVKSpVvcFpsValueTieFHD90Fps         = 1ULL << 27,
    TVKSpVvcFpsValueHDRvividFHD90Fps   = 1ULL << 28,
    TVKSpVvcFpsValueHDRvividUHD90Fps   = 1ULL << 29,
    TVKSpVvcFpsValueTieUHD90Fps        = 1ULL << 30,
    TVKSpVvcFpsValue90FpsPlaceHolder1      = 1ULL << 31, // 预留1
    TVKSpVvcFpsValue90FpsPlaceHolder2      = 1ULL << 32, // 预留2
    
    TVKSpVvcFpsValueSD120Fps           = 1ULL << 33,
    TVKSpVvcFpsValueHD120Fps           = 1ULL << 34,
    TVKSpVvcFpsValueSHD120Fps          = 1ULL << 35,
    TVKSpVvcFpsValueFHD120Fps          = 1ULL << 36,
    TVKSpVvcFpsValueUHD120Fps          = 1ULL << 37,
    TVKSpVvcFpsValueTieFHD120Fps         = 1ULL << 38,
    TVKSpVvcFpsValueHDRvividFHD120Fps  = 1ULL << 39,
    TVKSpVvcFpsValueHDRvividUHD120Fps  = 1ULL << 40,
    TVKSpVvcFpsValueTieUHD120Fps       = 1ULL << 41,
    TVKSpVvcFpsValue120FpsPlaceHolder1     = 1ULL << 42, // 预留1
    TVKSpVvcFpsValue120FpsPlaceHolder2     = 1ULL << 43, // 预留2
};

// 视频下载类型，熟知跟后台一致
typedef NS_ENUM(NSUInteger, TVKMediaDLType) {
    TVKMediaDLTypeAuto      = 0,
    TVKMediaDLTypeHttp      = 1,
    TVKMediaDLTypeP2P       = 2,
    TVKMediaDLTypeHLS       = 3,
    TVKMediaDLTypeHLSM3U8   = 8,  // M3U8直出
};

static const int kDefinitionSrcAutoReduce = 10;  // 自动降清晰度
static const int kDefinitionSrcCastAuto   = 5;   // 投射默认清晰度
// TVM超分算法类型，服务端定义，0:不支持，1:支持TVM超分
static const NSInteger kSuperResolutionTypeTVM = 1;

typedef NS_ENUM(NSUInteger, TVKMediaPlayBizType) {
    TVKMediaPlayBizTypeVod,
    TVKMediaPlayBizTypeLive,
};

/// 点播编码类型
typedef NS_ENUM(NSInteger, TVKVODEncodeType) {
    TVKVODEncodeTypeDefault    = 0,  //普通视频、普通音频
    TVKVODEncodeTypeDolbyAudio = 1,  //普通视频、dolby音频
};

/// TODO(ahailiu, 2024/6/18)
/// 点播与直播复用同一套定义，但是由于业务持续迭代导致枚举值不一致了,
/// 点直播请求和返回字段都差异较大，理论上也不应该某些字段或者字段值复用同一套定义，
/// 最终会导致，某些复用，又有些区分
/// 所以现需分离TVKVideoType点直播的枚举值定义，由于TVKVideoType主要以点播为主，
/// 所以新增TVKLiveVideoType，而理论上应该修改为TVKVodVideoType
/// 但修改较多，先保留原状
typedef NS_ENUM(NSUInteger, TVKVideoType) {
    TVKVideoTypeH264        = 1,
    TVKVideoTypeH265        = 2,
    TVKVideoTypeHDR10       = 3,
    TVKVideoTypeDolbyVision = 4,
    /// 纯音频码流
    TVKVideoTypeAudio       = 5,
    /// SDR+
    TVKVideoTypeSDRPlus     = 6,
    /// SDR
    TVKVideoTypeSDR         = 7,
    /// t265
    TVKVideoTypeT265        = 8,
    /// 臻彩视听Hdr2倍fps,一般为60
    TVKVideoTypeSFR2xHDR10  = 10,
    /// 臻彩视听Hdr4倍fps，一般为120
    TVKVideoTypeSFR4xHDR10  = 11,
    /// AVS3编码
    TVKVideoTypeAVS3        = 13,
    /// tie2编码臻彩视听(旧)
    TVKVideoTypeTIEV2       = 15,
    /// 臻彩视听，Hdr vivid
    TVKVideoTypeHDRVivid    = 19,
    /// VVC(o266)
    TVKVideoTypeVVC         = 20,
    /// o264
    TVKVideoTypeO264        = 21,
    /// tie4编码臻彩视听(新)
    TVKVideoTypeTIEV4       = 23,
    /// VAV1
    TVKVideoTypeAV1         = 26,
    /// 高帧率hdrvivid
    TVKVideoTypeSFRHDRVivid = 27,
    /// mv-hevc (3d)
    TVKVideoTypeMvHevc      = 28,
    /// vvc tie
    TVKVideoTypeO266TIEV4 = 38,
    /// vvc vivid
    TVKVideoTypeO266Vivid = 39,
};

typedef NS_ENUM(NSUInteger, TVKLiveVideoType) {
    // AVC
    TVKLiveVideoTypeH264 = 1,
    // HEVC
    TVKLiveVideoTypeH265 = 2,
    // VVC
    TVKLiveVideoTypeH266 = 3,
};

typedef NS_ENUM(NSUInteger, TVKVODAudioType) {
    TVKVODAudioTypeAAC = 1,
    TVKVODAudioTypeDolbySurround = 2,
    TVKVODAudioTypeDolbyAtmos = 3,
    TVKVODAudioTypeDolby2_0 = 4,
    TVKVODAudioTypeAAC5_1 = 8,
    TVKVODAudioTypeAudioVivid5_1_4 = 9,
    TVKVODAudioTypeFlac = 11,
};

typedef NS_ENUM(NSUInteger, TVKLiveAudioType) {
    TVKLiveAudioTypeAAC   = 1,
    TVKLiveAudioTypeDolby = 2,
};

/// 直播色彩类型
typedef NS_ENUM(NSUInteger, TVKLiveHeightenType) {
    TVKLiveHeightenTypeDefault = 0,
    // 杜比
    TVKLiveHeightenTypeDolby = 2,
    // hdr vivid
    TVKLiveHeightenTypeHDRVivid = 4,
};
typedef NS_ENUM(NSUInteger, TVKVODVideoState) {
    TVKVODVideoStatePlayable   = 2,  // 只有等于2的时候为可播状态
    TVKVODVideoStateNeedCharge = 8,  // 等于8的时候为收费状态，是试看
};

typedef NS_ENUM(NSUInteger, TVKLimitType) {
    TVKLimitTypePiracy      = 1,  // 防盗链限制
    TVKLimitTypeOuter       = 2,  // 站外限时
    TVKLimitTypeDefnPreview = 3,  // 清晰度试看
    TVKLimitTypeDefnPreviewNew = 4,  // 新的清晰度中间试看
};

typedef NS_ENUM(NSUInteger, TVKDrmType) {
    TVKDrmTypeNone     = 0, //非加密
    TVKDrmTypeChaCha20  = 3, //自研加密，chacha20
    TVKDrmTypeFairPlay = 4, //fair play
};

/// 点播的VVC能力值
typedef NS_ENUM(NSUInteger, TVKVVCLevel) {
    TVKVVCLevelUnSupported = 0,
    TVKVVCLevelSD   = 0x1,
    TVKVVCLevelHD   = 0x02,
    TVKVVCLevelSHD  = 0x04,
    TVKVVCLevelFHD  = 0x08,
};

/// 直播VVC能力值
typedef NS_ENUM(NSUInteger, TVKLiveVVCLevel) {
    TVKLiveVVCLevelUnSupported = 0,
    TVKLiveVVCLevelSDFps30   = 0x1,
    TVKLiveVVCLevelSDFps60   = 0x2,
    TVKLiveVVCLevelHDFps30   = 0x4,
    TVKLiveVVCLevelHDFps60   = 0x8,
    TVKLiveVVCLevelSHDFps30  = 0x10,
    TVKLiveVVCLevelSHDFps60  = 0x20,
    TVKLiveVVCLevelFHDFps30  = 0x40,
    TVKLiveVVCLevelFHDFps60  = 0x80,
};

/// 支持的AV1能力值
typedef NS_ENUM(NSUInteger, TVKAV1Level) {
    TVKAV1LevelUnSupported = 0,
    TVKAV1LevelSD   = 1,       // 480 * 270
    TVKAV1LevelHD   = 2,       // 848 * 480
    TVKAV1LevelSHD  = 4,       // 1280 * 720
    TVKAV1LevelFHD  = 8,       // 1920 * 1080
};

/// 支持的AVS3能力值
typedef NS_ENUM(NSUInteger, TVKAVS3Level) {
    TVKAVS3LevelUnSupported = 0,
    TVKAVS3LevelSD   = 1,       // 480 * 270
    TVKAVS3LevelHD   = 2,       // 848 * 480
    TVKAVS3LevelSHD  = 4,       // 1280 * 720
    TVKAVS3LevelFHD  = 8,       // 1920 * 1080
};

/// 中插广告的能力值
typedef NS_ENUM(NSUInteger, TVKAdCapability) {
    TVKAdCapabilityVideoIn      = 1, /// 植入广告的能力
    TVKAdCapabilityVideoPlugin  = 2, /// 插入广告的能力
};

/// CGI参数spvfps定义值
/// 传最高能支持的视频帧率，如：30,60,120
/// 参考http://tapd.oa.com/qqvideo_prj/markdown_wikis/show/#1210114481001146481
typedef NS_ENUM(NSUInteger, TVKVideoFPS) {
    TVKVideoFPS0 = 0,       //默认
    TVKVideoFPS30 = 30,     //最高支持30
    TVKVideoFPS60 = 60,     //最高支持60
    TVKVideoFPS120 = 120,   //最高支持120
};

/// CGI参数spsfrhdr定义值
/// 100:臻彩视听30帧
/// 200:臻彩视听60帧
/// 250:臻彩视听90帧
/// 300:臻彩视听120帧
/// 参考http://tapd.oa.com/qqvideo_prj/markdown_wikis/show/#1210114481001146481
typedef NS_ENUM(NSUInteger, TVKHdrFPS) {
    TVKHdrFPS0 = 0,       //默认
    TVKHdrFPS30 = 100,     //最高支持30
    TVKHdrFPS60 = 200,     //最高支持60
    TVKHdrFPS90 = 250,     //最高支持90
    TVKHdrFPS120 = 300,   //最高支持120
};

/// fps能力值定义
typedef NS_ENUM(NSUInteger, TVKCapabilityFPS) {
    TVKCapabilityFPS0 = 0,
    TVKCapabilityFPS30 = 30,
    TVKCapabilityFPS60 = 60,
    TVKCapabilityFPS90 = 90,
    TVKCapabilityFPS120 = 120,
};

/// 支持的视频能力
typedef NS_ENUM(NSUInteger, TVKVideoCapability) {
    TVKVideoCapabilityHDR10            = 0x4,    // 支持HDR10
    TVKVideoCapabilityDolbyVision      = 0x8,    // 支持FFMP4 DolbyVision（旧属性，等同于TVKVideoCapabilityFFMP4DolbyVision，请用下面新定义的枚举值）
    TVKVideoCapabilityFFMP4DolbyVision = 0x10,   // 支持FFMP4的Dolby Vision
    TVKVideoCapabilitySDRPlus          = 0x20,   // 支持SDR+
    TVKVideoCapabilityTSDolbyVision    = 0x40,   // 支持TS分片的Dolby Vision
    TVKVideoCapabilityHDR10Enhance     = 0x80,   // 支持HDR10增强
    TVKVideoCapabilityIMAX             = 0x100,  // 支持IMAX
    TVKVideoCapabilityHDRVivid         = 0x400,  // 支持hdrvivi
    TVKVideoCapabilityMvHevc           = 0x800,  // 支持mv-hevc (3d)
};

/// 支持4K+hdrvivid+高帧率能力
typedef NS_ENUM(NSUInteger, TVKUhdHdrVividFpsCapability) {
    TVKUhdHdrVividFps30Capability      = 100,    // 支持UHD HDRVivid 30帧
    TVKUhdHdrVividFps60Capability      = 200,    // 支持UHD HDRVivid 60帧
};

/// 支持的音频能力
typedef NS_ENUM(NSUInteger, TVKAudioCapability) {
    TVKAudioCapabilityAudioOnly     = 0x1,   // 支持纯音频
    TVKAudioCapabilityDolbySurround = 0x2,   // 支持支持杜比环绕声
    TVKAudioCapabilityDolbyAtoms    = 0x4,   // 支持FFMP4的Dolby Vision
    TVKAudioCapabilityDolby2_0      = 0x8,   // 支持SDR+
    TVKAudioCapabilityDTSHD         = 0x10,  // 支持TS分片的Dolby Vision
    TVKAudioCapabilityDTSX          = 0x20,  // 支持HDR10增强
    TVKAudioCapabilityAAC5_1        = 0x80,  // 支持AAC5.1
    TVKAudioCapabilityAudioVivid5_1_4 = 0x100,  // 支持audio vivid 5.1.4
    TVKAudioCapabilityFlac          = 0x400,  // 支持flac
};

/// 支持的杜比软硬解码能力
typedef NS_ENUM(NSUInteger, TVKDolbyDecodeCapability) {
    TVKDolbySoftRenderDolbyVision   = 0x1,   // 支持Dolby Vision视频软渲染
    TVKDolbyHardDecodeDolbyVision   = 0x2,   // 支持Dolby Vision视频硬解码
    TVKDolbySoftDecodeDolbySurround = 0x4,   // 支持Dolby Surround音频软解码
    TVKDolbyHardDecodeDolbySurround = 0x8,   // 支持Dolby Surround音频硬解码
    TVKDolbySoftDecodeDolbyAtoms    = 0x10,   // 支持Dolby Atoms音频软解码
    TVKDolbyHardDecodeDolbyAtoms    = 0x20,   // 支持Dolby Atoms音频硬解码
};

/// 支持的软水印能力
typedef NS_ENUM(NSUInteger, TVKWaterMarkCapability) {
    TVKWaterMarkCapabilityStaticNone = 0,  // 不支持
    TVKWaterMarkCapabilityStatic     = 1,  // 静态水印
    TVKWaterMarkCapabilityAction     = 2,  // 动态水印
};

/// 支持的多音轨能力
typedef NS_ENUM(NSUInteger, TVKMultiAudioTrackCapability) {
    // 不支持
    TVKMultiAudioTrackCapabilityNone                   = 0,
    // 支持后台在ai节点下下发原始音轨（即合流音轨）的信息. 如果当前请求返回了一个以上的外挂音轨，则返回track=default的原音轨，原音轨的ul为空
    TVKMultiAudioTrackCapabilityProvideInternalTrack   = 0x4,
    // 支持下发所有音轨地址
    TVKMultiAudioTrackCapabilityProvideAllUrls         = 0x8,
    // 支持chacha20下发多音轨
    TVKMultiAudioTrackCapabilityMultiTrackOnChacha20   = 0x10,
    // 不支持多音轨，下发合流轨信息
    TVKMultiAudioTrackCapabilityOnlyInternalTrack      = 0x80,
};

/// 富媒体的支持能力
typedef NS_ENUM(NSUInteger, TVKRichMediaCapability) {
    TVKRichMediaCapabilityDisable   = 0, //不支持
    TVKRichMediaCapabilityEnable    = 1, //phone端默认支持全部富媒体能力
};

/// 支持的加密能力
typedef NS_ENUM(NSUInteger, TVKDRMCapability) {
    TVKDRMCapabilityOrdinary    = 0x1,   // 支持普通DRM方案
    TVKDRMCapabilityFakeDRM     = 0x2,   // 支持伪DRM
    TVKDRMCapabilityUnitEnd     = 0x4,   // 支持数字太和DRM
    TVKDRMCapabilityHLSEncryptChaCha20  = 0x8,   // 支持HLS加密
    TVKDRMCapabilityFairplay    = 0x10,  // 支持Fairplay加密
    TVKDRMCapabilityWidevine    = 0x20,  // 支持Widevine加密
};

/// multi-drm能力值
typedef NS_ENUM(NSUInteger, TVKMultiDRMCapability) {
    TVKMultiDRMCapabilityNone = 0,
    TVKMultiDRMCapabilityFairplay = 0x10,
};

/// 清晰度付费能力
typedef NS_ENUM(NSUInteger, TVKDefnPayVer) {
    TVKDefnPayVer1080P       = 0x1,  // 支持1080P付费
    TVKDefnPayVer4K          = 0x2,  // 支持4K付费
    TVKDefnPayVerDolbyVision = 0x4,  // 支持杜比付费
};

/// hls相关能力
typedef NS_ENUM(NSUInteger, TVKHlsCapability) {
    TVKHlsCapabilityM3u8          = 0x2,  // 支持m3u8直出
    TVKHlsCapabilityExternalSubtitleTrackM3u8 = 0x4,  // 支持外挂字幕的m3u8直出
    TVKHlsCapabilityExternalAudioTrackM3u8 = 0x8,  // 支持外挂音轨的m3u8直出
};

/// 软字幕版本
typedef NS_ENUM(NSUInteger, TVKSRTCapability) {
    /// 不支持软字幕
    TVKSRTCapabilityNone = 0,
    /// 如果视频有硬字幕，则不下发软字幕
    TVKSRTCapabilityMutex = 1,
    /// 不管是否有硬字幕，都下发软字幕
    TVKSRTCapabilityNonMutex = 3,
};


/// 支持字幕分离能力值
typedef NS_ENUM(NSUInteger, TVKSubtitleSeperateCapability) {
    /// 不支持字幕分离
    TVKSubtitleCaptionTrackUnsupportMultiSeparate = 0,
    /// 支持字幕分离
    TVKSubtitleCaptionTrackSupportMultiSeparate = 1
};

/// 客户端防录屏能力值
typedef NS_ENUM(NSUInteger, TVKScreenCaptureLevel) {
    TVKScreenCaptureLevelNone     = 0,  // 客户端不支持
    TVKScreenCaptureLevelSupport  = 1,  // 如果视频有硬字幕，则不下发软字幕
};

typedef NS_ENUM(NSUInteger, TVKCGILoginType) {
    TVKCGILoginTypeNone = 0,  // 未登录
    TVKCGILoginTypeQQ   = 1,  // QQ
    TVKCGILoginTypeWx   = 2,  // 微信
};

typedef NS_ENUM(NSUInteger, TVKCGINetType) {
    TVKCGINetTypeNone = 0,
    TVKCGINetTypeWifi = 1,
    TVKCGINetType2G   = 2,
    TVKCGINetType3G   = 3,
    TVKCGINetType4G   = 4,
    TVKCGINetType5G   = 5,
};

typedef NS_ENUM(NSUInteger, TVKCGIIPStack) {
    TVKCGIIPStackUnknow = 0,
    TVKCGIIPStackIPV4 = 1,
    TVKCGIIPStackIPV6 = 2,
    TVKCGIIPStackDual = 3,
};

/// getvinfo请求类型
typedef NS_ENUM(NSUInteger, TVKGetVInfoRequestType) {
    TVKGetVInfoRequestTypeOnline        = 0,  // 在线播放（会尝试读取CGI缓存）
    TVKGetVInfoRequestTypeOfflinePlay   = 1,  // 离线播放
    TVKGetVInfoRequestTypeDownload      = 2,  // 离线下载
};

/// cgi参数 dtype定义
extern NSString *const TVKCGIDTypeHls;
extern NSString *const TVKCGIDTypeMp4;

/// cgi参数 clip定义
extern NSString *const TVKCGIClipDefault;   //默认
extern NSString *const TVKCGIClipAuto;      //后台指定
extern NSString *const TVKCGIClipMulti;     //5分钟分片Mp4
extern NSString *const TVKCGIClipOne;       //整段Mp4

/// cgi参数 encryption字段值定义
extern NSString *const TVKCGIEncryptionNone; // 无加密
extern NSString *const TVKCGIEncryptionHigh; // 强加密
extern NSString *const TVKCGIEncryptionLow;  // 弱加密

/// getvinfo 返回的sshot定义
typedef NS_ENUM(NSInteger, TVKCGISShot) {
    TVKCGISShotAppLogic         = 0,    // 使用 app 逻辑判断
    TVKCGISShotForbidden        = 1,    // 不可截屏/录屏
    TVKCGISShotSystemForbidden  = 2,    // 系统不可但 app 可截屏/录屏
    TVKCGISShotAll              = 3,    // 系统和 app 均可截屏/录屏
};

/// cgi请求类型 
typedef NS_ENUM(NSUInteger, TVKCGIRequestType) {
    TVKCGIRequestTypeNormal                 = 0,  // 正常播放cgi请求
    TVKCGIRequestTypeSwitchDefnSeamless     = 1,  // 无缝切换清晰度
    TVKCGIRequestTypeSwitchDefnReOpen       = 2,  // 非无缝切换清晰度
    TVKCGIRequestTypeSwitchAudioTrack       = 3,  // 切换音轨
    TVKCGIRequestTypeSwitchAudioTrackReOpen = 4,  // 重启播放器播放合成流的方式切换音轨
    TVKCGIRequestTypeHighRail               = 5,  // 高铁模式
    TVKCGIRequestTypeErrorRetry             = 6,  // 错误
    TVKCGIRequestTypeLiveSeekBack           = 7,  // 直播回看
    TVKCGIRequestTypeVKeyExpire             = 8,  // VKEY过期
    TVKCGIRequestTypeOfflineDownload        = 10,  // 离线下载
    TVKCGIRequestTypeNoMoreData             = 11, // NoMoreData，CGI不关心
    TVKCGIRequestTypeRefreshPlayerWithReopen          = 12, // 刷新播放器
    TVKCGIRequestTypeXmlAssetGetter         = 13, // 腾讯视频URL直出，外部直接传递一个xml
    TVKCGIRequestTypeAdaptiveSwitchDefn     = 14, // 自适应切换清晰度，无缝切换
    TVKCGIRequestTypeRefreshPlayer          = 15, // 无缝刷新播放器
};

/// 降低直播延时的策略
typedef NS_ENUM(NSInteger,  TVKLiveReduceLatencyAction) {
    // 不采取任何措施
    TVKLiveReduceLatencyActionNone = 0,
    // 采用加速播放降低延迟
    TVKLiveReduceLatencyActionSpeedUp = 1,
    // 采用跳帧降低延迟 (功能暂未实现)
    TVKLiveReduceLatencyActionSkipFrame = 2
};

typedef NS_ENUM(NSUInteger, TVKCGIReportEvent) {
    // 未知事件
    TVKCGIReportEventUnknown                      = 0,
    // http发送事件
    TVKCGIReportEventHttpSend                     = 1,
    // 收到http response事件
    TVKCGIReportEventHttpResponseRectived         = 2,
    // xml解析完成事件
    TVKCGIReportEventParseDataDone                = 3,
    // xml数据回抛事件
    TVKCGIReportEventParseDataResponse            = 4,
    // cgi线程转播放线程事件
    TVKCGIReporteventCGIThreadToPlayerThread      = 5,
};

/// master m3u8能力支持。详细见 https://iwiki.woa.com/pages/viewpage.action?pageId=813555542 中的定义
typedef NS_OPTIONS(NSInteger, TVKMasterM3u8Capability) {
    /// 不支持
    TVKMasterM3u8CapabilityNone = 0x00,
    /// 0x01: 支持二级m3u8直出(video和audio的m3u8直出)
    TVKMasterM3u8CapabilityMultiLayer = 0x01,
    /// 0x02: 返回master m3u8的代理地址
    TVKMasterM3u8CapabilityMasterUrl = 0x02,
    /// 0x10: 支持avs清流的调度返回，只返回单字幕单音轨，但vid存在dolby音频时，会返回dolby音轨和普通音轨
    /// （可以组合使用，默认没有二级直出，只是控制调度）
    TVKMasterM3u8CapabilitySingleSubtitleAudio = 0x10,
    /// 0x20:支持avs请求的调度返回，返回多字幕多音轨
    TVKMasterM3u8CapabilityMultiSubtitleAudio = 0x20
};

/// getvinfo   previd类型
typedef NS_ENUM(NSInteger, TVKCGIPrevidType) {
    TVKCGIPrevidTypeVod          = 0,    // 点播的秒播
    TVKCGIPrevidTypeSimulatedLive        = 1,    // 伪直播
};

/// medialabvr的支持能力
typedef NS_ENUM(NSUInteger, TVKMedialabVRCapability) {
    TVKMedialabVRCapabilityNone   = 0, //不支持
    TVKMedialabVRCapabilityEnable    = 1, //支持
};

/// 播放器特殊控制
typedef NS_ENUM(NSInteger, TVKCGIPlayCtrl) {
    /// 仅下发hls格式的清晰度
    TVKCGIPlayCtrlOnlyHls = 0x01,
    /// 支持下发4k清晰度,  不带这个值后台就不下发4k清晰度
    TVKCGIPlayCtrlEnableZhencaiUhdDefinition = 0x02,
    /// mac端支持下发tie(1080p + 4k), 忽略hevclv控制逻辑
    TVKCGIPlayCtrlEnableTieDefinition = 0x04,
    /// 是否把4k sdr从臻彩4k清晰度里分离出来作为单独清晰度，不带这个值 4k sdr属于臻彩4k清晰度档
    TVKCGIPlayCtrlEnableSdrUhdDefinition = 0x10,
};

/// 标版广告特殊控制，bitset
typedef NS_ENUM(NSInteger, TVKCGIPluginAdCtrl) {
    /// 支持高光标版广告，不带这个值则表示不支持
    TVKCGIPluginAdCtrlHightlight = 0x01,
    /// 根据adsid和adpinfo下发广告，不管是否是切换清晰度
    TVKCGIPluginAdCtrlAdConsistency = 0x02,
};

/// CGI事件上报参数
@interface TVKCGIReportEventParam : NSObject
/// 事件类型
@property (nonatomic, assign) TVKCGIReportEvent event;
/// 发生时间，从UTC1970-1-1 0:0:0开始计时，单位毫秒
@property (nonatomic, assign) int64_t timeSince1970Ms;
/// 发生时间，从设备第一次启动开始计时，单位毫秒
@property (nonatomic, assign) int64_t timeSinceBootMs;
@end
