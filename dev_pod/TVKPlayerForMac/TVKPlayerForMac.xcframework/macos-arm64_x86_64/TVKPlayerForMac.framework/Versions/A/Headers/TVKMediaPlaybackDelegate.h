/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKMediaPlaybackDelegate.h
 Author      : chen
 Version     : 1.0
 Date        : 14-11-4
 Description : 播放器delegate
 History     : 14-11-4 初始版本
 ***********************************************************/
#if TARGET_OS_OSX
#import <AppKit/AppKit.h>
#else
#import <UIKit/UIKit.h>
#endif

#import "TVKPlayerDefine.h"
#import "TVKReportEvent.h"
#import "TVKAudioFrameBuffer.h"
#import "TVKVideoFrameBuffer.h"
#import "ITVKSubtitleData.h"

@class TVKMediaPlayer;
@class TVKNetVideoInfo;
@class TVKUserInfo;
@class TVKNetAdInfo;
@class TVKNetMediaDefinitionInfo;
@class TVKPlayerEventParams;
@protocol ITVKMediaPlayer;
@class TVKReportEventParams;

#pragma mark 播放器播放正片相关状态和事件
@protocol TVKMediaPlaybackDelegate <NSObject>

@optional

/**
 *  @brief 播放器播放正片状态变化
 *
 *  @param mediaPlayer 播放器实例
 *  @param state 状态
 *  @param error 播放失败时的错误信息,
 *               error.code: 错误码,
 *               error.userInfo: 错误详情,使用kev-value携带额外信息，包括modelId,errMsg,exErrorCode, exErrMsg,
 * data，key的定义请见TVKPlayerDefine.h，其中modelId=平台号+模块号
 *               示例：
 *                  error.code=1361
 *                  userInfo[@"modelId"]: 20101
 *                  userInfo[@"errMsg"]: @"vid is invalid"
 *                  userInfo[@"exErrorCode"]: 1
 *                  userInfo[@"exErrorMsg"]: @"vid format is wrong"
 *                  userInfo[@"data"]: 如果是直播cgi错误，则携带的是netVideoInfo，其他情况为nil
 */
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer stateChanged:(TVKMediaPlayerState)state withError:(NSError *)error;

/**
 *  @brief 播放器播放事件变化，主要是指缓冲、拖动以及切换清晰度事件，具体参看TVKMediaPlayerEvent
 *
 *  @param mediaPlayer 播放器实例
 *  @param event 事件，参考TVKMediaPlayerEvent
 *  @param eventInfo 事件相关的扩展信息。详情请见TVKMediaPlayerEvent定义
 */
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer eventChanged:(TVKMediaPlayerEvent)event withExtraInfo:(id)eventInfo;

/**
 *  @brief 后台返回的正片视频信息，包括当前清晰度，清晰度列表等信息
 *
 *  @param mediaPlayer 播放器实例
 *  @param netVideoInfo 正片视频信息
 */
// NOLINTNEXTLINE
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer NetVideoInfo:(TVKNetVideoInfo *)netVideoInfo;

/**
 * @brief 播放进度通知
 * @param position 当前播放位置
 * @param playablePosition 可播位置，即缓冲时长
 */
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer currentPosition:(NSTimeInterval)position playablePosition:(NSTimeInterval)playablePosition;

/**
 *  @brief 正片视频的宽和高
 *  @param mediaPlayer 播放器实例
 *  @param width       正片视频的宽
 *  @param height      正片视频的高
 */
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer videoSizeChangedWithWidth:(NSInteger)width height:(NSInteger)height;

/* --------------------以下是几个个方法是AirPlay回调-------------------- */
/**
 *  @brief airplay激活或关闭的通知，已不建议使用
 *  @param mediaPlayer 播放器实例
 *  @param airPlayState YES为激活，NO为关闭
 */
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer airPlayDidActive:(BOOL)airPlayState;

/**
 *  @brief airplay投射停止
 *  @param mediaPlayer 播放器实例
 *  @error 错误信息
 */
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer airPlayError:(NSError *)error;

/* --------------------以下是几个个方法是画中画回调-------------------- */
/**
 * @brief 画中画即将开始
 * @param mediaPlayer 播放器实例
 */
- (void)mediaPlayerPictureInPictureWillStart:(TVKMediaPlayer *)mediaPlayer;

/**
 * @brief 画中画已经开始
 * @param mediaPlayer 播放器实例
 */
- (void)mediaPlayerPictureInPictureDidStart:(TVKMediaPlayer *)mediaPlayer;

/**
 * @brief 画中画启动失败
 * @param mediaPlayer 播放器实例
 * @param error 错误信息
 */
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer failToStartPictureInPictureWithError:(NSError *)error;

/**
 * @brief 画中画即将停止
 * @param mediaPlayer 播放器实例
 */
- (void)mediaPlayerPictureInPictureWillStop:(TVKMediaPlayer *)mediaPlayer;

/**
 * @brief 画中画已经停止
 * @param mediaPlayer 播放器实例
 */
- (void)mediaPlayerPictureInPictureDidStop:(TVKMediaPlayer *)mediaPlayer;

/**
 *  @brief 从画中画恢复到正常播放，调用方完成UI上的恢复操作后，需要调用
 *  @param mediaPlayer 播放器实例
 *  @param completionHandler 恢复之后需要调用该completionHandler告知播放器
 */
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer restoreForPictureInPictureWithCompletionHandler:(void (^)(BOOL restored))completionHandler;

/// @brief 是否可以获得音频帧回调。
/// 内部使用自研播放器时isAvailable为YES，使用系统播放器时isAvailable为NO
/// 回调时机：起播调用openMediaPlayerWithMediaInfo后，和内部切换到系统播放器时。
/// @param mediaPlayer 播放器实例
/// @param isAvailable 是否可获得音频帧回调
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer audioFrameOutAvailable: (BOOL)isAvailable;

/// @brief 音频帧回调
/// 回调的音频数据和内部播放音频使用的数据是同一份，外部对音频数据的改动会影响到音频的播放
/// 该接口为同步接口，因为音频帧数据必须按照时序。
/// 如果要在该回调中对音频数据处理，注意不能做耗时操作，会阻塞解码线程。
/// @param mediaPlayer 播放器实例
/// @param audioFrameBuffer 音频帧数据
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer audioFrameOut:(TVKAudioFrameBuffer *)audioFrameBuffer;

/// @brief 是否可以获得视频帧回调。
/// 一些情况下，视频帧数据无法回调（比如加密视频），此时isAvailable为NO。如果能回调视频帧数据，则isAvailable为YES。
/// 回调时机：起播调用openMediaPlayerWithMediaInfo后，以及切换清晰度（比如切换到杜比）等条件变化后，无法继续或者可以恢复回调数据的情况。
/// @param mediaPlayer 播放器实例
/// @param isAvailable 是否可获得视频帧回调
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer videoFrameOutAvailable: (BOOL)isAvailable;

/// @brief 视频帧回调
/// 该接口为同步接口，因为视频帧数据必须按照时序。
/// 如果要在该回调中对视频数据处理，注意不能做耗时操作，会阻塞解码线程。
/// @param mediaPlayer 播放器实例
/// @param videoFrameBuffer 视频帧数据
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer videoFrameOut:(TVKVideoFrameBuffer *)videoFrameBuffer;

#if TARGET_OS_VISION
/// @brief 字幕数据回调
/// @param mediaPlayer 播放器实例
/// @param subtitleData 字幕数据
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer subtitleData:(id<ITVKSubtitleData>)subtitleData;
#endif

@end

#pragma mark 播放器播放广告状态和事件，如果合作形式有广告，则下列代理必须实现，否则可能无法进行播放

@protocol TVKAdPlayDelegate <NSObject>

#pragma mark 播放器播放前贴片广告时相关状态变化和事件

/**
 *  @brief 前贴片广告状态变化. 前贴片处于TVKMediaPlayerAdStatePrepared之后的状态，adDuration是有合法值.
 *  @param mediaPlayer 播放器实例
 *  @param state 前贴片广告状态
 *  @param adDuration 前贴片广告时长
 */
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer preAdStateChanged:(TVKMediaPlayerAdState)state withAdDuration:(NSTimeInterval)adDuration;

/**
 *  @brief 后台返回的广告视频信息。注意:现只在前贴片和后贴片有回调，其他类型广告暂无返回。
 *
 *  @param mediaPlayer 播放器实例
 *  @param netAdInfo 广告信息.现在返回的netAdinfo为nil,无有效信息返回，仅用于app获取广告信息的事件通知
 */
// NOLINTNEXTLINE
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer netAdInfo:(TVKNetAdInfo *)netAdInfo;

#pragma mark 播放器播放后贴片广告时相关状态变化和事件

/**
 *  @brief 后贴片广告状态变化.后贴片处于TVKMediaPlayerAdStatePrepared之后的状态，adDuration是有合法值.
 *  @param mediaPlayer 播放器实例
 *  @param state 后贴片广告状态
 *  @param adDuration 后贴片广告时长
 */
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer postRollAdStateChanged:(TVKMediaPlayerAdState)state withAdDuration:(NSTimeInterval)adDuration;

#pragma mark 播放器播放中插广告时相关状态变化和事件

/**
 *  @brief 中插广告状态变化.状态处于TVKMediaPlayerAdStatePrepared之后的状态，adDuration是有合法值
 *  @param mediaPlayer 播放器实例
 *  @param state 中插广告状态
 *  @param adDuration 中插广告总时长
 */
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer midAdStateChanged:(TVKMediaPlayerAdState)state withAdDuration:(NSTimeInterval)adDuration;

/**
 *  @brief 中插广告倒计时开始
 *  @param mediaPlayer 播放器实例
 *  @param timeCountView 倒计时view. app可以根据视图，放置到合适位置
 */
#if TARGET_OS_OSX
- (void)onMidAdCountDownStart:(TVKMediaPlayer *)mediaPlayer timeCountView:(NSView *)timeCountView;
#else
- (void)onMidAdCountDownStart:(TVKMediaPlayer *)mediaPlayer timeCountView:(UIView *)timeCountView;
#endif
/**
 *  @brief 中插广告倒计时结束
 *  @param mediaPlayer 播放器实例
 *  @param  timeCountView 倒计时view. 回调此接口后，即使app不remove view, 内部也会从父view中移除timeCountView.
 */
#if TARGET_OS_OSX
- (void)onMidAdCountDownEnd:(TVKMediaPlayer *)mediaPlayer timeCountView:(NSView *)timeCountView;
#else
- (void)onMidAdCountDownEnd:(TVKMediaPlayer *)mediaPlayer timeCountView:(UIView *)timeCountView;
#endif

@end

#pragma mark 截图Delegate
@protocol TVKCaptureDelegate <NSObject>

/**
 * @brief 截图回调
 * @param mediaPlayer 播放器实例
 * @param captureId 调用播放器截图接口时返回的id
 * @param width 图像宽
 * @param height 图像高
 * @param image 截取的图像
 */
#if TARGET_OS_OSX
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer captureWithId:(int)captureId width:(CGFloat)width height:(CGFloat)height image:(NSImage *)image;
#else
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer captureWithId:(int)captureId width:(CGFloat)width height:(CGFloat)height image:(UIImage *)image;
#endif
@end

#pragma mark 播放权限相关通知
@protocol TVKPermissionDelegate <NSObject>

/**
 * @brief 会员视频或者付费视频试看时间到
 * @param mediaPlayer 播放器实例
 */
- (void)permissionTimeoutWithMediaPlayer:(TVKMediaPlayer *)mediaPlayer;

@end

#pragma mark 播放细分事件通知
@protocol TVKPlayerEventDelegate <NSObject>

/**
 *  @brief 播放器细分事件回调
 *  @param mediaPlayer  播放器实例
 *  @param event 播放事件
 *  @param params 播放事件参数
 */
- (void)mediaPlayer:(id<ITVKMediaPlayer>)mediaPlayer event:(TVKPlayerEvent)event withParams:(TVKPlayerEventParams *)params;

@end

#pragma mark 播放上报事件通知
@protocol TVKReportEventDelegate <NSObject>

/**
 * @brief 播放上报事件回调
 * @param mediaPlayer  播放器实例
 * @param event 上报事件
 * @param params 事件参数
 */
- (void)mediaPlayer:(id<ITVKMediaPlayer>)mediaPlayer reportEvent:(TVKReportEvent)event withParams:(TVKReportEventParams *)params;

@end
