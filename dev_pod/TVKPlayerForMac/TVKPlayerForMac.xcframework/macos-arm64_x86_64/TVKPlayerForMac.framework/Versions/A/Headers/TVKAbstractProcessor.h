/*****************************************************************************
 * @copyright Copyright (C), 1998-2021, Tencent Tech. Co., Ltd.
 * @file     TVKAbstractProcessor.h
 * @brief    特效工厂
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/2/19
 * @license  GNU General Public License (GPL)
 *****************************************************************************/


/// 音效状态
/// 注意！！！这些定的所有枚举均对外，千万不要改现有枚举对应的值，这会导致依赖老版本tvk的sdk和依赖新版本tvk的工程一起编译运行后走到错误的逻辑中。
/// 别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!
typedef NS_ENUM(NSUInteger, TVKPostProcessorType) {
    // 视频后处理
    TVKPostProcessorTypeVideo,
    // 音频后处理
    TVKPostProcessorTypeAudio,
};

@protocol TVKAbstractProcessor <NSObject>

/**
 * 表示处理器的类型，图像或者音频
 */
@property (nonatomic, readwrite) TVKPostProcessorType processorType;

@end

