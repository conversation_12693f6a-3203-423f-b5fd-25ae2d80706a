/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     ITVKSnapshotor.h
 * @brief    不依赖播放器实例的视频截图接口，支持使用url形式视频资源，一次截取单张或多张图
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/12/3
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKVideoFrameBuffer.h"
#import "TVKPixelFormat.h"

NS_ASSUME_NONNULL_BEGIN

/// 截图的各种参数
/// 进行截图时可不传，采用默认值
@interface TVKSnapshotParams : NSObject
/// 截图的宽，为0代表使用默认宽，即图像原始的宽
/// 默认为0
@property (nonatomic, assign) int width;
/// 截图的高，为0代表使用默认高，即图像原始的高
/// 默认为0
@property (nonatomic, assign) int height;
/// 实际截取的图像时间戳会在[positionMs - positionMsToleranceBefore, positionMs +
/// positionMsToleranceAfter]之间。
/// 两个值均为0时，代表将启用精准seek来进行截图，这将耗费一定的解码时间。
/// 允许在请求时间之前截图的毫秒数, 默认为0
@property (nonatomic, assign) int64_t positionMsToleranceBefore;
/// 允许在请求时间之后截图的毫秒数, 默认为0
@property (nonatomic, assign) int64_t positionMsToleranceAfter;

@end

/// 回调方法
@protocol ITVKSnapshotorDelegate <NSObject>

@required

/// @brief 视频帧截图成功回调
/// 由于截图工具支持某次任务截多张图，所以需要靠回调的截图位置来区分数据帧
/// @param taskId 截图任务id
/// @param requestedPositionMs 请求截图的播放位置
/// @param actualPositionMs 实际截图的播放位置
/// @param videoFrameBuffer 视频帧数据
- (void)onSnapshotSuccess:(int64_t)taskId
      requestedPositionMs:(int64_t)requestedPositionMs
         actualPositionMs:(int64_t)actualPositionMs
            videoFrameOut:(TVKVideoFrameBuffer *)videoFrameBuffer;

/// @brief 视频帧截图失败回调
/// @param taskId 截图任务id
/// @param requestedPositionMs 请求截图的播放位置
/// @param error 错误信息
- (void)onSnapshotFailed:(int64_t)taskId
     requestedPositionMs:(int64_t)requestedPositionMs
                   error:(NSError *)error;

@end

@protocol ITVKSnapshotor <NSObject>

/// 截图回调
@property (nonatomic, weak) id<ITVKSnapshotorDelegate> snapshotorDelegate;

/// 截取单张图的接口
/// @param positionMs 希望截图的播放位置,单位毫秒
/// @param params 截图的参数
/// @return id，任务id，用于回调区分任务
- (int64_t)snapshotAsyncAtPositionMs:(int64_t)positionMs
                              params:(nullable TVKSnapshotParams *)params;

/// 截取多张图的接口
/// @param positionsMs 希望截取的时间戳集合, 时间戳的单位为毫秒
/// @param params 截图的参数
/// @return id，任务id，用于回调区分任务
- (int64_t)snapshotAsyncForPositionsMs:(NSArray<NSNumber *> *)positionsMs
                                params:(nullable TVKSnapshotParams *)params;

/// 取消所有截图操作
- (void)cancelAll;

@end

NS_ASSUME_NONNULL_END
