/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     ITVKVideoFrameCapture.h
 * @brief    自研播放器视频截取接口 不仅可以通过vid的方式出图，也可以通过url的方式出图
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/1/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKMediaInfo.h"
#import "TVKUserInfo.h"
#import "TVKVideoFrameBuffer.h"

NS_ASSUME_NONNULL_BEGIN

@protocol ITVKVideoFrameCapture;

/// 回调方法
@protocol TVKVideoFrameCaptureDelegate <NSObject>

@required

/// @brief 视频帧回调
/// 该接口为同步接口，因为视频帧数据必须按照时序。
/// 如果要在该回调中对视频数据处理，注意不能做耗时操作，会阻塞解码线程。
/// @param videoFrameCapture 视频截取器实例
/// @param videoFrameBuffer 视频帧数据
- (void)onCapture:(id<ITVKVideoFrameCapture>)videoFrameCapture videoFrameOut:(TVKVideoFrameBuffer *)videoFrameBuffer;

/// 视频截取器加载完成， 之后可以调用start开始出图
/// @param videoFrameCapture 视频截取器实例
- (void)onCapturePrepared:(id<ITVKVideoFrameCapture>)videoFrameCapture;

/// 完成回调
/// @param videoFrameCapture 视频截取器实例
- (void)onCaptureCompletion:(id<ITVKVideoFrameCapture>)videoFrameCapture;

/// 错误回调
/// @param videoFrameCapture 视频截取器实例
/// @param error 错误信息
- (void)onCapture:(id<ITVKVideoFrameCapture>)videoFrameCapture error:(NSError *)error;

/// @brief 是否可以获得视频帧回调。
/// 一些情况下，视频帧数据无法回调（比如加密视频、hdr视频），此时isAvailable为NO。如果能回调视频帧数据，则isAvailable为YES。
/// 回调时机：起播调用captureVideoByMediaInfo后
/// @param videoFrameCapture 视频截取器实例
/// @param isAvailable 是否可获得视频帧回调
- (void)onCapture:(id<ITVKVideoFrameCapture>)videoFrameCapture videoFrameOutAvailable:(BOOL)isAvailable;

@optional

/// seek完成
/// @param videoFrameCapture 视频截取器实例
- (void)onSeekComplete:(id<ITVKVideoFrameCapture>)videoFrameCapture;

/// 开始缓冲
/// @param videoFrameCapture 视频截取器实例
- (void)onStartBuffering:(id<ITVKVideoFrameCapture>)videoFrameCapture;

/// 结束缓冲
/// @param videoFrameCapture 视频截取器实例
- (void)onEndOfBuffering:(id<ITVKVideoFrameCapture>)videoFrameCapture;

@end

/// 自研播放器出图接口
@protocol ITVKVideoFrameCapture <NSObject>

/// delegate
@property (nonatomic, weak) id<TVKVideoFrameCaptureDelegate> delegate;

/// 启动加载  通过vid生成帧数据，cgj的交互流程由内部完成，外部不需要了解
/// @param  mediaInfo: 视频信息，mediaInfo里可以配置跳过片头片尾时间点，即起始点和结束点
/// @param  userInfo: 用户信息
- (void)captureVideoByMediaInfo:(TVKMediaInfo *)mediaInfo userInfo:(TVKUserInfo *)userInfo;

/// 开始
- (void)start;

/// 暂停
- (void)pause;

/// 停止
- (void)stop;

/// seek拖动
/// @param positionSec: 需要seek到的点位 单位秒
/// @param mode: 模式, 默认是非精准seek  参考TVKSeekMode
- (void)seekTo:(NSTimeInterval)positionSec mode:(TVKSeekMode)mode;

@end

NS_ASSUME_NONNULL_END
