//
//  TVKClipInfo.h
//  TVKPlayer
//
//  Created by hem<PERSON><PERSON> on 2019/10/1.
//  Copyright © 2019 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// @brief 分片信息
@interface TVKClipInfo : NSObject
/// 索引
@property (nonatomic, assign) int index;
/// 分片时长
@property (nonatomic, assign) NSTimeInterval clipDuration;
/// 分片大小
@property (nonatomic, assign) int64_t clipSize;
/// 分片MD5
@property (nonatomic, copy) NSString *md5;
/// 分片keyID
@property (nonatomic, copy) NSString *keyID;
/// 分片vkey
@property (nonatomic, copy) NSString *vkey;
/// 分片sha
@property (nonatomic, copy) NSString *sha;

/**
 * 只有分片MP4+免流的情况会使用到该property，因为免流情况下，getvbkey直接返回每个分片的地址，不需要拼接
 */
@property (nonatomic, copy) NSArray<NSString *> *urlList;

@end

NS_ASSUME_NONNULL_END
