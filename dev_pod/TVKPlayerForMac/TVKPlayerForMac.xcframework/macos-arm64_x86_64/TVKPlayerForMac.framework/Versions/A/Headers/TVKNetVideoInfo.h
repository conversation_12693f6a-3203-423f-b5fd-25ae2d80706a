/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKNetVideoInfo.h
 Author      : liyukuan
 Version     : 1.0
 Date        : 17/1/6
 Description : 后台返回的媒体信息。详细字段含义见视频信息协议 https://tapd.woa.com/qqvideo_prj/markdown_wikis/#1010114481009541303
 History     : 17/1/6 初始版本
 ***********************************************************/

#import <CoreGraphics/CoreGraphics.h>
#import <Foundation/Foundation.h>

#import "TVKPlayerDefine.h"

NS_ASSUME_NONNULL_BEGIN
/// 后台返回的字幕信息
@interface TVKSubTitleInfo : NSObject

/// 软字幕语种.。比如"ch","eng"等.和TVKPlayerDefine.h 中TVKMediaSubtitleLanguage定义一致.
/// (后台可能扩充字幕语种支持，返回的语种范围要大于TVKMediaSubtitleLanguage中的定义)
@property (nonatomic, copy, nonnull) NSString *subtitleLanguage;

/// 软字幕格式
@property (nonatomic, copy, nonnull) NSString *formatId;

/// 软字幕名称。比如"中文"，"英文"等
@property (nonatomic, copy, nonnull) NSString *name;

/// 字幕编码id
@property (nonatomic, copy) NSString *langId;

/// 1：srt，2：ass，3：vtt
@property (nonatomic, assign) int type;

/// 表示硬字幕顶部距离视频画面顶部的高度百分比，取值[0,100]
@property (nonatomic, assign) CGFloat captionTopHPercent;
/// 表示硬字幕底部距离视频画面顶部的高度百分比，取值[0,100]
@property (nonatomic, assign) CGFloat captionBottomHPercent;

/// 标识字幕特性
/// 0x01，标识是AI生成字幕
@property (nonatomic, assign) int feature;

/// 字幕的权益标识
@property (nonatomic, assign) int limit;

/// 当前用户是否有字幕播放付费权益，0-表示无需付费（可播）1-表示需要付费（不可播）
@property (nonatomic, assign) int charge;

@end

/// 后台返回的清晰度信息
@interface TVKNetMediaDefinitionInfo : NSObject

/// 视频清晰度名字，比如sd，hd等，见TVKPlayerDefine.h
@property (nonatomic, copy, nonnull) NSString *definition;

/// 该格式实际的清晰度，比如sd、hd、shd.
/// 和filename(definition)不同，比如filename=hdr10, formatdefn=fhd
@property (nonatomic, copy) NSString *formatDefn;

/// 清晰度分组，透传给业务，比如1080和hdr如果属于同一组，在业务UI上是同一个清晰度格子
@property (nonatomic, assign) int group;

/// 当前视频的formatId
@property (nonatomic, copy) NSString *formatId;

/// 清晰度显示完整文案，以分号和括号作为分隔符将清晰度名字和分辨率等隔开，调用方可根据自己需要处理。格式如下：
/// 标清;(270P)
/// 高清;(480P)
/// 超清;(720P)
/// 蓝光;(1080P VIP尊享)
/// HDR臻彩视界;(VIP尊享)
/// 杜比视听;(VIP尊享)
@property (nonatomic, copy) NSString *fullText;

/// 分辨率文本，比如：270P、480P、720P、1080P、HDR
@property (nonatomic, copy) NSString *resolutionText;

/// 清晰度的显示名称，由fullText解析而成。例子：
/// 高清 720P
/// 蓝光 1080P VIP尊享
/// 注：此字段即将废掉，推荐使用fullText;
@property (nonatomic, copy, nonnull) NSString *definitionShowName DEPRECATED_MSG_ATTRIBUTE("use fullText instead");

/// 清晰度显示的短格式名称
@property (nonatomic, copy, nonnull) NSString *definitionShowShortName;

/// 视频源存在视频和音频合流的场景，同一清晰度可能有多个不同音频的流。
/// 该值用来表示对应视频清晰度下拥有的音频类型，格式为[@"aac", @"atmos",...]，为空表示只有aac，目前该值仅直播可用
@property (nonatomic, copy) NSArray *liveAudioTrackList;

/// 当前清晰度格式是否需要会员才能观看
@property (nonatomic, assign) BOOL isNeedVip;

/// 当前清晰度格式对应的整体文件大小，单位字节
@property (nonatomic, assign) UInt64 fileSize;

/// 当前清晰度视频编码格式，1:H264, 2:H265, 3:HDR10, 4:DolbyVision
@property (nonatomic, assign) int videoCodec;

/// 当前清晰度音频编码格式，1:AAC, 2:Dolby Surround, 3:Dolby Atmos, 4:Dolby 2.0, 5:DTS HD, 6:DTS X, 7:PCG 2.0, 8:aac5.1, 9:audio vivid, 10:wanos 11:flac
@property (nonatomic, assign) int audioCodec;

/// 当前清晰度会进行哪些后处理，bitset形式，0x1:超分
@property (nonatomic, assign) int postProcess DEPRECATED_MSG_ATTRIBUTE("use videoSuperResolutionType instead");

/// 当前清晰度支持哪些超分：0: 不支持， 1: TVM超分
@property (nonatomic, assign) int videoSuperResolutionType;

/// 当前清晰度格式使用终端超分能力时，是否需要会员才能观看
@property (nonatomic, assign) BOOL isSuperResolutionNeedVip;

/// app清晰度推荐
@property (nonatomic, assign) BOOL recommend;

/// 视频当前帧率，一般非高帧默认25，高帧格式会对齐当前播放格式的帧率
@property (nonatomic, assign) int vfps;

/// 清晰度限制说明。
/// | limit | 说明 |
/// | ----- | ---- |
/// | 0     | 可免费播放 |
/// | 1     | 会员专享 |
/// | 3     | 会员专享 |
/// | 4     | 限时免费（非会员可播，前端提示限时免费) |
@property (nonatomic, assign) int limit;

/// drm类型
/// | drm值 | 含义 |
/// | ------ | ---- |
/// | 0      | 非加密视频 |
/// | 1      | marlindrm加密视频 |
/// | 2      | 数字太和drm |
/// | 3      | chacha20加密视频 |
/// | 4      | fairplay加密视频 |
/// | 5      | widevine加密视频 |
/// | 6      | 自研chinadrm(不会新增) |
/// | 7      | chinadrm2.0 |
@property (nonatomic, assign) int drm;

/// 清晰度特性，透传，播放器暂不理解
/// 0x01 无损格式
/// 0x04 当前流是无字幕清流
/// 0x08 当前流有无字幕清流
/// 0x10 当前流有硬字幕流
@property (nonatomic, assign) int feature;

/// 当前视频宽
@property (nonatomic, assign) int width;

/// 当前视频高
@property (nonatomic, assign) int height;

@end

/// 后台返回的多音轨信息
@interface TVKAudioTrackInfo : NSObject

/// 音轨名称.比如"杜比"
@property (nonatomic, copy) NSString *showName;

/// 音频流名称. 比如"db"、"aac"
@property (nonatomic, copy) NSString *audioTrack;

/// 当前清晰度格式是否需要会员才能观看
@property (nonatomic, assign) BOOL isNeedVip;

/// 付费限制标签
@property (nonatomic, assign) int lmt;

/// 音轨特性
@property (nonatomic, assign) int feature;

/// 音轨属性(1.aac，2.DolbySurround，3.Dolby Atmos)
@property (nonatomic, assign) int audio;

/// 试听时长，-1：表示无限制（免费或用户已付费），0：表示无试听，大于0为试听最大时长
@property (nonatomic, assign) int preview;

/// 音轨类型(0.外挂音轨，1.合流音轨)
@property (nonatomic, assign) TVKAudioTrackType type;

@end

/// seek的预览截图信息
/// 请参考wiki：http://tapd.oa.com/qqvideo_prj/markdown_wikis/#1010114481005866113
@interface TVKNetThumbInfo : NSObject

/// 截图的宽度
@property (nonatomic, assign) CGFloat width;

/// 截图的高度
@property (nonatomic, assign) CGFloat height;

/// 一张物理截图每行有几张小图
@property (nonatomic, assign) NSInteger column;

/// 一张物理截图有多少行
@property (nonatomic, assign) NSInteger row;

/// 截图与截图之间的时间间隔，单位秒
@property (nonatomic, assign) NSTimeInterval interval;

/// 截图下载地址的前缀
@property (nonatomic, copy, nonnull) NSString *urlPrefix;

/// 截图格式对应的（部分）文件名
@property (nonatomic, copy, nonnull) NSString *fileName;

@end

/// 后台返回的直播回看信息
@interface TVKNetLiveSeebackInfo : NSObject

/// 回看开始时间
@property (nonatomic, assign) NSUInteger seebackStartTime;

/// 最大回看时长
@property (nonatomic, assign) NSUInteger maxSeebackTime;

/// 服务器当前时间
@property (nonatomic, assign) NSUInteger serverTime;

/// 当前这次播放是否是回看
@property (nonatomic, assign) BOOL isSeebackState;

@end

/// 直播试看信息
@interface TVKNetLivePreviewInfo : NSObject

/// 直播流的播放限时（本次试看剩余时长)，单位秒，0为不限制
@property (nonatomic, assign) NSTimeInterval currentPreviewDurationSec;

/// 直播流的每次总的可试看时长，单位秒，0为不支持。对单个直播，值固定，比如一个直播每次试看时长为300s，如果已经请求了一次试看，在100s后又请求了一次试看，
/// 第二次请求时因为第一次试看时间没到，仍为第一次试看，此时previewDurationSec仍为300s，但是第二次实际可试看时长currentPreviewDurationSec=200s
@property (nonatomic, assign) NSTimeInterval previewDurationSec;

/// 当天可以试看的总次数
@property (nonatomic, assign) NSInteger previewCount;

/// 当天剩余可试看次数
@property (nonatomic, assign) NSInteger restPreviewCount;

@end

/// 后台返回的直播排队信息
@interface TVKLiveQueueInfo : NSObject

/// 是否排队，0:未排队, 1.排队中, 2.已出队
@property (nonatomic, assign) int queue_status;

/// 排队的排名信息
@property (nonatomic, assign) int queue_rank;

/// 开通会员是否可以插队, 0:NO, 1:YES
@property (nonatomic, assign) int queue_vip_jump;

/// 排队的会员key， 轮询时使用
@property (nonatomic, copy, nonnull) NSString *queue_session_key;

@end

/// 后台正片广告信息，主要为创意中插信息
/// 创意中插广告信息
@interface TVKPADInfo : NSObject

/// 片段的vid
@property (nonatomic, strong) NSString *vid;

/// 广告开始时间
@property (nonatomic, assign) double startTime;

/// 广告分片时长
@property (nonatomic, assign) double duration;

/// 广告分片偏移，单位秒
@property (nonatomic, assign) double offsetTime;

/// 广告类型
@property (nonatomic, assign) NSInteger optType;

/// hls标版广告形态
@property (nonatomic, assign) NSInteger hlsType;

@end

/// 广告信息
@interface TVKADInfo : NSObject

/// 广告的session ID
@property (nonatomic, strong) NSString *adsid;

/// 创意中插广告列表原始数据
@property (nonatomic, strong) NSString *adPInfo;

/// 创意中插广告列表
@property (nonatomic, strong) NSArray<TVKPADInfo *> *pADInfos;

/// 广告透传信息
@property (nonatomic, copy) NSString *adPass;

@end

/// 后台返回的水印信息
@interface TVKNetWaterMarkInfo : NSObject

/// 遮标水印位置
@property (nonatomic, assign) CGRect blockPosition;

/// 是否遮挡logo
@property(nonatomic, assign) BOOL isShow;

@end


/// 后台返回的静态水印信息(点播或直播)
@interface TVKNetStaticWaterMarkInfo : NSObject
/// 水印位置
@property (nonatomic, assign) CGRect position;
/// 水印图片Md5
@property (nonatomic, copy) NSString *md5;
/// 图片地址
@property (nonatomic, copy) NSString *url;
/// 图片https 地址
@property (nonatomic, copy) NSString *httpsUrl;
/// 透明属性
@property (nonatomic, assign) NSInteger alpha;
/// 是否需要显示
@property (nonatomic, assign) BOOL isShow;

@end

/// 漂浮水印信息
@interface TVKNetFloatWaterMarkInfo : NSObject

/// 登录态漂浮水印显示flag。0不使用漂浮水印，1使用漂浮水印
@property (nonatomic, assign) int fwFlag;

/// 登录态漂浮水印需要展示的name名称，通过vuid转成的rtxname。这里水印跟其他水印不同，只有文字，需要由app自己来绘制
@property (nonatomic, copy) NSString *uKey;

@end

/// 富媒体信息
@interface TVKNetRichMediaInfo : NSObject

/// 富媒体的网关地址
@property (nonatomic, copy) NSString *richMediaUrl;
@end

/// 伪直播信息
@interface TVKNetSimulatedLiveInfo : NSObject

/// 伪直播的chid
@property (nonatomic, copy) NSString *chid;

/// 伪直播的扩展信息 透传给app
@property (nonatomic, copy) NSString *extInfo;

@end

/// 非纯净版片源广告起始位置信息
@interface TVKAdDot : NSObject

/// 广告开始时间 ms
@property (nonatomic, assign) int64_t startTimeMs;

/// 广告结束时间 ms
@property (nonatomic, assign) int64_t endTimeMs;

@end

/// 后台返回的正片视频信息，包括当前清晰度，清晰度列表等信息
@interface TVKNetVideoInfo : NSObject

///  视频id.用于某些场景下，比如秒播，外面没有videoId的情况，此时需要使用这里返回的videoId
@property (nonatomic, copy, nonnull) NSString *videoId;

/// 视频专辑id.
@property (nonatomic, copy) NSString *cid;

/// 直播节目id
@property (nonatomic, copy) NSString *livePid;

/// 直播机位id
@property (nonatomic, copy) NSString *liveChid;

///  视频时长，单位为秒
@property (nonatomic, assign) NSTimeInterval duration;

/// 当前播放的url
@property (nonatomic, copy) NSString *playUrl;

/// 备用url
@property (nonatomic, copy) NSArray<NSString *> *backPlayUrl;

///  当前视频播放的清晰度信息
@property (nonatomic, strong, nonnull) TVKNetMediaDefinitionInfo *currentDefinition;

///  当前视频的清晰度列表信息
@property (nonatomic, strong, nonnull) NSArray<TVKNetMediaDefinitionInfo *> *definitionList;

/// seek的预览截图信息列表
@property (nonatomic, strong, nonnull) NSArray<TVKNetThumbInfo *> *thumbInfoList;

/// 付费状态
/// -2 用户没有登录
/// -1 视频状态非法
/// 0  无需付费/检查没付费
/// 1  单片已付费
/// 2  会员（包月）已付费
@property (nonatomic, assign) NSInteger chargeState;

/// 视频状态
/// 跟用户付费状态有关，表示该用户对该视频的播放权限
/// 取值0, 1, 2, 3, 5, 6, 130, 131, 133, 134
/// 注意：当视频状态为2时, 视频可以播放, 其他状态都不可播放；视频状态8为收费状态
@property (nonatomic, assign) int state;

/// 付费限制细分错误码
/// 1:防盗链限制 2:站外限 3:清晰度试看
/// 参考文档：http://tapd.oa.com/qqvideo_prj/markdown_wikis/show/#1210114481001144847
@property (nonatomic, assign) NSInteger exem;

/// 媒资付费状态(仅在秒播时使用)
@property (nonatomic, assign) int mediaState;

/// 视频“链接”（即两个视频内容相同但是vid不一样, 当没有链接时, link的值为vid本身）
@property (nonatomic, copy, nonnull) NSString *lnk;

/// 点播试看起始时间
@property (nonatomic, assign) NSTimeInterval vodPreviewStart;

/// 点播试看时长
@property (nonatomic, assign) NSTimeInterval vodPreviewTime;

/// 当前视频是否需要付费
@property (nonatomic, assign) BOOL needPay;

/// 用户是否已经付过费
@property (nonatomic, assign) BOOL isPay;

/// 是否是试看
@property (nonatomic, assign) BOOL isPreview;

/// 视频宽高比，
@property (nonatomic, assign) float aspectRation;

/// getVInfo返回的视频宽高(目前仅点播cgi返回该字段，直播后续可能进行扩展)
@property (nonatomic, assign) CGSize videoSize;

/// 后台返回的媒体格式
@property (nonatomic, assign) TVKMediaFormat mediaFormat;

/// 是否是VR视频
@property (nonatomic, assign) BOOL isVR;

/// 是否是快发版本视频，快发版本视频是非完整版视频
@property (nonatomic, assign) BOOL fVideo;

/// 直播排队
@property (nonatomic, strong, nullable) TVKLiveQueueInfo *liveQueueInfo;

/// 直播试看信息
@property (nonatomic, strong, nullable) TVKNetLivePreviewInfo *livePreviewInfo;

/// 直播回看信息
@property (nonatomic, strong, nullable) TVKNetLiveSeebackInfo *seebackInfo;

/// 当前视频的字幕列表
@property (nonatomic, strong, nullable) NSArray<TVKSubTitleInfo *> *subtitleList;

/// 视频当前的字幕
@property (nonatomic, strong, nullable) TVKSubTitleInfo *currentSubtitle;

///  当前视频的音频信息
@property (nonatomic, strong, nonnull) TVKAudioTrackInfo *currentAudioTrackInfo;

///  当前视频的音频列表信息
@property (nonatomic, strong, nonnull) NSArray<TVKAudioTrackInfo *> *audioTrackInfoList;

/// 用户主动切换的清晰度
@property (nonatomic, assign) BOOL isUserSwithDefition;

/// 截屏/录屏方式
/// 0: 使用 app 逻辑判断
///  1: 不可截屏/录屏
/// 2: 系统不可但 app 可截屏/录屏
/// 3: 系统和 app 均可截屏/录屏
@property (nonatomic, assign) NSInteger shotType __attribute((deprecated("use sshot instead.")));

/// 截屏/录屏方式
/// 0: 使用 app 逻辑判断
/// 1: 不可截屏/录屏
/// 2: 系统不可但 app 可截屏/录屏
/// 3: 系统和 app 均可截屏/录屏
@property (nonatomic, assign) NSInteger sshot;

/// 截图方式 0: 播放器截图，1: 后台截图
@property (nonatomic, assign) NSInteger mshot;

/// 跟随正片信息返回的广告信息
@property (nonatomic, strong) TVKADInfo *adInfo;

/// 水印信息
@property (nonatomic, copy) NSArray<TVKNetWaterMarkInfo *> *waterMarkInfos;

#if TARGET_OS_VISION
/// 静态水印信息，RealityKit场景下由App进行绘制，播放器仅做协议解析
@property (nonatomic, copy) NSArray<TVKNetStaticWaterMarkInfo *> *staticWaterMarkInfos;
#endif

/// 0 不加密、1 弱加密、2 强加密
/// 直播加密类型，透传给app
@property (nonatomic, assign) NSInteger streamSecret;

/// 点播加密类型，透传给app
@property (nonatomic, strong) NSString *vodEncryption;

/// 视频是否强加密
@property (nonatomic, assign) BOOL strongEncryption;

/// 漂浮水印信息。这个水印跟其他水印不一样，只有文字，播放器仅做协议解析，由app进行绘制。仅仅直播情况下有此字段
@property (nonatomic, strong) TVKNetFloatWaterMarkInfo *floatWaterMarkInfo;

/// 富媒体信息
@property (nonatomic, strong) TVKNetRichMediaInfo *richMediaInfo;

/// 后台返回的AB实验信息
/// key: 实验名称
/// value: 实验ID
@property (nonatomic, strong) NSDictionary<NSString *, NSNumber *> *abTestInfo;

/// vvip时实际播的纯净片源的vid
@property (nonatomic, copy) NSString *pureVid;

/// 非vvip的压片广告时间信息
@property (nonatomic, strong) NSArray<TVKAdDot *> *embeddedAdDots;

/// report 字段，透传后台的report节点下的内容
@property (nonatomic, copy) NSString *report;

/// 签名， 透传给app
@property (nonatomic, copy) NSString *signature;

/// 文件名称
@property (nonatomic, copy) NSString *fileName;

/// 视频标题，透传给app
@property (nonatomic, copy) NSString *title;

/// 视频片头时长。单位：秒
@property (nonatomic, assign) NSTimeInterval startPositionSec;

/// 视频片尾时长。单位：秒
@property (nonatomic, assign) NSTimeInterval skipEndPositionSec;

/// 业务透传字段，播放器不理解
@property (nonatomic, copy) NSString *bizExt;

/// 硬字幕位置信息，字幕最低点位置在视频高度上的比例
@property (nonatomic, assign) float subtitleNadir;

/// 硬字幕位置信息，字幕最高点位置在视频高度上的比例
@property (nonatomic, assign) float subtitlePeak;

/// 投屏设置，透传给app。  后台配置的这四种投屏方式：私有、dlna、airplayStreaming、airplayMirroring，是否允许
@property (nonatomic, copy) NSString *castSet;

/// 表示包含创意中插广告的视频文件总时长.  该值为0时请使用duration值获取视频时长。单位Ms
@property (nonatomic, assign) int64_t totalDurationMs;

/// 是否是低清晰度起播
@property (nonatomic, assign) BOOL lowDefinitionOpen __attribute__((deprecated("低清晰度起播功能已下线")));

/// getVinfo结果来源类型
@property (nonatomic, assign) TVKMediaPlayInfoFromType fromType;

/// 伪直播chid
@property (nonatomic, copy) NSString *simulatedLiveChid __attribute__((deprecated("废弃，simulatedLiveInfo替代")));

/// 伪直播信息
@property (nonatomic, strong) TVKNetSimulatedLiveInfo *simulatedLiveInfo;

/// 压流广告的点位信息 json字符串 透传给APP
@property (nonatomic, copy) NSString *dotInfo;

/// 是否VVC编码格式
@property (nonatomic, assign) BOOL isVvc;

/// 视频源特性
@property (nonatomic, assign) int feature;

@end

NS_ASSUME_NONNULL_END
