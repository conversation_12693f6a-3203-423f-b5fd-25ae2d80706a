//
//  ITVKVideoView.h
//  TXVPlayer
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/10/23.
//  Copyright © 2019 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "ITVKDrawableContainer.h"

/// view所属场景，用于业务方从TVKVideoView获取一个用于
/// 某个场景的子view，业务方将自己的内容放在该子view上
typedef NS_ENUM(NSUInteger, TVKViewScene) {
    // 广场场景
    TVKViewSceneAD,
};

@protocol ITVKVideoView;

@protocol TXVVideoViewDelegate <NSObject>

@optional
- (void)videoView:(id<ITVKVideoView>)videoView newFrame:(CGRect)frame;

@end

@protocol ITVKVideoView <ITVKDrawableContainer>

- (void)setDelegate:(id<TXVVideoViewDelegate>)delegate;

/// remove所有的子view，如果你在新的播放器实例中复用之前的播放器实例，最好调用该方法去掉所有子view
- (void)removeAllSubviews;

/// 获取内部中台播放器正片使用的view
#if TARGET_OS_OSX
- (NSView *)currentDisplayView;
#else
- (UIView *)currentDisplayView;
#endif

/// 获取特定场景的view，如果不存在，则创建，如果存在，则直接返回。
/// 该子view铺满整个TVKVideoView，即大小和TVKVideoView保持一致
/// 该子view的层级由内部决定，内部会把该子view放在合适的层级
#if TARGET_OS_OSX
- (NSView *)subviewWithScene:(TVKViewScene)scene;
#else
- (UIView *)subviewWithScene:(TVKViewScene)scene;
#endif

@end
