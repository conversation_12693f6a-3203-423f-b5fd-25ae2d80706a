/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     ITVKRichMediaSynchronizer.h
 * @brief    富媒体处理接口和消息delegate定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2020/4/10
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#import "TVKRichMediaDefine.h"

@protocol ITVKRichMediaSynchronizer;

@protocol ITVKRichMediaSynchronizerDelegate <NSObject>

/**
 * 在调用select接口后，那么当有内容返回，则通过此接口返回
 *
 * @param synchronizer 富媒体处理引擎实例
 * @param feature 富媒体功能
 * @param featureData 富媒体返回内容
 */
- (void)onRichMedia:(id<ITVKRichMediaSynchronizer>)synchronizer
                   feature:(TVKRichMediaFeature *)feature
               featureData:(TVKRichMediaFeatureData *)featureData;

/**
 * 富媒体运行出错，无法再继续工作时，通过此接口回调消息通知
 *
 * @param synchronizer 富媒体处理引擎实例
 * @param errorCode 详细的错误码，用于调试和数据上报
 */
- (void)onRichMedia:(id<ITVKRichMediaSynchronizer>)synchronizer
          errorCode:(int)errorCode;

/**
 * 调用select接口后成功执行结果通知
 *
 * @param synchronizer 富媒体处理引擎实例
 * @param feature 富媒体功能
 */
- (void)onSelectFeatureSuccess:(id<ITVKRichMediaSynchronizer>)synchronizer
                       feature:(TVKRichMediaFeature *)feature;

/**
 * 调用deselect接口后成功执行结果通知
 *
 * @param synchronizer 富媒体处理引擎实例
 * @param feature 富媒体功能
 */
- (void)onDeselectFeatureSuccess:(id<ITVKRichMediaSynchronizer>)synchronizer
                         feature:(TVKRichMediaFeature *)feature;

/**
 * 某个富媒体功能因为数据加载、解析等失败，无法再回抛数据富媒体内容，通过此接口进行通知。
 *
 * @param synchronizer  富媒体处理引擎实例
 * @param feature 富媒体功能
 * @param errorCode 错误码
 */
- (void)onRichMediaFeatureFailure:(id<ITVKRichMediaSynchronizer>)synchronizer
                          feature:(TVKRichMediaFeature *)feature
                        errorCode:(int)errorCode;

/**
 *富媒体的info信息回抛接口
 *@param infoType  info信息的类型
 *@param infoData  info信息携带的数据
 */
- (void)onRichMedia:(id<ITVKRichMediaSynchronizer>)synchronizer
           infoType:(TVKRichMediaInfoType)infoType
           infoData:(TVKRichMediaInfoData *)infoData;

@end

@protocol ITVKRichMediaSynchronizer <NSObject>

/**
 用于监听richMediaProcess的消息通知等
 */
@property (nonatomic, weak) id<ITVKRichMediaSynchronizerDelegate> delegate;

/**
 获取所有支持的富媒体功能列表
 @return 所有支持的富媒体功能列表
 */
- (NSArray<TVKRichMediaFeature *> *)features;

/**
 开启富媒体流功能
 @param feature  指定的富媒体功能
 */
- (TVKRichMediaActResult)selectFeature:(TVKRichMediaFeature *)feature;

/**
 关闭富媒体流功能
 @param feature 指定的富媒体功能
 */
- (TVKRichMediaActResult)deselectFeature:(TVKRichMediaFeature *)feature;

@end
