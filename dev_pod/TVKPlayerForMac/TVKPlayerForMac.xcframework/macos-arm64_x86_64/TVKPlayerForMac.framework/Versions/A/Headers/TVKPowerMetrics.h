/************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TVKPowerMetrics.h
 * @brief    功耗度量信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/10/12
 * @license     GNU General Public License (GPL)
 ***********************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, TVKPowerLevel) {
   /**
    * 正常，可正常运行各项能力
    */
   TVKPowerLevelNormal = 0,

   /**
    * 高，建议不影响用户体验的情况下做降功耗策略。
    */
   TVKPowerLevelHigh = 1,

   /**
    * 很高，建议进行较严格限制，轻微影响用户体验的情况下做降功耗策略。
    */
   TVKPowerLevelVeryHigh = 2,

   /**
    * 极高，建议采用最严格的限制关闭不必要的功能。
    */
   TVKPowerExtremelyHigh = 3,
};

/// @brief 同文件头注释
@interface TVKPowerMetrics : NSObject

/// 功耗评级等级
@property(nonatomic, assign, readonly) TVKPowerLevel level;

@end

NS_ASSUME_NONNULL_END

