/************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     ITVKPowerController.h
 * @brief    TVKPlayer全局功耗控制器
 *           计划是通过实时的功耗情况调整TVKPlayer功能升降级
 *           但目前功耗的评估上层来负责，当前功耗控制器只接受调整指令
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/10/12
 * @license     GNU General Public License (GPL)
 ***********************************************************/

#import <Foundation/Foundation.h>
#import "TVKPowerMetrics.h"
#import "ITVKPowerConsumerRegistrationDelegate.h"

NS_ASSUME_NONNULL_BEGIN

/// @brief 功耗控制器
@protocol ITVKPowerController <NSObject>

/// 功耗消耗者注册回调
@property (atomic, weak) id<ITVKPowerConsumerRegistrationDelegate> delegate;

/// 功耗信息
@property (atomic, strong) TVKPowerMetrics *powerMetrics;

@end

NS_ASSUME_NONNULL_END
