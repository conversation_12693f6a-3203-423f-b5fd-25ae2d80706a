/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TVKDownloadProxyUrlBuilder.h
 * @brief    下载组件本地代理地址获取器
 * <AUTHOR>
 * @version  1.0.0
 * @date     2022/9/13
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKMediaPlayInfo.h"
#import "TVKDownloadProgressInfo.h"

NS_ASSUME_NONNULL_BEGIN

/// 代理状态回调
@protocol ITVKDownloadProxyUrlBuilderDelegate <NSObject>

@optional

/// 下载进度更新
/// @param downloadProgressInfo 下载进度信息
- (void)onDownloadProgressUpdate:(TVKDownloadProgressInfo *)downloadProgressInfo;

/// 下载完成
- (void)onDownloadSuccess;

/// 下载出错
/// @param error 错误信息
- (void)onDownloadError:(NSError *)error;

@end

/// 下载组件本地代理地址获取器
@interface TVKDownloadProxyUrlBuilder : NSObject

/// 构建一个下载任务
/// @param mediaPlayInfo  CGI返回的视频相关信息
/// @param delegate 回调监听
/// @return 返回代理地址   出错时返回空，一个实例只能同时起一个下载任务，上一个任务stop前再调用该方法，会返回空
- (NSString *)buildDownloadProxyUrlWithMediaPlayInfo:(TVKMediaPlayInfo *)mediaPlayInfo
                                            delegate:(nullable id<ITVKDownloadProxyUrlBuilderDelegate>)delegate;

/// 停止当前的下载任务
- (void)stop;


@end

NS_ASSUME_NONNULL_END
