/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKTrackInfo.h
 * @brief    媒体轨道信息描述
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/02/26
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

///媒体轨道类型
/// 注意！！！该文件中定义的所有枚举均对外，千万不要改现有枚举对应的值，这会导致依赖老版本tvk的sdk和依赖新版本tvk的工程一起编译运行后走到错误的逻辑中。
/// 别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!
typedef NS_ENUM(NSUInteger, TVKTrackInfoType) {
    /// 未知
    TVKTrackInfoTypeUnknow = 0,
    /// 视频轨道
    TVKTrackInfoTypeVideo,
    /// 音频轨道
    TVKTrackInfoTypeAudio,
    /// 字幕轨道
    TVKTrackInfoTypeSubTitle,
};

NS_ASSUME_NONNULL_BEGIN

/// 视频轨、音频轨和字幕轨等轨道信息描述
@interface TVKTrackInfo : NSObject

/// 媒体轨道类型
@property (nonatomic, assign, readonly) TVKTrackInfoType trackInfoType;

/// 轨道名称
@property (nonatomic, copy, readonly) NSString *name;

/// 轨道唯一标识符
@property (nonatomic, copy, readonly) NSString *uniqueId;

/// 是否选中
@property (nonatomic, assign, readonly) BOOL isSelected;

@end

NS_ASSUME_NONNULL_END
