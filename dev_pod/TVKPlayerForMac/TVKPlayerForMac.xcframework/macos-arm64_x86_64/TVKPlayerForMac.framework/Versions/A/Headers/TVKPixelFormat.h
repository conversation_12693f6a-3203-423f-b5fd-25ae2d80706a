/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TVKPixelFormat.h
 * @brief    视频pixel format定义， 枚举值必须和ThumbPlayer(TPPixelFormat.h)保持一致
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/6/6
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 注意！！！该文件中定义的所有枚举均对外，千万不要改现有枚举对应的值，这会导致依赖老版本tvk的sdk和依赖新版本tvk的工程一起编译运行后走到错误的逻辑中。
/// 别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!

/// 视频格式定义，枚举值须跟ThumbPlayer(TPPixelFormat.h)保持一致
typedef NS_ENUM(NSInteger, TVKPixelFormat) {
    /// 未定义
    TVKPixelFormatUnknown = -1,
    /// planar YUV 4:2:0, 12bpp, (1 Cr & Cb sample per 2x2 Y samples)
    TVKPixelFormatYUV420P = 0,
    /// rgb24
    TVKPixelFormatRGB24 = 2,
    /// planar YUV 4:2:0, 12bpp, full scale (JPEG), deprecated in favor of TVK_PIX_FMT_YUV420P and setting color_range
    TVKPixelFormatYUVJ420P = 12,
    /// packed RGBA 8:8:8:8, 32bpp, RGBARGBA.
    TVKPixelFormatRGBA = 26,
    /// packed BGRA 8:8:8:8, 32bpp, BGRABGRA...
    TVKPixelFormatBGRA = 28,
    /// packed RGB 5:6:5, 16bpp, (msb)   5R 6G 5B(lsb), little-endian.
    TVKPixelFormatRGB565 = 37,
    /// CVPixelBufferRef
    TVKPixelFormatCVPixelBuffer = 160,
};
