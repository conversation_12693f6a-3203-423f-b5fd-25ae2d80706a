/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKPlayerDefine.h
 Author      : liyukuan
 Version     : 1.0
 Date        : 17-1-19
 Description :
 History     : 17-1-19 初始版本
 ***********************************************************/

/// 注意！！！该文件中定义的所有枚举均对外，千万不要改现有枚举对应的值，这会导致依赖老版本tvk的sdk和依赖新版本tvk的工程一起编译运行后走到错误的逻辑中。
/// 别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!

#pragma once

/// 错误码userInfo字段定义，在对外的错误码我们统一使用该格式。
/// @discussion 我们使用NSError的code属性携带错误码，而NSError的userInfo携带modelId, errMsg, exErrorCode(扩展错误码), exErrMsg错误码说明文案,
///             可能为空,errorLog错误信息日志打印，不用于对外展示。只有个别错误码有exErrorCode和exErrMsg，
/// @example 错误码为1380，modelId为20101，扩展错误码为3
///          code:1380
///          userInfo[@"modelId"]: 20101
///          userInfo[@"errMsg"]: @"xxx" 错误码描述文案
///          userInfo[@"exErrorCode"]: 3
///          userInfo[@"exErrorMsg"]: @"yyy"
///          userInfo[@"exObj"]: some extra info when necessary
///          userInfo[@"fullErrorCodeStr"]: @"modelId.errorCode.exErrorCode" 完整错误码，例如@"20101.1380.3"，子错误码exErrorCode可能不存在
static NSString *const kTVKErrorModelIdKey = @"modelId";

static NSString *const kTVKErrorMessageKey = @"errMsg";

static NSString *const kTVKFullErrorCodeStrKey = @"fullErrorCodeStr";

static NSString *const kTVKErrorUseVPN = @"useVPN";

static NSString *const kTVKExErrorCodeKey = @"exErrorCode";

static NSString *const kTVKExErrorMsgKey = @"exErrMsg";

static NSString *const kTVKErrorLogKey = @"errorLog";

static NSString *const kTVKExErrorDataKey = @"data";

extern NSString *const kTVKErrorAudioTrackNameKey;

extern NSString *const kTVKVideoSwitchDefinitionTypeKey;


/*** 事件信息等返回时，携带的extraInfo字典的相关key值定义 ***/
/// 自动切换的清晰度key
extern NSString *const kTVKSelfAdaptiveSwitchDefinitionTypeKey;
extern NSString *const kTVKIsSelfAdaptiveSwitchDefinitionBoolKey;
extern NSString *const kTVKMediaInfoKey;
extern NSString *const kTVKNextMediaInfoKey;
extern NSString *const kTVKNetVideoInfoKey;
extern NSString *const kTVKPlayerErrorKey;
extern NSString *const kTVKPlayerIsSucessKey;
extern NSString *const kTVKMultiNetworkCardStateKey;
extern NSString *const kTVKVideoFrameDropRateKey;
extern NSString *const kTVKVideoFrameRateKey;
extern NSString *const kTVKVideoDecoderType;

/// 播放类型
typedef enum {
    /// 腾讯视频在线点播
    TVKPlayTypeOnlineVod,
    /// 腾讯视频离线点播
    TVKPlayTypeOfflineVod,
    /// 腾讯视频边下边播
    TVKPlayTypeDownloadingVod,
    /// 腾讯视频在线直播
    TVKPlayTypeOnlineLive,
    /// 本地文件
    TVKPlayTypeLocalFile,
    /// 外部播放链接地址
    TVKPlayTypeExternalUrl
} TVKPlayType;

/// 登录类型
typedef enum {
    /// 未登录
    TVKLoginTypeNone,
    /// 主登录态为qq登录
    TVKLoginTypeQQ,
    /// 主登录态为微信登录
    TVKLoginTypeWx
} TVKLoginType;

/// 会员类型
typedef enum {
    /// 未登录
    TVKVipTypeNotLogin = 0,
    /// 登录的普通用户
    TVKVipTypeLogin = 1,
    /// 腾讯视频会员（好莱坞会员）
    TVKVipTypeVip = 2,
    /// 腾讯视频会员附属卡
    TVKVipTypeSupplementCard = 3,
    /// 腾讯视频vvip会员
    TVKVipTypeVvip = 4
} TVKVipType;

/// 流类型
typedef enum {
    /// 自动
    TVKMediaFormatAuto,
    /// 分片MP4地址
    TVKMediaFormatMultiMp4,
    /// 整片MP4地址
    TVKMediaFormatOneMp4,
    /// HLS
    TVKMediaFormatHLS,
    /// FLV
    TVKMediaFormatFLV,
    /// rtmp
    TVKMediaFormatRTMP,
    /// dash,暂不支持，先和后台约定了格式定义，后续如果支持不用在添加
    TVKMediaFormatDASH,
    /// webrtc,暂不支持，先和后台约定了格式定义，后续如果支持不用在添加
    TVKMediaFormatWebRTC,
    /// trtc,暂不支持，先和后台约定了格式定义，后续如果支持不用在添加
    TVKMediaFormatTRTC,
} TVKMediaFormat;

/// 直播格式能力值，同时与后台返回的stream(流类型)值相同
typedef NS_ENUM(NSInteger, TVKLiveFormat) {
    /// 非法格式
    TVKLiveFormatInvalid = 0x00,
    /// FLV
    TVKLiveFormatFLV = 0x01,
    /// HLS
    TVKLiveFormatHLS = 0x02,
    /// rtmp
    TVKLiveFormatRTMP = 0x04,
    /// dash，暂不支持，先和后台约定好了能力值定义
    TVKLiveFormatDASH = 0x08,\
    /// webtrc，暂不支持, 先和后台约定好了能力值定义
    TVKLiveFormatWebRTC = 0x10,
    /// trtc，暂不支持, 先和后台约定好了能力值定义
    TVKLiveFormatTRTC = 0x20,
};

/// 广告类型
typedef enum TVKPlayerAdType {
    /// 未知广告类型
    TVKPlayerAdTypeNone = 0,
    /// 前贴片
    TVKPlayerAdTypePre,
    /// 中插片
    TVKPlayerAdTypeMid,
    /// 后贴片
    TVKPlayerAdTypePostroll,
} TVKPlayerAdType;

/*** 视频清晰度 ***/
/// 自动，由后台决定返回的清晰度（分段MP4或HLS） （非自适应）
static NSString *const kTVKMediaDefinitionAuto = @"auto";
/// 智能清晰度，自适应切清晰度模式
static NSString *const kTVKMediaDefinitionAdaptive = @"adaptive";
/// 纯音频
static NSString *const kTVKMediaDefinitionAudio = @"audio";
/// 流畅（分段MP4或HLS）
static NSString *const kTVKMediaDefinitionMSD = @"msd";
/// 标清（分段MP4或HLS）
static NSString *const kTVKMediaDefinitionSD = @"sd";
/// 高清MP4
static NSString *const kTVKMediaDefinitionMp4 = @"mp4";
/// 高清（分段MP4或HLS）
static NSString *const kTVKMediaDefinitionHD = @"hd";
/// 超清（分段MP4或HLS）
static NSString *const kTVKMediaDefinitionSHD = @"shd";
/// 全高清
static NSString *const kTVKMediaDefinitionFHD = @"fhd";
/// 全高清高码率
static NSString *const kTVKMediaDefinitionFHD10M = @"fhd10m";
/// 超高清
static NSString *const kTVKMediaDefinitionUHD = @"uhd";
/// hdr
static NSString *const kTVKMediaDefinitionHDR = @"hdr";
/// hdr10
static NSString *const kTVKMediaDefinitionHDR10 = @"hdr10";
/// 杜比视界
static NSString *const kTVKMediaDefinitionDOLBY = @"dolby";
/// 臻彩4k https://tapd.woa.com/qqvideo_prj/markdown_wikis/show/#1210114481001145039
static NSString *const kTVKMediaDefinitionSUHD = @"suhd";

/***软字幕语种 注:现只列举了下面的几个语种，如需其他语种或者咨询详细最新的语种支持，可以咨询后台willqqsun***/
/// 中文
static NSString *const kTVKMediaSubtitleLanguageCh = @"ch";
/// 英语
static NSString *const kTVKMediaSubtitleLanguageEng = @"eng";
/// 泰文
static NSString *const kTVKMediaSubtitleLanguageThai = @"thai";
/// 中英文
static NSString *const kTVKMediaSubtitleLanguageChAndEng = @"ch_eng";
/// 关闭字幕
static NSString *const kTVKMediaSubtitleLanguageNone = @"none";

/// 播放模式
/// 快速出图模式.不进行同步
static NSString *const kTVKPlayModeVideoCapture = @"video_capture";

/***SDK属性的key***/
/// 预览模式  value为"1"时，开启预览模式，"0"关闭预览模式  不设置默认关闭预览模式
FOUNDATION_EXPORT NSString *const kTVKSDKPropertyMapKeyEnablePreviewMode;
/// app侧设置三端一码实验id的key
FOUNDATION_EXPORT NSString *const kTVKSDKPropertyMapKeyFrameworkExpId;

/***extraRequestParamsMap中的Key***/
/// 秒播用previd
FOUNDATION_EXPORT NSString *const kTVKMediaInfoExtraRequestMapKeyPreVid;

/***configMap中的Key***/
/// 秒播是否使用后台返回的跳过片头片尾
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeySkipStartAndEnd;
/// 强制使用App传入的跳过片头片尾，主要为秒播使用
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyForceUseAppSkipStartAndEnd;
/// 跳过片头片尾的策略，"0"为强制使用app传入的片头片尾，"1"为根据vinfo和外部传入综合决策，默认为"0"。目前主要为非秒播使用
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeySkipStartEndStrategy;
/// 直播类型
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyLiveType;
/// 历史vid history_vid
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyHistoryVid;
/// 是否允许精准起播
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnableAccurateStartPos;
/// 外部指定请求帧率
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyPlayerRequestFrameRate;
/// 指定playType为TVKPlayTypeExternalUrl时，选择TVKExternalUrlBusinessType填入
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyExternalUrlBusinessType;
/// 实验id，字符串中内容为数字
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyAppTestId;
/// AirPlay
FOUNDATION_EXPORT NSString *const kTVKMediaPlayerSceneAirPlay;
/// 画中画，iOS14之后iPhone也可以使用画中画
FOUNDATION_EXPORT NSString *const kTVKMediaPlayerScenePictureInPicture;
/// 是否开启音频帧回调
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnableAudioFrameOut;
/// 是否开启空间音频能力
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnableXRSpatialAudio;
/// 是否开启ts转fmp4格式转换，1:开启，0:关闭
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnableTS2FMP4Convert;
/// 是否开启字幕数据回调
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnableSubtitleDataOut;
/// 是否强制走AVPlayerViewController
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyForceAVPlayerVideoView;
/// 设置透出字幕数据类型「0 ：透出文本类型字幕，1 ：透出图片类型字幕，默认为 -1」
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeySubtitleDataType;
/// 是否在沉浸式空间播放视频
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyIsXRVideoImmersive;
/// 回调的音频数据采样率
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyAudioSampleRateHz;
/// 回调的音频帧数帧个数
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyAudioNbSamplesPerChannel;
/// 是否开启视频帧回调.
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnableVideoFrameOut;
/// 回调的视频帧格式。
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyOutVideoFramePixelFormat;
/// 配置当前播放是否是真实用户点击播放. 默认为 "1" 即真实用户点击播放 1 真实用户点击播放，0 非真实用户播放（例如业务预加载播放）
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyRealUserPlay;
/// 回调的音频声道布局，取值为TVKAudioChannelLayout枚举定义，例如@(TVKAudioChannelMono).stringValue
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyAudioChannelLayout;
/// 回调的音频格式，取值为TVKAudioSampleFormat枚举定义，例如@(TVKAudioSampleFormatS16).stringValue
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyAudioSampleFormat;
/// 播放媒体源为flv流时, 如果需要onVideoSizeChanged回调，该值需传"1“, 默认为"0"不用设置.
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyUrlFlvVideoSizeChanged;
/// 起播时如果传入startpositon超出试看范围，是否纠正到试看起点。1：不纠正，直接抛错  0（默认）纠正到试看起点开始播
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyDisableStartPositionCorrection;
/// 业务定制的sei，原始数据是int数组，需要转为字符串设置进来，各个数之间用","间隔，例如:数组[1, 2, 3]->@"1,2,3"
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyCustomizedSeiList;
/// 是否是外部拉起 1:外部拉起  0：非外部拉起
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyIsFirstBootFromOtherApp;
/// 试看场景是否启用精准起播  1：开启 0：关闭   默认开启
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnablePreviewAccurateStart;
/// 开启多网卡  1：开启  0：关闭  默认关闭
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyMultiNetworkCardOpen;
/// 设置这次播放需要使用的下载组件配置，当前播放要使用的下载侧bizId
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyDownloadProxyBizID;
/// 场景ID，方便统计场景流量。该KEY对应的值为string类型，业务方自定义，字符串类型
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeySceneId;
/// pcdn雾计算ID，方便各业务方单独进行雾计算结算（区分广告与正片），字符串类型
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyPcdnChargeId;
/// 存储路径，主要是缓存完成后需要移动到磁盘的路径，由业务方提供，有拷贝到磁盘需求时才设置，字符串类型
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeySavePath;
/// 是否优先缓存拷贝，有拷贝到磁盘需求时才设置.1表示移动文件时使用拷贝，本地目录文件还可以提供P2P上传，0表示使用rename方式移动，不能提供P2P，默认是0
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyCacheCopyFirst;
/// 是否允许TVKPlayer绘制水印  0：关闭水印绘制   1：开启水印绘制
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnableShowWatermark;
/// 是否在画中画中起播，用于画中画自动续播场景   1:是在画中画起播   0 :不是   不设置默认为0
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyOpenWithPictureInPicture;
/// 设置画中画里是否显示快进快退按钮 1：显示快进快退按钮 0：不显示
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnablePictureInPictureSeekButton;
/// 配置dlna投射外部设备Hevc能力。仅通过ITVKQQLiveAssetRequester请求dlna地址时生效。value为TVKHEVCLevel类型对应的字符串。如@(TVKHEVCLevelUnSupported).stringValue
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyDlnaDeviceHevcLevel;
/// 是否需要在播放过程中把数据下载到本地   1：下载到本地   0：不下载   不设置默认为0 设为其它非法值当做0值处理,仅点播可用
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyNeedCache;
/// 设置是否开启画中画无缝拉起能力   1：开启  0：不开启   如果打开 起播后播放器内会做一些准备工作
/// 要使用startPictureInPictureSeamless接口主动拉起画中画或者要开启退后台自动画中画功能，这个参数必须设置为1，否则一定失败,tvk回调failToStartPictureInPictureWithError
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnableOpenPictureInPictureSeamless;
/// 腾讯云xp2p所使用的key，播放器不理解，透传给下载组件
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyXp2pKey;
/// airplay场景  0：非投射，默认值 不用传  1：AirPlayStreaming  2：AirPlayMirroring
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyAirplayMode;
/// 是否通过airplay协议将音频输出到homepod/homepod mini等设备上进行音频输出。"0" 否。"1" 是。
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyIsAirplayAudioOutput;
/// 是否允许低清晰度起播。 "0" 否。"1" 是。  默认否
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnableLowDefinitionOpen __attribute__((deprecated("低清晰度起播功能已下线")));
/// 业务播放场景下对接的后处理类型（不是具备后处理能力），值为string， 按位表示不同的后处理能力（目前仅包含了TVM超分，后续扩展）
/// 0：此播放场景下没有对接任何后处理能力
/// 1:  此播放场景下对接了TVM超分（不代表一定可以使用TVM超分能力，是否能使用由播放器自身能力和是否下载了超分模型决定）
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEffectEnableOperation;
/// 业务播放场景下，用户使能的不需要vip权限的后处理类型开关状态（不代表用户使用后处理能力），
/// 值为string， 按位表示不同的后处理能力（目前仅包含了TVM超分，后续扩展）
/// 0：用户开关状态，不使能任何后处理能力
/// 1:  用户开关状态，TVM超分（不代表一定可以使用TVM超分能力，是否能使用由播放器自身能力和是否下载了超分模型决定）
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEffectUserEnableOperation;
/// 业务播放场景下，用户使能的需要vip权限的后处理类型开关状态（因为开关独立，所以需要两个key传递），
/// 值为string， 按位表示不同的后处理能力（目前仅包含了TVM超分，后续扩展）
/// 0：用户开关状态，不使能任何后处理能力
/// 1:  用户开关状态，TVM超分（不代表一定可以使用TVM超分能力，是否能使用由播放器自身能力和是否下载了超分模型决定）
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyVipEffectUserEnableOperation;
/// 业务页面ID，用于区分不同播放页面，如频道页，底层页等
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyPageId;
/// 选中的字幕语言ID 业务需要记住最新一次的TVKNetVideoInfo.currentSubtitle.langId
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyLangId;
/// 禁用软字幕 @"1"： 禁用，@"0"：不禁用，默认不禁用
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyDisableSoftSubtitle;
/// 播放模式
/// kTVKPlayModeVideoCapture : 快速出图模式.不进行同步
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyPlayMode;
/// 是否允许tvk播放器对象复用。@"1"：支持，@"0"：不支持，默认不支持。
///  tvk init时候会创建广告对象，如果要支持复用的话，tvk内部会在open时候再次创建新的广告对象，因为广告QAD的对象不支持复用。 这会影响open的速度
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeySupportTVKReuse;
/// 平台特有的功能，不同平台可能有不同的功能，需要根据具体的功能来设置。
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyPlatformSpecificFeature;
/// 是否为slaveVideoTrackPlayer播放
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyIsSlaveVideoTrackPlayer;
/// 广告的session id 置于TVKPlayerVideoInfo中用于videoTrackPlayer使用
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyAdSessionId;
/// adInfo的json数据
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyAdInfoJson;
/// 大同数据上报key，大同唯一标识，用于大同上报播放数据与业务数据绑定
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyDatongReportKey;
/// 是否把4k sdr从臻彩4k清晰度里分离出来作为单独清晰度
/// @"1" 4k sdr作为单独一档清晰度， @“0” : 4k sdr属于臻彩4k清晰度档 , 默认为@“0”
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnableSdrUhdDefinition;
/// 存在软字幕的情况下，是否开启字幕渲染信息的回调，默认关闭
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyEnableSubtitleRenderInfoCallback;
/// 透明视频的Alpha通道分隔模式
/// @"0" : LR左右模式，图像Alpha通道和RGB通道为1:1，左侧为Alpha通道，右侧为RGB通道
/// @"1" : LR左右模式，图像RGB通道和Alpha通道为1:1，左侧为RGB通道，右侧为Alpha通道
/// @"5" : LR左右模式，图像RGB通道和Alpha通道为3:1，左侧四分之三为RGB通道，右侧四分之一为Alpha通道
/// 详细内容参考{@link ITVKVideoFx.h.TVKAlphaSeparateMode}
FOUNDATION_EXPORT NSString *const kTVKMediaInfoConfigMapKeyAlphaSeparateMode;

/// 播放器播放视频时的状态
typedef enum {
    /// 初始状态
    TVKMediaPlayerStateUnknown = 0,
    /// 正片获取信息中
    TVKMediaPlayerStatePreparing,
    /// 正片播放准备完毕
    TVKMediaPlayerStatePrepared,
    /// 正片播放中
    TVKMediaPlayerStatePlaying,
    /// 正片播放用户行为导致暂停
    TVKMediaPlayerStateUserPaused,
    /// 正片播放中断
    TVKMediaPlayerStateInterrupt,
    /// 正片播放停止(用户主动调用停止后，进入此状态)
    TVKMediaPlayerStateStopped,
    /// 正片播放完毕
    TVKMediaPlayerStateComplete,
    /// 正片播放失败
    TVKMediaPlayerStateError,
} TVKMediaPlayerState;

/// 播放器播放视频时的发生的事件信息。例如缓冲信息，发生缓冲时，app可以进行UI提示
/// 个别事件抛出时会携带额外参数，详情请见下面的注释
typedef enum {
    /// 初始状态
    TVKMediaPlayerEventUnkown = 0,
    /// 首帧渲染
    TVKMediaPlayerEventFirstFrameRendered = 1,
    /// 播放器发生缓冲.
    TVKMediaPlayerEventBufferingStart = 2,
    /// 播放器缓冲结束.
    TVKMediaPlayerEventBufferingEnd = 3,
    /// 播放器开始处理seek操作
    TVKMediaPlayerEventSeekingStart = 4,
    /// 播放器处理seek结束
    TVKMediaPlayerEventSeekingEnd = 5,
    /// 切换清晰度开始. 事件中的额外参数用于是否要指定无缝切换等.为nil时，表示进行非无缝切换.字典内容格式：
    /// key1: kTVKVideoSwitchDefinitionTypeKey 切换清晰度的类型，直接使用TVKPlayerDefine.h中的kTVKVideoSwitchDefinitionTypeKey定义即可。
    /// value1: TVKVideoSwitchDefinitionType列举值的NSNumber类型
    TVKMediaPlayerEventSwitchDefinitionStart = 6,
    /// 切换清晰度结束. 事件中的额外参数用于是否要指定无缝切换等.为nil时，表示进行非无缝切换.字典内容格式：
    /// key1: kTVKVideoSwitchDefinitionTypeKey 切换清晰度的类型，直接使用TVKPlayerDefine.h中的kTVKVideoSwitchDefinitionTypeKey定义即可。
    /// value1: TVKVideoSwitchDefinitionType列举值的NSNumber类型
    TVKMediaPlayerEventSwitchDefinitionEnd = 7,
    /// 自适应切换清晰度开始. 事件中的额外参数用于是否要指定无缝切换等.为nil时，表示进行非无缝切换.字典内容格式：
    /// key1: kTVKVideoSwitchDefinitionTypeKey 切换清晰度的类型，直接使用TVKPlayerDefine.h中的kTVKVideoSwitchDefinitionTypeKey定义即可。
    /// value1: TVKVideoSwitchDefinitionType列举值的NSNumber类型
    TVKMediaPlayerEventAdaptiveSwitchDefinitionStart = 8,
    /// 自适应切换清晰度结束. 事件中的额外参数用于是否要指定无缝切换等.为nil时，表示进行非无缝切换.字典内容格式：
    /// key1: kTVKVideoSwitchDefinitionTypeKey 切换清晰度的类型，直接使用TVKPlayerDefine.h中的kTVKVideoSwitchDefinitionTypeKey定义即可。
    /// value1: TVKVideoSwitchDefinitionType列举值的NSNumber类型
    TVKMediaPlayerEventAdaptiveSwitchDefinitionEnd = 9,
    /// reach the #EXT-QQHLS-AD tag when playing live hls
    TVKMediaPlayerEventReachHLSAdTag = 10,
    /// 回抛sei, extrainfo为TVKSeiInfo定义
    /// codecType:参考TVKVideoCodecType, 目前支持avc, hevc
    /// seiType:参考TVKVideoSeiType, 目前支持pictiming, unregisetered user data
    /// seiData:  回抛内容
    TVKMediaPlayerEventObjectVideoSei = 11,
    /// 加载字幕结束
    TVKMediaPlayerEventLoadSubTitleEnd = 12,
    /// 切换字幕开始
    TVKMediaPlayerEventSwitchSubTitleStart = 13,
    /// 切换字幕结束
    TVKMediaPlayerEventSwitchSubTitleEnd = 14,
    /// 一次循环播放结束，只有设置了循环播的时候会抛这个事件
    TVKMediaPlayerEventOneLoopComplete = 15,
    /// 新的视频播放开始，仅在互动模式下才会有这个信息通知.字典内容格式：
    /// key1: kTVKMediaInfoKey 将要播放的视频，直接使用TVKPlayerDefine.h中的kTVKMediaInfoKey定义即可。
    /// value1: TVKMediaInfo实例
    TVKMediaPlayerEventNewVideoPlayStart = 16,
    /// 当前视频播放结束，仅在互动模式下才会有这个信息通知.字典内容格式：
    /// key1: kTVKMediaInfoKey 当前播放完毕的视频，直接使用TVKPlayerDefine.h中的kTVKMediaInfoKey定义即可。
    /// value1: TVKMediaInfo实例
    TVKMediaPlayerEventCurrentVideoPlayComplete = 17,
    /// 音频轨道开始切换
    TVKMediaPlayerEventAudioSwitchTrackStart = 18,
    /// 音频轨道结束切换
    TVKMediaPlayerEventAudioSwitchTrackEnd = 19,
    /// RefreshPlayerWithReopen开始
    TVKMediaPlayerEventRefreshPlayerWithReopenStart = 20,
    /// RefreshPlayerWithReopen结束
    TVKMediaPlayerEventRefreshPlayerWithReopenEnd = 21,
    /// RefreshPlayer开始
    TVKMediaPlayerEventRefreshPlayerStart = 22,
    /// RefreshPlayer结束
    TVKMediaPlayerEventRefreshPlayerEnd = 23,
    /// 播放器内部产生了一个新的flowId
    TVKMediaPlayerEventGenerateFlowId = 24,
    /// 富媒体准备就绪
    TVKMediaPlayerEventRichMediaSynchronizerPreapred = 25,
    /// 富媒体准备失败
    TVKMediaPlayerEventRichMediaSynchronizerPrepareFailed = 26,
    /// 当后台返回的sshot字段表示不能进行系统录屏时，此时进行系统录屏，播放画面会主动黑屏
    TVKMediaPlayerEventScreenCaptureInvalid = 27,
    /***AB实验通知事件***/
    /// AB实验曝光
    /// 参数extrainfo为字典，内容格式为：
    /// key:实验名称，NSString类型
    /// value:实验ID,  NSNumber类型
    TVKMediaPlayerEventAbTestExposed = 28,
    /// AB实验停止曝光
    TVKMediaPlayerEventAbTestUnExposed = 29,
    /// 多网卡通知app命中低速, 此时外部可以调用informRealTimeInfoChangedWithInfoKey（TVKRealTimeInfoKeyMultiNetworkCardOpen）开启多网卡来改善网络状态
    /// 开启后下载组件可选择用wifi或蜂窝同时下载的方式提高下载速度。
    /// 网卡使用状态发生变化时，通过拋 TVKMediaPlayerEventMultiNetworkCardState事件告知正在使用的是哪个网卡
    TVKMediaPlayerEventMultiDetectedNetworkCardAndLowSpeed = 30,
    /// 多网卡通知网卡使用状态.参数extrainfo为字典，内容格式为：
    /// key: kTVKMultiNetworkCardStateKey
    /// value: 为TVKMultiNetworkCardState枚举值： 0双通道未开启，1双通道开启wifi下载，2双通道开启wifi和蜂窝网卡并行下载
    TVKMediaPlayerEventMultiNetworkCardStateChange = 31,
    /// 当前条件不再支持后处理时，内部自动断开后处理连接，并抛出该事件通知
    /// 已知场景：当APP进入后台时，并处于画中画播放时，会自动断开连接。
    /// 参数extrainfo为数组，包含断开的后处理特效类型TVKVideoFxType
    TVKMediaPlayerEventPostProcessorConnectInterrupt = 32,
    /// 后处理恢复连接。由于内部原因断开后处理连接后，当条件恢复时，内部自动恢复之前连接的后处理特效
    /// 已知场景：当app进后台画中画，内部自动断开连接，回到前台后，会自动恢复连接
    /// 参数extrainfo为数组，包含恢复的后处理特效类型TVKVideoFxType
    TVKMediaPlayerEventPostProcessorConnectRecover = 33,
    /// 播放器每秒会计算当前的视频丢帧率，若丢帧率超过20%，则抛出该事件通知
    /// 参数extrainfo为字典，内容格式为：
    /// key1:kTVKVideoFrameDropRateKey，视频丢帧率，直接使用TVKPlayerDefine.h中的kTVKVideoFrameDropRateKey定义即可
    /// value1:丢帧率，取值范围(0.2f, 1.0f]，NSNumber类型
    TVKMediaPlayerEventVideoHighFrameDropRateAlert = 34,
    /// 播放器每秒会计算当前的视频帧率，若帧率低于15fps，则抛出该事件通知
    /// 参数extrainfo为字典，内容格式为：
    /// key1:kTVKVideoFrameRateKey，视频帧率，直接使用TVKPlayerDefine.h中的kTVKVideoFrameRateKey定义即可
    /// value1:帧率，取值范围[0.0f, 15.0f)，NSNumber类型
    TVKMediaPlayerEventVideoLowFrameRateAlert = 35,
    /// 秒播时使用了本地离线资源
    TVKMediaPlayerEventQuickPlayWithOfflineResource = 36,
    /// 下载进度更新。携带参数为TVKDownloadProgressInfo实例。
    TVKMediaPlayerEventDownloadProgressUpdate = 37,
    /// 请求更新用户信息，收到该事件后，app需要调用setUserInfo接口更新用户信息
    TVKMediaPlayerEventRequestUpdateUserInfo = 38,
    /// 多音轨是否支持状态变化回调
    /// 参数为NSNumber，实际内容为BOOL型，表示当前是否支持多音轨能力，直接取boolValue即可
    TVKMediaPlayerEventMultiAudioSupportStatusChanged = 39,
    /// 伪直播每个vid进入播放时回调，包括第一个vid。 携带参数为这个vid的TVKNetVideoInfo
    TVKMediaPlayerEventSimulatedLiveBeginningOfVid = 40,
    /// vinfo回包http header更新, 参数extrainfo是http header, 类型是字典NSDictionary *   https://iwiki.woa.com/p/4009690864
    TVKMediaPlayerEventGetVinfoResponseHeaders = 41,
    /// 系统播放器因为外部保护不足而黑屏时和解除黑屏时回调
    /// extraInfo  是NSNumber,  表示是否黑屏，1 黑屏  0 解除黑屏
    TVKMediaPlayerEventAVPlayerOutputObscuredDueToInsufficientExternalProtectionChanged = 42,
#if TARGET_OS_VISION
    /// 系统播放器AVPlayer可用时的回调
    TVKMediaPlayerEventAVPlayerAvailable = 43,
    /// 系统播放器AVPlayer不可用时的回调
    TVKMediaPlayerEventAVPlayerUnavailable = 44,
#endif
    /// 视频解码器类型变更
    /// 参数extrainfo为字典，内容格式为：
    /// key1:kTVKVideoDecoderType，视频解码器类型，直接使用TVKPlayerDefine.h中的kTVKVideoDecoderType定义即可
    /// value1:解码器类型，NSNumber类型, 参考:
    /// 未知解码类型
    /// TPVideoDecoderTypeUnknown  = -1,
    /// 使用FFmpeg解码视频（软解）
    /// TPVideoDecoderFFmpeg       = 101,
    /// 使用VideoToolbox解码视频（硬解）
    /// TPVideoDecoderVideoToolbox = 103,
    /// 使用standalone 解码视频（软解）
    /// TPVideoDecoderStandalone   = 104,
    /// 使用 MediaLabVR 解码视频（内部调用的VideoToolbox）
    //// TPVideoDecoderMediaLabVR   = 107,
    TVKMediaPlayerEventVideoDecoderTypeChanged = 45,

    /// 视频元数据变化事件
    /// 参数extrainfo类型为NSArray<NSDictionnary<NSString *, NSString *> *>
    /// 数组中每个NSDictionnary只包含一对键值对，用这种形式是为了严格保证顺序
    /// 参数内容为视频的元数据信息，包括像素数、帧率及色域等等
    /// 内容示例如下：
    /// [
    ///   {"有效像素数":  "1920x1080"}
    ///   {"帧率":  "25"}
    ///   {"色域": "ITU-R BT.709"}
    ///   ......
    /// ]
    /// 该事件用于业务更新展示视频元数据以配合检查，业务直接使用字典里的k-v即可，不用理解内容
    TVKMediaPlayerEventVideoMetadataChanged = 46,
    /// 超分状态变更关闭/开启，extrainfo 为NSNumber *,  1: 开启，0：关闭
    TVKMediaPlayerSuperResolutionStateChange = 47,
    /// 更新源开始
    TVKMediaPlayerEventUpdateSourceForCurrentDefinitionStart = 48,
    /// 更新源结束
    TVKMediaPlayerEventUpdateSourceForCurrentDefinitionEnd = 49,
} TVKMediaPlayerEvent;

/// 播放器播放广告时的状态
typedef enum {
    /// 初始状态
    TVKMediaPlayerAdStateUnknown = 0,
    /// 获取信息中
    TVKMediaPlayerAdStatePreparing,
    /// 广告播放准备完毕
    TVKMediaPlayerAdStatePrepared,
    /// 广告处于播放中
    TVKMediaPlayerAdStatePlaying,
    /// 广告播放中断（由于处于后台，用户点击了广告详情导致的暂停，sdk不会主动播放，app或者用户去主动调用播放开始接口即可）
    TVKMediaPlayerAdStatePaused,
    /// 广告播放结束
    TVKMediaPlayerAdStateEnd,
} TVKMediaPlayerAdState;

/// 拉伸模式
typedef NS_ENUM(NSUInteger, TVKVideoStretchMode) {
    /// 按原始比例缩放，适配videoView大小，未铺满部分添加黑边
    TVKVideoStretchModeAspectFit = 0,
    /// 按原始比例缩放，视频内容铺满videoView，会有一部分在边界之外
    TVKVideoStretchModeAspectFill = 1,
    /// 不按比例缩放，视频内容铺满videoview，某个方向可能会拉伸，但不会超出videoView边界
    TVKVideoStretchModeFullScreen = 2,
};

/// 无缝切换清晰度形式
typedef NS_ENUM(NSUInteger, TVKVideoSwitchDefinitionType) {
    /// 正常的传统切换清晰度
    TVKVideoSwitchDefinitionTypeNormal = 0,
    /// 无缝切换清晰度
    TVKVideoSwitchDefinitionTypeSeamless = 1,
};

/// VR Mode
typedef NS_ENUM(NSUInteger, TVKVRMode) {
    /// 单目
    TVKVRModeMonocular,
    /// 双目
    TVKVRModeBinocular,
};

/// seek Mode
typedef NS_ENUM(NSUInteger, TVKSeekMode) {
    /// 普通seek，seek后播放位置可能与指定位置有偏差
    TVKSeekModeNormal = 0,
    /// 精确seek，seek后播放位置准确但可能起播更耗时
    TVKSeekModeAccuratePosition,
};

/// 直播信息cgi返回的错误码
typedef NS_ENUM(NSUInteger, TVKLiveMediaPlayError) {
    /// 数据正确返回
    TVKLiveMediaPlayErrorOK = 0,
    /// 需要排队
    TVKLiveMediaPlayErrorNeedToWait = 139,
    /// 请求付费鉴权模块失败（网络超时或解析出错）提示试看已结束购买
    TVKLiveMediaPlayErrorAuthFailedInPay = 1323,
    /// 无用户登录信息（登录态cookie缺少必填字段） 提示登录 试看
    TVKLiveMediaPlayErrorLostLoginInfo = 1325,
    /// 请求登录验证模块失败（网络超时或解析出错）提示登录 试看
    TVKLiveMediaPlayErrorLoginInfoVerifyFailed = 1328,
    /// 当前节目未付费 提示直接购买
    TVKLiveMediaPlayErrorNoPay = 1330,
    /// 用户未登录 提示登录 试看
    TVKLiveMediaPlayErrorNoLogin = 1331,
    /// CKEY验证失败
    TVKLiveMediaPlayErrorCKEYVerifyFailed = 1332,
    /// 试看次数达到上限 提示试看已结束购买
    TVKLiveMediaPlayErrorTryWatchChanceUsed = 1345,
    /// 试看计数失败（网络错误） 重试
    TVKLiveMediaPlayErrorGetPreviewCountFailed = 1347,
    /// 微信登录验证失败（网络错误） 试看
    TVKLiveMediaPlayErrorWeixinVerifyFailed = 1348,
    /// 微信登录验证超时
    TVKLiveMediaPlayErrorWeixinVerifyTimeOut = 1349,
    /// 获取试看信息失败（网络错误） 重试
    TVKLiveMediaPlayErrorGetPreviewInfoFailed = 1350
};

/// 免流状态 freeflowType
typedef NS_ENUM(NSUInteger, TVKFreeFlowType) {
    /// 不免流
    TVKFreeFlowTypeNone = 0,
    /// 联通免流
    TVKFreeFlowTypeUnicom = 1,
    /// 移动免流
    TVKFreeFlowTypeMobile = 2,
    /// 电信免流
    TVKFreeFlowTypeTelecom = 3,
};

/// 实时信息的key定义
typedef NS_ENUM(NSUInteger, TVKRealTimeInfoKey) {
    /// 主要用于多个播放器，当前播放器是否是正在播放的播放器还是预加载或者非活跃播放的播放器，即当前播放器是否是active状态。用于下载策略优化等。
    TVKRealTimeInfoKeyIsPreload = 0,
    /// 跳过片头片尾的时间
    TVKRealTimeInfoKeySkipPos = 1,
    /// 用来控制音频后台播放的打开和关闭，音频后台播放是指虽然不是audio清晰度，切到后台时依然播放声音。
    TVKRealTimeInfoKeyBackgroundAudioPlay = 2,
    /// 用来控制是否可以在播放当前视频时，同时设置下一个视频，在播放完毕当前视频，可以无缝切换到另一个视频。
    TVKRealTimeInfoKeyEnableSetNextMediaInfo = 3,
    /// 多网卡开关
    TVKRealTimeInfoKeyMultiNetworkCardOpen = 4,
    /// 退后台自动画中画开关
    TVKRealTimeInfoKeyEnableAutoPictureInPicture = 5,
    /// 当前播放是否是真实用户点击播放 value类型：NSNumber 0：非真实用户点击播放（例如业务预加载播放） 1: 真实用户点击播放 默认为1
    TVKRealTimeInfoKeyRealUserPlay = 6,
    /// 自适应清晰度模式 ，  value类型：NSNumber (int)
    TVKRealTimeInfoKeyAdaptiveMode = 7,
};

typedef NS_ENUM(NSUInteger, TVKSetNextMediaInfoSupportLevel) {
    /// 未知，一般是当前播放没有起播，无法判断是否支持
    TVKSetNextMediaInfoSupportLevelUnknow = 0,
    /// 当前播放支持设置下一个视频
    TVKSetNextMediaInfoSupportLevelSupport = 1,
    /// 当前设备不支持,调用setNextMediaInfo无法生效，需要更好的设备
    TVKSetNextMediaInfoSupportLevelDeviceUnsupport = 2,
    /// 当前片源不支持,调用setNextMediaInfo无法生效。后台的片源是fairplay加密等原因导致
    TVKSetNextMediaInfoSupportLevelVideoUnsupport = 3,
    /// 当前清晰度不支持，可以考虑切换到更低的清晰度，比如1080P(含)之下的清晰度
    TVKSetNextMediaInfoSupportLevelDefinitionUnsupport = 4,
};

FOUNDATION_EXPORT NSString *const kTVKSkipStartPosKey;
FOUNDATION_EXPORT NSString *const kTVKSkipEndPosKey;

/// 播放事件定义 ， 包含 （正片事件 ， 视频类广告事件）
typedef NS_ENUM(NSUInteger , TVKPlayerEvent) {
    /// 未知事件
    TVKPlayerEventUnknown = 0,
    /// 广告事件：广告开始加载
    /// 参数：@{kTVKPlayerEventParamAdTypeKey: @(TVKAdType)}
    TVKPlayerEventAdPreparing = 1,
    /// 广告事件：广告加载完成和总时长
    /// 参数：@{kTVKPlayerEventParamAdTypeKey: @(TVKAdType)}
    /// 参数：@{kTVKPlayerEventParamAdDurationKey: NSNumber*}
    TVKPlayerEventAdPrepared = 2,
    /// 广告事件：广告播放中
    /// 参数：@{kTVKPlayerEventParamAdTypeKey: @(TVKAdType)}
    TVKPlayerEventAdPlaying = 3,
    /// 广告事件：广告暂停播放
    /// 参数：@{kTVKPlayerEventParamAdTypeKey: @(TVKAdType)}
    TVKPlayerEventAdPaused = 4,
    /// 广告事件：广告停止播放
    /// 参数：@{kTVKPlayerEventParamAdTypeKey: @(TVKAdType)}
    TVKPlayerEventAdStopped = 5,
    /// 广告事件：广告播放完成
    /// 参数：@{kTVKPlayerEventParamAdTypeKey: @(TVKAdType)}
    TVKPlayerEventAdComplete = 6,
    /// 广告事件：广告播放错误
    /// 参数：@{kTVKPlayerEventParamAdTypeKey: @(TVKAdType)}
    TVKPlayerEventAdError = 7,
    /// 正片事件：打开播放器
    /// 参数：@{kTVKPlayerEventParamMediaInfoKey : TVKMediaInfo*}
    /// 参数：@{kTVKPlayerEventParamUserInfoKey : TVKUserInfo*}
    TVKPlayerEventOpenMediaPlayer = 8,
    ////正片事件：正片视频开始加载
    TVKPlayerEventVideoPreparing = 9,
    /// 正片事件：正片视频加载完成
    /// 参数：@{kTVKPlayerEventParamVideoDurationKey : NSNumber*}
    TVKPlayerEventVideoPrepared = 10,
    /// 正片事件：正片视频播放中
    TVKPlayerEventVideoPlaying = 11,
    /// 正片事件：正片视频暂停播放
    TVKPlayerEventVideoPaused = 12,
    /// 正片事件：正片视频停止播放
    TVKPlayerEventVideoStopped = 13,
    /// 正片事件：正片视频播放完成
    TVKPlayerEventVideoComplete = 14,
    /// 正片事件：正片视频播放错误
    TVKPlayerEventVideoError = 15,
    /// 正片事件：上报透传参数更新
    /// 参数：@{kTVKPlayerEventParamReportInfoEventKey : @(TVKReportInfoEvent)}
    /// 参数：@{kTVKPlayerEventParamReportInfoMapKey : NSDictionary*}
    TVKPlayerEventUpdateReportParam = 16,
    /// 正片事件：获取到视频信息
    /// 参数：@{kTVKPlayerEventParamNetVideoInfoKey : TVKNetVideoInfo*}
    TVKPlayerEventNetVideoInfo = 17,
    /// 正片视频video首帧渲染
    TVKPlayerEventFirstVideoFrameRendered = 18,
    ///正片事件：切换清晰度
    TVKPlayerInfoEventVideoSwitchDefinitionEnd = 19,
    ///正片事件：切换音轨
    TVKPlayerInfoEventAudioTrackSwitchEnd = 20,
    ///正片事件：改变音频解码模式
    ///参数：@{kTVKPlayerEventParamAudioDecoderTypeKey : (NSNumber *)DecoderType}
    TVKPlayerInfoEventAudioDecoderModeDidChange = 21
};

/// 与AVPlayer的AVAudioSpatializationFormats一一对应
typedef NS_ENUM(NSUInteger, TVKAudioSpatializationFormats) {
    TVKAudioSpatializationFormatsNone = 0UL,
    TVKAudioSpatializationFormatsMonoAndStereo = 0x3UL,
    TVKAudioSpatializationFormatsMultichannel = 0x4UL,
    TVKAudioSpatializationFormatsMonoStereoAndMultichannel = 0x7UL
};

/// 播放类型为ExternalUrl时的特殊业务场景
typedef NS_ENUM(NSInteger, TVKExternalUrlBusinessType) {
    /// 无特殊场景
    TVKExternalUrlBusinessTypeNone = 0,
    /// 杜比Vision审核场景
    TVKExternalUrlBusinessTypeDolbyAudit = 1,
};

/// 投射场景
typedef NS_ENUM(NSInteger, TVKAirplayMode) {
    TVKAirplayModeNone = 0,
    TVKAirplayModeStreaming = 1,
    TVKAirplayModeMirroring = 2,
};

/// 加密请求配置类型，定义为bitset值
typedef NS_OPTIONS(NSInteger, TVKEaType) {
    TVKEaTypeNone = 0,
    TVKEaTypeEncrypVinfo = 0x01,
};

/// 多网卡状态
typedef NS_ENUM(NSUInteger, TVKMultiNetworkCardState) {
    /// 双通道未开启
    TVKMultiNetworkCardStateNone = 0,
    /// 双通道开启wifi下载
    TVKMultiNetworkCardStateWifi = 1,
    /// 双通道开启wifi和蜂窝网卡并行下载
    TVKMultiNetworkCardStateWifiAndCellularNetworkCard = 2,
};

typedef NS_ENUM(NSUInteger, TVKReportInfoEvent) {
    /// 所有的事件都上报(指vv、vod、live)，除了飞天上报以外
    TVKReportInfoEventAllExceptFeiTian,
    /// 用于飞天上报
    TVKReportInfoEventFeiTian,
};

typedef NS_ENUM(NSUInteger, TVKPlayerType) {
    TVKPlayerTypeUndefined = 0,
    /// 自研播放器
    TVKPlayerTypeThumbPlayer,
    /// 系统播放器
    TVKPlayerTypeSystemPlayer,
};

/// 支持的HEVC能力值
typedef NS_ENUM(NSUInteger, TVKHEVCLevel) {
    /// 不支持
    TVKHEVCLevelUnSupported = 0,
    /// sd标清
    TVKHEVCLevelSD   = 11,
    /// hd高清
    TVKHEVCLevelHD   = 16,
    /// shd超清
    TVKHEVCLevelSHD  = 21,
    /// fhd全高清
    TVKHEVCLevelFHD  = 26,
    /// uhd超高清4K, Android TVK端有个伪4K，对应能力值是28，iOS端没有
    TVKHEVCLevelUHD  = 33,
};

/**
 getvinfo结果的来源类型，区分后台返回，cache缓存，p2p缓存
 如果是TVKMediaPlayInfoFromDLProxy，那么构建下载组件任务的时候一定设置offline播放
 单独定义来源枚举的目的是为了以后的扩展性
 */
typedef NS_ENUM(NSUInteger, TVKMediaPlayInfoFromType) {
    // 媒体信息来源于外部输入，外部url等方式
    TVKMediaPlayInfoFromUnDefine = 0,
    // 媒体信息来源于后台媒咨
    TVKMediaPlayInfoFromServer = 1,
    // 媒体信息来源于播放器的CGI缓存
    TVKMediaPlayInfoFromCache = 2,
    // 媒体信息来源于下载组件的缓存
    TVKMediaPlayInfoFromDLProxy = 3,
    // 媒体信息来源于Url直出的Xml数据
    TVKMediaPlayInfoFromXmlAsset = 4,
    // 媒体信息来源于多网卡请求
    TVKMediaPlayInfoFromServerByMultiNetWork = 5
};

typedef NS_ENUM(NSUInteger, TVKAudioTrackType) {
    // 外挂音轨
    TVKAudioTrackExternal = 0,
    // 内嵌音轨
    TVKAudioTrackInternal = 1,
};

/// 设备对清晰度支持类型定义。
typedef NS_OPTIONS(NSInteger, TVKDefinitionSupportType) {
    /// 支持
    TVKDefinitionSupportTypeSupport = 0,
    /// 不支持
    TVKDefinitionSupportTypeUnsupport = 0x01,
    /// 由于播放卡顿（性能不足）导致不支持。比如一些低端设备播放4K因为设备性能不足会卡顿
    TVKDefinitionSupportTypeUnsupportWithStuttering = 0x02,
    /// 由于屏幕不满足要求导致不支持。比如HDR清晰度在一些扩展屏上，因为屏幕亮度不够，导致HDR显示效果不好。
    TVKDefinitionSupportTypeUnsupportWithScreenNotSatisfied = 0x04
};


/// 平台特性相关定义
typedef NS_OPTIONS(NSInteger, TVKPlatformSpecificFeature) {
    /// 没有设置该Feature
    TVKPlatformSpecificFeatureUnset = 0x0,
    /// 忽略设备能力值， Mac客户端强制下发uhd(4K)清晰度
    TVKPlatformSpecificFeatureUHDForce = 0x01,
    /// 忽略设备能力值，Mac客户端强制下发TIE(1080+4K)清晰度
    TVKPlatformSpecificFeatureTIE = 0x02
};

/**
 * video codec类型, 与TPPlayerCodecType定义一致
 */
typedef NS_ENUM(NSInteger, TVKVideoCodecType) {
    TVKVideoCodecTypeUnknow = -1,
    TVKVideoCodecTypeAvc = 26,
    TVKVideoCodecTypeHevc = 172,
    TVKVideoCodecTypeVvc = 193,
};
