/************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     ITVKPowerConsumerRegistrationDelegate.h
 * @brief    功耗消耗者注册回调
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/10/12
 * @license     GNU General Public License (GPL)
 ***********************************************************/

#import <Foundation/Foundation.h>
#import "ITVKPowerConsumer.h"

NS_ASSUME_NONNULL_BEGIN

/// @brief 同文件头注释
@protocol ITVKPowerConsumerRegistrationDelegate <NSObject>

- (void)addConsumer:(id<ITVKPowerConsumer>)consumer;
- (void)removeConsumer:(id<ITVKPowerConsumer>)consumer;

@end

NS_ASSUME_NONNULL_END
