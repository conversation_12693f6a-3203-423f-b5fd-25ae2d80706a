/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TVKSubtitleRenderParams.h
 * @brief    字幕渲染控制参数
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/12/26
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 参数标志位，用来标识设置了哪些参数
typedef NS_OPTIONS(int64_t, TVKSubtitleParamFlag) {
    TVKSubtitleParamFlagUnknown = 0,
    TVKSubtitleParamFlagCanvasWidth = (1 << 0),
    TVKSubtitleParamFlagCanvasHeight = (1 << 1),
    TVKSubtitleParamFlagFontSize = (1 << 2),
    TVKSubtitleParamFlagFontColor = (1 << 3),
    TVKSubtitleParamFlagOutlineWidth = (1 << 4),
    TVKSubtitleParamFlagOutlineColor = (1 << 5),
    TVKSubtitleParamFlagLineSpace = (1 << 6),
    TVKSubtitleParamFlagStartMargin = (1 << 7),
    TVKSubtitleParamFlagEndMargin = (1 << 8),
    TVKSubtitleParamFlagVerticalMargin = (1 << 9),
    TVKSubtitleParamFlagFontStyleBold = (1 << 10),
    TVKSubtitleParamFlagFontScale = (1 << 11),
    TVKSubtitleParamFlagShadowColor = (1 << 12),
    TVKSubtitleParamFlagShadowRadius = (1 << 13),
    TVKSubtitleParamFlagShadowOffset = (1 << 14),
};

/// 字幕背景渲染参数
@interface TVKSubtitleBackgroundParams : NSObject
/// 背景框的填充颜色，ARGB格式
/// 如果不设置，默认为透明(0)
@property(nonatomic, assign) int backgroundColor;
/// 背景框相对字幕文本的左右边距, 单位是像素
/// 如果不设置，默认为0
@property (nonatomic, assign) float backgroundHorizontalMargin;
/// 背景框相对字幕文本的上下边距，单位是像素
/// 如果不设置，默认为0
@property (nonatomic, assign) float backgroundVerticalMargin;
/// 背景框边框的颜色，ARGB格式
/// 如果不设置，默认为透明(0),即没有边框
@property (nonatomic, assign) int backgroundBorderColor;
/// 背景框边框的线宽，单位是像素
/// 如果不设置，默认为0
@property (nonatomic, assign) float backgroundBorderWidth;
/// 背景框的圆角半径相对于背景高度高度的比例,取值范围【0-0.5】
/// 如果不设置，默认为0
@property (nonatomic, assign) float cornerRadiusToHeightRatio;

@end

/// 字幕装饰，即在字幕左右两侧增加图片等
@interface TVKSubtitleDecoration : NSObject

/// 字幕左侧图片路径
@property (nonatomic, copy) NSString *leftPicPath;
/// 字幕左侧图片宽度，单位像素
@property (nonatomic, assign) float leftPicWidth;
/// 字幕左侧侧图片高度，单位像素
@property (nonatomic, assign) float leftPicHeight;
/// 左侧图片的左边离字幕左边的距离，单位像素
@property (nonatomic, assign) float leftPicOffset;

/// 字幕右侧图片路径
@property (nonatomic, copy) NSString *rightPicPath;
/// 字幕右侧图片宽度，单位像素
@property (nonatomic, assign) float rightPicWidth;
/// 字幕右侧侧图片高度，单位像素
@property (nonatomic, assign) float rightPicHeight;
/// 右侧图片的右边离字幕右边的距离，单位像素
@property (nonatomic, assign) float rightPicOffset;

@end

/// @brief 描述见文件头
@interface TVKSubtitleRenderParams : NSObject

/// paramFlags标识设置哪些渲染参数
@property (nonatomic, assign) TVKSubtitleParamFlag paramFlags;

/// paramPriorityFlags标识设置了paramFlags的参数是否优先于其他设定值, 比如vtt文件里默认的字体大小/颜色/高度等
/// 当paramFlags对应位置位后再判断该参数的对应位
@property (nonatomic, assign) TVKSubtitleParamFlag paramPriorityFlags;

/// canvasWidth是字幕渲染画布的宽
/// 如果设置了canvasWidth，paramFlags须置位TVKSubtitleParamFlagFontSize
/// canvasWidth和canvasHeight的比例必须和视频的宽高比一致，否则渲染出的字体会变形。
/// 如果不设置，播放器会取当前视频的大小作为渲染画布的大小。
@property (nonatomic, assign) int canvasWidth;

/// canvasHeight是字幕渲染画布的高
/// 如果设置了canvasHeight，paramFlags须置位TVKSubtitleParamFlagCanvasHeight
@property (nonatomic, assign) int canvasHeight;

/// 字体大小
/// 如果设置了fontSize，则必须设置canvasWidth和canvasHeight，否则内部不知道以什么大小为参考来渲染字体
/// 如果设置了fontSize，paramFlags须置位TVKSubtitleParamFlagCanvasWidth
/// 如果不设置fontSize，内部会使用默认的字体大小
@property (nonatomic, assign) float fontSize;

/// 字体缩放比例 vtt css专用
/// 使用fontScale乘以vtt设定的font-size: em值再适应视频宽
/// 如果设置了fontScale，paramFlags须置位TVKSubtitleParamFlagFontScale
/// 最终字体像素为fontScale * vtt em * 16 * canvas width(video width) / default width(491)
/// fontScale默认1.0, 视频宽491像素时,中文字号设定为16像素大小, 将vtt文件内字体大小设定为1em(font-size: 1.00em;)
/// 参考https://developer.mozilla.org/zh-CN/docs/Web/CSS/font-size#ems
/// 如果未设置则采用fontSize
@property (nonatomic, assign) float fontScale;

/// 字体颜色，ARGB格式
/// 如果设置了fontColor，paramFlags须置位TVKSubtitleParamFlagFontColor
/// 如果不设置，默认为白色不透明(0xFFFFFFFF)
@property (nonatomic, assign) int fontColor;

/// 描边宽度
/// 如果设置了outlineWidth，则必须设置canvasWidth和canvasHeight，否则内部不知道以什么大小为参考来渲染描边
/// 如果设置了outlineWidth，paramFlags须置位TVKSubtitleParamFlagOutlineWidth
/// 如果不设置，内部会使用默认的描边宽度
@property (nonatomic, assign) float outlineWidth;

/// 描边颜色，ARGB格式
/// 如果设置了outlineColor，paramFlags须置位TVKSubtitleParamFlagOutlineColor
/// 如果不设置，默认为黑色不透明(0xFF000000)
@property (nonatomic, assign) int outlineColor;

/// 行距 如果设置了lineSpace
/// 则必须设置canvasWidth和canvasHeight 如果设置了lineSpace，paramFlags须置位TVKSubtitleParamFlagLineSpace
/// 如果不设置，内部会使用默认的行距
@property (nonatomic, assign) float lineSpace;

/// 以下startMargin、endMargin和verticalMargin定义字幕的绘制区域，如果不设置，则使用字幕文件中的设置，如果字幕文件也没有定义，则使用默认的
/// 注意：一旦设置了startMargin、endMargin和yMargin，而字幕文件也定义了这几个参数的一个或多个，则会覆盖字幕文件中相应的参数。
/// 下面示意图描绘了水平书写方向下这几个参数的意义，请借助每个参数的注释来理解
/// -----------------------------------------------------------------------
/// |                                                                      |
/// |                                                                      |
/// |                                                                      |
/// |                        _________________________                     |
/// |----- startMargin -----|  This is subtitle text  |------endMargin-----|
/// |                       |_________________________|                    |
/// |                                    | yMargin                         |
/// -----------------------------------------------------------------------

/// 沿着字幕文本方向的边距，根据不同的书写方向意义不同。
/// startMargin是一个比例值，取值范围[0, 1]，即相对于视频画面大小的比例。
/// 对于水平书写方向，startMargin表示字幕左边距离视频画面左边的距离，比如startMargin=0.05则边距为视频宽度的0.05倍（5%）
/// 对于垂直书写方向（无论从右到左还是从左到右），startMargin表示字幕顶部距离视频画面顶部的距离，比如startMargin=0.05则边距为视频高度的0.05倍（5%）
/// 如果设置了startMargin，paramFlags须置位TVKSubtitleParamFlagStartMargin
@property(nonatomic, assign) float startMargin;

/// 沿着字幕文本方向的边距，根据不同的书写方向意义不同。
/// endMargin是一个比例值，取值范围[0, 1]，即相对于视频画面大小的比例。
/// 对于水平书写方向，endMargin表示字幕右边距离视频画面右边的距离，比如endMargin=0.05则边距为视频宽度的0.05倍（5%）
/// 对于垂直书写方向（无论从右到左还是从左到右），endMargin表示字幕底部距离视频画面底部的距离，比如endMargin=0.05则边距为视频高度的0.05倍（5%）
/// 如果设置了endMargin，paramFlags须置位TVKSubtitleParamFlagEndMargin
@property(nonatomic, assign) float endMargin;

/// 垂直字幕文本方向的边距，根据不同的书写方向意义不同。 yMargin为一个比例值，取值范围[0, 1]，即相对于视频画面大小的比例
/// 对于水平书写方向，yMargin表示字幕底部距离视频画面底部的距离，比如yMargin=0.05则边距为视频高度的0.05倍（5%）
/// 对于垂直、从右至左书写方向，yMargin表示字幕右边距离视频画面右边的距离，比如yMargin=0.05则边距为视频宽度的0.05倍（5%）
/// 对于垂直、从左至右书写方向，yMargin表示字幕左边距离视频画面左边的距离，比如yMargin=0.05则边距为视频宽度的0.05倍（5%）
/// 如果设置了verticalMargin，paramFlags须置位TVKSubtitleParamFlagVerticalMargin
@property(nonatomic, assign) float verticalMargin;

/// 文字阴影颜色，ARGB格式，如果设置了该字段，须设置标记TVKSubtitleParamFlagShadowColor
@property (nonatomic, assign) int shadowColor;

/// 文字阴影模糊半径，单位像素，默认为0.0，即没有阴影。如果设置了该字段，须设置标记TVKSubtitleParamFlagShadowRadius
@property (nonatomic, assign) float shadowRadius;

/// 文字阴影在水平方向的偏移，单位为像素，如果设置了该字段，须设置标记TVKSubtitleParamFlagShadowOffset
@property (nonatomic, assign) float shadowXOffset;

/// 文字阴影在垂直方向的偏移，单位为像素，如果设置了该字段，须设置标记TVKSubtitleParamFlagShadowOffset，同shadowXOffset
@property (nonatomic, assign) float shadowYOffset;


/// 自定义字体文件路径,传入有效路径时，以该字体进行渲染，否则以内部默认字体渲染
/// 只支持本地磁盘路径
@property (nonatomic, copy) NSString *fontFile;

/// 背景渲染参数，默认为空, 即默认不渲染背景
@property(nonatomic, strong) TVKSubtitleBackgroundParams *backgroundParams;

/// 字幕装饰
@property(nonatomic, strong) TVKSubtitleDecoration *decoration;

@end

NS_ASSUME_NONNULL_END
