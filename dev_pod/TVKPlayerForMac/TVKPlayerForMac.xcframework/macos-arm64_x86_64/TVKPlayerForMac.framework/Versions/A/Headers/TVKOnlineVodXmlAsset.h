/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKOnlineVodXmlAsset.h
 * @brief    Xml类型资源，主要用于腾讯视频Url直出
 * <AUTHOR> chen
 * @version  1.0.0
 * @date     2020/12/1
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKAssetBase.h"

/// @brief Xml类型资源，主要用于腾讯视频Url直出
@interface TVKOnlineVodXmlAsset : TVKAssetBase

/// 视频vid
@property (nonatomic, copy) NSString *vid;

/// 视频cid
@property (nonatomic, copy) NSString *cid;

/// 从业务后台返回的播放信息xml
@property (nonatomic, copy) NSString *xml;

/// 业务指定flowId，如不填入，播放器会根据内部自有规则生成
@property (nonatomic, copy) NSString *flowId;

/// 初始化方法
/// @param xml xml字符串
- (instancetype)initWithXml:(NSString *)xml;
@end
