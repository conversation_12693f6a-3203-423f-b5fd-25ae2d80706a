/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKAssetFactory.h
 * @brief    资源创建工厂
 * <AUTHOR> chen
 * @version  1.0.0
 * @date     2020/12/1
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKOnlineVodXmlAsset.h"
#import "TVKOnlineVodVidAsset.h"
#import "TVKUrlAsset.h"
#import "TVKLivePidAsset.h"
#import "TVKLiveSidAsset.h"
#import "TVKOfflineVodVidAsset.h"
#import "TVKOnlineVodQuickPlayAsset.h"
#import "TVKOnlineSimulatedLiveAsset.h"

@protocol ITVKAsset;
/// @brief 资源创建工厂
@interface TVKAssetFactory : NSObject

/// 创建点播xml资源类型
/// @param xml 从业务后台返回的播放信息Xml
+ (TVKOnlineVodXmlAsset *)createOnlineVodXmlAssetWithXml:(NSString *)xml;

/// 创建点播vid资源类型
/// @param vid 视频id
/// @param cid 专辑id
+ (TVKOnlineVodVidAsset *)createOnlineVodVidAssetWithVid:(NSString *)vid cid:(NSString *)cid;

/// 创建点播离线资源类型
/// @param vid 视频id
/// @param cid 专辑id
/// @param offlineType 离线类型
+ (TVKOfflineVodVidAsset *)createOfflineVodVidAssetWithVid:(NSString *)vid
                                                       cid:(NSString *)cid
                                               offlineType:(TVKOfflineType)offlineType;


/// 创建url资源类型
/// @param url 需要播放的外部播放地址
+ (TVKUrlAsset *)createUrlAssetWithUrl:(NSURL *)url;

/// 创建直播pid资源类型
/// @param pid 直播节目id
/// @param chid 直播机位id
+ (TVKLivePidAsset *)createLivePidAssetWithPid:(NSString *)pid chid:(NSString *)chid;

/// 创建直播sid资源类型
/// @param sid 直播流id
/// @param livePid 直播节目id
+ (TVKLiveSidAsset *)createLiveSidAssetWithSid:(NSString *)sid livePid:(NSString *)livePid;

/// 创建秒播资源类型
/// @param preVid 秒播previd字符串
/// @param cid 专辑id
+ (TVKOnlineVodQuickPlayAsset *)createOnlineVodQuickPlayAssetWithPreVid:(NSString *)preVid cid:(NSString *)cid;

/// 创建伪直播(轮播)资源类型
/// @param pid 伪直播节目id
/// @param chid 伪直播机位id
/// @param appScene 伪直播业务场景参数， 由业务侧生成，影响下发内容
+ (TVKOnlineSimulatedLiveAsset *)createSimulatedLiveAssetWithPid:(NSString *)pid chid:(NSString *)chid appScene:(NSString *)appScene;

@end
