/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITVKDrawableContainer.h
 * @brief   播放器绘制资源的抽象基类。该类提供一种方式来处理可能采用的不同渲染方式
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/11/14
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#ifndef ITVKDrawableContainer_h
#define ITVKDrawableContainer_h

/// 播放器绘制资源的抽象基类， 提供可能采用的不同渲染方式
@protocol ITVKDrawableContainer <NSObject>
@end

#endif /* ITVKDrawableContainer_h */
