//
//  ITVKMediaPlayer.h
//  TXVPlayer
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/10/23.
//  Copyright © 2019 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TVKMediaPlaybackDelegate.h"
#import "TVKPlayerDefine.h"
#import "TVKMediaInfo.h"
#import "TVKUserInfo.h"
#import "TVKNetVideoInfo.h"
#import "ITVKVideoView.h"
#import "TVKTrackInfo.h"
#import "ITVKRichMediaSynchronizer.h"
#import "ITVKSubtitleRendererController.h"
#import "ITVKPlayerSynchronizer.h"
#if TARGET_OS_VISION
#import "ITVKXRSpatialAudioParams.h"
#endif

@protocol ITVKVRControl;
@protocol TVKAbstractProcessor;
@protocol TVKAudioFxProcessor;
@protocol ITVKVideoFxProcessor;

NS_ASSUME_NONNULL_BEGIN

@protocol ITVKMediaPlayer <NSObject>

/**
 * 正片播放回调
 */
@property (atomic, weak) id<TVKMediaPlaybackDelegate> playbackDelegate;

/**
 * 广告播放回调
 */
@property (atomic, weak) id<TVKAdPlayDelegate> adPlayDelegate;

/**
 * @brief 广告业务透传回调, 需要遵守QADVideoSDKBusinessDelegate
 */
@property (nonatomic, weak) id adBusinessDelegate;

/**
 * 截图回调
 */
@property (atomic, weak) id<TVKCaptureDelegate> captureDelegate;

/**
 * 播放权限回调
 */
@property (atomic, weak) id<TVKPermissionDelegate> permissionDelegate;

/**
 * playbackDelegate、adPlayDelegate、captureDelegate、permissionDelegate等delegated的回调队列。这里
 * delegateCallBackQueue应该是个序列队列，这样能保证回调的方法是按照回调顺序回调。
 * 如果不设置或者设置为nil, 则内部会默认使用主线程。
 *
 * 注意： 对于mediaPlayer:showOutsideController:、mediaPlayerAdVideoView:等需要返回值的回调方法，
 * 为保证内部的运行统一，是在播放器的工作线程回调的，这里处理的时候要特别注意。
 */
@property (atomic, strong, nullable) dispatch_queue_t delegateCallBackQueue;

/**
 * 展示视频的可绘制容器对象，由外面创建好，传入。可以是view或componentHolder
 * @see ITVKVideoView
 * @see ITVKVideoPlayerComponentHolder(仅针对vision os平台)
 * 如果是view，建议一个播放器对应一个videoView不要复用，若要复用，复用之前请调用TXVVideoView的removeAllSubviews;
 */
@property (atomic, strong, nullable) id<ITVKDrawableContainer> videoView;

/**
 * 画中画后台系统播放器view，需要开启退后台自动进画中画功能的时候必须传入该view
 * view需挂载在view树上，在用户看不见的地方。view尺寸：屏幕最大尺寸，view位置: 建议中心位置和videoView中心位置相同
 */
#if TARGET_OS_OSX
@property (atomic, strong) NSView *backgroundPlayerView;
#else
@property (atomic, strong) UIView *backgroundPlayerView;
#endif

/**
 * 获取播放器内部保存的mediaInfo
 */
@property (nonatomic, strong) TVKMediaInfo *mediaInfo;

/**
 * 获取播放器当前的播放状态
 */
@property (nonatomic, assign, readonly) TVKMediaPlayerState currentPlayerState;

/**
 * 获取播放器当前的netvideoInfo
 */
@property (nonatomic, strong, readonly) TVKNetVideoInfo *currentNetVideoInfo;

/**
 * 播放器保存的userInfo,当cookie发生变化时，请相应也更新一下userinfo，保证播放期间使用的cookie和APP使用相一致
 */
@property (nonatomic, strong) TVKUserInfo *userInfo;

/**
 * 播放进度回调时间间隔，默认1.0s
 */
@property (nonatomic, assign) NSTimeInterval progressInterval;

/**
 * 是否可开启pip，你应该收到prepared之后再获取该属性，此时比较准确。
 */
@property (nonatomic, assign, readonly) BOOL pictureInPicturePossible;

/**
 * pip是否激活
 */
@property (nonatomic, assign, readonly) BOOL pictureInPicureActive;

/*
 *  视频拉伸模式，请见TVKVideoStretchMode定义
 */
@property (nonatomic, assign) TVKVideoStretchMode stretchMode;

/*
 *  是否允许airplay投射，默认为YES
 */
@property (nonatomic, assign) BOOL allowsExternalPlayback;

/*
 *  airplay镜像是否使用airplay投射，默认为YES
 */
@property (nonatomic, assign) BOOL usesExternalPlaybackWhileExternalScreenIsActive;

/**
 *  音量，0.0意味着静音(mute)
 */
@property (nonatomic, assign) float volume;

/**
 *  广告音量，0.0意味着静音(mute)
 */
@property (nonatomic, assign) float adVolume;

/**
 *  是否支持色盲，请在prepared之后获取该值。
 */
@property (nonatomic, assign, readonly) BOOL supportColorBlind;

/**
 *  系统播放器AirPods Pro Spatial音效选项,仅iOS14后生效，具体值与AVAudioSpatializationFormats对应
 */
@property (nonatomic, assign) TVKAudioSpatializationFormats allowedAudioSpatializationFormats;

#ifndef TXVPLAYER_NO_MONET
/**
 * 获取后处理接口，在第一次获取时创建
 * @discusstion videoFxProcessor的生命周期仅在一次播放过程中有效，如果调用了播放stop方法，则内部会销毁videoFxProcessor，如果调用方持用了videoFxProcessor，
 * 则播放器stop之后，需要通过该property重新获取才能使用。
 */
@property (nonatomic, readonly) id<ITVKVideoFxProcessor> videoFxProcessor;

#endif

/// 获取字幕渲染控制器
@property (nonatomic, strong, readonly) id<ITVKSubtitleRendererController> subtitleRenderController;

/**
 * @brief 获取音效处理组件，获取后需要添加特效，若要开始处理，调用connect接口，链接播放器
 */
@property (nonatomic, strong, readonly) id<TVKAudioFxProcessor> tvkAudioFxProcessor;

/**
 *获取富媒体处理接口，区别与richMediaProcess
 */
@property (nonatomic, strong, readonly) id<ITVKRichMediaSynchronizer> richMediaSynchronizer;

/**
 * 获取播放器的同步器
 */
@property (nonatomic, strong, readonly, nonnull) id<ITVKPlayerSynchronizer> playerSynchronizer;

/**
 * 支持的最大播放速率，请在prepared之后获取该值。
 */
@property (nonatomic, assign, readonly) float maxPlayRate;

/**
 获取当前播放器类型. 如果是系统播放器，则外部在画中画的情况下不能调用续播、不能在画中画时切换清晰度
*/
- (TVKPlayerType)currentPlayerType;

/**
 * 播放入口，传入视频信息，准备播放，调用完该方法之后，须调用play方法开始播放
 * @param mediaInfo 视频信息，请看TVKMediaInfo定义
 * @param userInfo 用户账号信息
 */
- (void)openMediaPlayerWithMediaInfo:(TVKMediaInfo *)mediaInfo userInfo:(TVKUserInfo *)userInfo;

/**
 *  启动播放
 */
- (void)play;

/**
 *  暂停播放,默认会请求暂停广告
 */
- (void)pause;

/**
 *  暂停播放，不会请求“暂停广告”
 */
- (void)pauseWithoutAD;

/**
 *  seek操作，设置播放时间点（实现快进快退）
 *  @param position 要seek到的位置，单位秒
 *  @warning 此方法即将废掉，不再推荐使用，请使用seekTo:mode:
 */
- (void)seekTo:(NSTimeInterval)position;

/**
 *  seek操作，设置播放时间点（实现快进快退）
 *  @param position 播放时间点，单位秒
 *  @param mode     seek模式
 */
- (void)seekTo:(NSTimeInterval)position mode:(TVKSeekMode)mode;

/**
 *  用于直播回看
 *  @param position 要seek到的server时间，单位为秒，传入-1表示seek到正常直播，请参考TVKNetLiveSeebackInfo
 */
- (void)seekLive:(int64_t)position;

/**
 *  停止播放. 停掉播放的时候，务必调用，用于清理内部资源、必要的数据上报等。
 *  调用接口后，播放器会清理外部的所有设置（除各种delegate），和新创建播放器几乎一致。如果复用播放器，下次播放需要重新设置。
 */
- (void)stop;

/**
 * 倍速播放
 * @param rate 倍速率，比如传入2表示以正常速度的2倍播放
 */
- (void)setPlayRate:(CGFloat)rate;

/**
 * 跳过广告，只有在一边播放视频一边登陆而且是好莱坞回来才能调用，只对腾讯视频开放
 */
- (void)skipAd;

/**
 * 循环播放
 * @param loopBack 是否循环播放，如果设置YES，则播放结束后会从头开始播放
 */
- (void)setLoopBack:(BOOL)loopBack;

/**
 * 循环播放
 * @param loopBack 是否循环播放，如果设置YES，则播放结束后会从头开始播放
 * @param startPositionMs 跳过片头位置，单位毫秒
 * @param skipEndPostionMs 跳过片尾位置，单位毫秒，比如到300s处开始循环播放，则设置skipEndPostionMs为300*1000
 */
- (void)setLoopBack:(BOOL)loopBack
      startPosition:(int64_t)startPositionMs
     skipEndPostion:(int64_t)skipEndPostionMs;

/**
 * 获取当前播放视频的总时长
 * @return 当前视频的总时长，单位秒
 */
- (NSTimeInterval)duration;

/**
 * 获取当前可播放时长，即已缓冲的时长，调用方可据此向用户展示已缓冲的部分
 * @return 当前可播放时长，单位秒
 */
- (NSTimeInterval)playableDuration;

/**
 * @brief 实时获取所有的DCL上报信息，返回值是包含key/value键值对的json字符串，key与value都是String类型。
 * key可直接作为DCL反馈的上报字段，value是对应的值，主要是为了方便app能实时获取播放器的信息并透传上报到DCL反馈上
 *
 * @return 获取所有的DCL上报信息
 */
- (NSString *)allDCLReportInfos;

/**
 * 获取当前播放视频的播放位置
 * @return 当前播放视频的播放位置，单位秒
 */
- (NSTimeInterval)currentPosition;

/**
 * 获取视频宽高
 * @return 视频宽高
 */
- (CGSize)videoSize;

/**
 * 切换清晰度
 * @param definition 要切换的清晰度格式，清晰度类型见TVKPlayerDefine.h. 默认内部走无缝切换
 */
- (BOOL)switchMediaDefinition:(NSString *)definition;

/**
 * 切换清晰度。默认使用无缝切换清晰度，如果视频不支持无缝切换清晰度，将采用重新打开播放器的方式进行清晰度切换
 * @param mediaInfo 要切换清晰度的视频信息
 * @param userInfo 切换时，需要更新的用户信息
 * @return 调用是成功执行
 */
- (BOOL)switchDefinitionWithMediaInfo:(TVKMediaInfo *)mediaInfo userInfo:(TVKUserInfo *)userInfo;

/**
 * 切换清晰度。使用重新打开播放器的方式进行清晰度切换。不会走到无缝切换清晰度
 * @param mediaInfo 要切换清晰度的视频信息
 * @param userInfo 切换时，需要更新的用户信息
 * @return 调用是成功执行
 */
- (BOOL)switchDefinitionReopenWithMediaInfo:(TVKMediaInfo *)mediaInfo userInfo:(TVKUserInfo *)userInfo;

/**
 * 重新起播。主要用于直播场景下，通过调用此接口，播放器会重新起播播放器，起到刷新的作用，这样播放器就可以播放最新的地址内容，减少直播内容的延迟
 */
- (void)refreshPlayerWithReopen;

/**
 * 无缝刷新播放器,主要用于点播场景，网络情况变化时，须重走cgi（后台根据新的网络，判断是否是免流场景，下发新的播放地址）。
 * 通过调用此接口，重走cgi，播放器内部无缝切换
 */
- (void)refreshPlayer;

/**
 * 为当前清晰度更新播放源，比如从硬字幕源切换到无硬字幕源，清晰度保持不变
 *
 * @param userInfo  用户信息
 * @param mediaInfo 视频信息 vid和definition须与当前播放的vid和definition相同，否则return NO
 * @return 是否执行换源
 */
- (BOOL)updateSourceForCurrentDefinition:(TVKMediaInfo *)mediaInfo userInfo:(TVKUserInfo *)userInfo;

/**
 * 截取当前播放的画面图像，结果通过captureDelegate返回
 * @param width 需要采集的宽，0则使用原始图像宽高
 * @param height 需要采集的高， 0则使用原始图像宽高
 * @return 返回此次截图的id
 */
- (int)captureImageWithWidth:(CGFloat)width height:(CGFloat)height;

/**
 * 边下边播时，视频可播放时长，即已经下载的部分
 * @return 边下边播的可播放时长，单位秒
 */
- (NSTimeInterval)downPlayableDuration;

/**
 * 通知播放器将要处的场景，主要用于广告的显示和消失
 * @param viewState 0:小窗; 1:全屏。
 */
- (void)informPlayerWillLayout:(int)viewState;

/**
 * 通知播放器已经所处的场景，主要用于广告的显示和消失
 * @param viewState 0:小窗; 1:全屏。
 */
- (void)informPlayerDidLayout:(int)viewState;

/**
 * 启动画中画，仅在app处于前台时调用有效
 */
- (void)startPictureInPicture;

/**
 * 无缝启动画中画, 仅在app处于前台时调用有效。要调用该接口，起播时必须通过mediaInfo.configMap设置kTVKMediaInfoConfigMapKeyEnableOpenPictureInPictureSeamless参数
 */
- (void)startPictureInPictureSeamless;

/**
 * 关闭画中画，仅在app处于前台时调用有效
 */
- (void)stopPictureInPicture;

/**
 * 播放过程中，场景等实时信息通知。
 * @param infoKey 是一个枚举值，请见TVKRealTimeInfoKey的定义
 * @param infoValue 根据key的不同，类型不同，具体如下
 *        key                            :  value
 *        ---------------------------------------
 *        TVKRealTimeInfoKeyIsPreload    : NSNumber类型，0表示非预加载（活跃播放），1代表预加载（非活跃播放）
 *        TVKRealTimeInfoKeySkipPos      : a NSDictionary，包含两个key-value对，key为字符串，value为NSNumber<float>，单位为秒,
 *                                         @{kTVKSkipStartPosKey : skipStartPos, kTVKSkipEndPosKey : skipEndPos}，
 *                                          kTVKSkipStartPosKey和kTVKSkipEndPosKey请见TVKPlayerDefine.h
 *        TVKRealTimeInfoKeyBackgroundAudioPlay    : NSNumber类型，BOOL值，YES为开启音频后台播放，NO为关闭。
 *        TVKRealTimeInfoKeyEnableSetNextMediaInfo : NSNumber类型，BOOL值，YES为开启"在播放完毕当前视频，可以无缝切换到另一个视频"功能
                                                    （通过setNextMediaInfo设置下一个视频），NO为关闭
 *        TVKRealTimeInfoKeyMultiNetworkCardOpen :NSNumber类型，0表示关闭，1表示开启
 *        TVKRealTimeInfoKeyEnableAutoPictureInPicture: 开启或关闭退后台自动画中画功能 NSNumber类型，0表示关闭，1表示开启   不设置默认关闭。
 *                                         要开启退后台自动画中画功能 ，起播时必须通过mediaInfo.configMap设置kTVKMediaInfoConfigMapKeyEnableOpenPictureInPictureSeamless参数
 *        TVKRealTimeInfoKeyRealAdaptiveMode: NSNumber类型， 控制下载组件自适应模式
 */
- (void)informRealTimeInfoChangedWithInfoKey:(TVKRealTimeInfoKey)infoKey infoValue:(NSObject *)infoValue;

/*
 * 当前支持setNextMediaInfo的level值，在使用"在播放完毕当前视频，可以无缝切换到另一个视频"功能（以下简称“互动视频”）应该先进行判断，
 * 根据level值决定是是否可以使用“互动视频“功能。
 * 当levle值为下列情况时,可以调用“互动视频“功能：
 *   1.TVKSetNextMediaInfoSupportLevelSupport;
 *   2.TVKSetNextMediaInfoSupportLevelUnknow且没有调用OpenMediaPlayer
 * 当level值为下列情况时，不可以使用“互动视频“功能：
 *   1.TVKSetNextMediaInfoSupportLevelDeviceUnsupport
 *   2.TVKSetNextMediaInfoSupportLevelVideoUnsupport
 * 当level值为下列情况时，通过降低清晰度到fhd之后，可以使用或者重新加载，并且OpenMediaPlayer之前设置
 * 为互动模式（通过informRealTimeInfoChangedWithInfoKey接口）：
 *   1.TVKSetNextMediaInfoSupportLevelDefinitionUnsupport
 */
- (TVKSetNextMediaInfoSupportLevel)supportLevelOfSetNextMediaInfo;

/**
 * 设置下一个续播的视频。用于播放当前视频后，继续无缝播放添加的视频，在播放当前视频之后，继续播放器添加的视频地址。
 * 注意：此接口主要用于实现”互动视频“，而不是电视剧等连续播放。仅在TVKRealTimeInfoKeyEnableSetNextMediaInfo设置为YES后有效。播放当前的视频，仅只能设置一次下一个续播的视频，多设置的会忽略掉。
 * 播放完毕当前的视频，会回调xxx, 可以继续设置下一个续播的视频。播放所有的视频后，不会回调oncomplete。退出，则直接调用stop即可
 * @param mediaInfo 将要播放的视频信息
 * @return  当前设置的MediaInfo是否生效
 */
- (BOOL)setNextMediaInfo:(TVKMediaInfo *)mediaInfo;

/**
 * 通知广告层播放器详情页相关事件状态, 具体查看QADPlayerBaseEventInfo
 */
- (void)notifyAdEventInfo:(id)adEventInfo;

/**
 * 当前播放的总时长，去除缓冲和暂停的时长
 */
- (NSTimeInterval)correctTotalPlayDuration;

/*
 * 暂停当前视频的下载，主要针对网络切换到运营商网络时暂停下载，防止不必要的浪费流流量
 */
- (void)pauseDownload;

/*
 * 恢复当前视频的下载，在运营商网络下，弹框让用户确认是否继续下载，如果用户同意，则恢复下载
 */
- (void)resumeDownload;

/**
 * 添加视频轨道，调用方要保证name唯一，否则添加失败
 * @param mediaInfo 添加的视频轨道信息.
 * @param name 轨道名称
 * @param playPosition 从当前视频的具体时间开始播放
 * @param startPosition 视频轨开始播放的位置
 * @param playDuration 视频轨道播放时长
 * @return 添加视频轨道是否成功
 */
- (BOOL)addVideoTrackSourceWithMediaInfo:(TVKMediaInfo *)mediaInfo
                                    name:(NSString *)name
                        fromPlayPosition:(NSTimeInterval)playPosition
                           startPosition:(NSTimeInterval)startPosition
                            playDuration:(NSTimeInterval)playDuration;

/**
 * 添加音频轨道，调用方要保证name唯一，否则添加失败
 * @param audioTrackUrlString 音频轨道地址
 * @param mimeType mimeType
 * @param name 音轨轨道名称
 * @return 添加音频轨道是否成功
 */
- (BOOL)addAudioTrackSource:(NSString *)audioTrackUrlString
                   mimeType:(NSString *)mimeType
                       name:(NSString *)name __attribute__((deprecated("暂不支持外部添加外挂音轨")));

/**
 * 添加字幕轨道，调用方要保证name唯一，否则添加失败
 * @param subtitleUrlString 字幕轨道地址
 * @param mimeType mimeType
 * @param name 字幕轨道名称
 * @return 添加字幕轨道是否成功
 */
- (BOOL)addSubtitleSource:(NSString *)subtitleUrlString
                 mimeType:(NSString *)mimeType
                     name:(NSString *)name __attribute__((deprecated("暂不支持外部添加外挂字幕功能")));

/**
 * 移除指定轨道
 * @param trackInfo 轨道对象
 */
- (void)removeTrackWithTrackInfo:(TVKTrackInfo *)trackInfo __attribute__((deprecated("add资源后，无需删除，在退出播放时，由播放器统一释放")));

/**
 * 选择指定媒体轨道. 需配合getTrackInfo使用，使用其返回的对象索引下标作为参数
 *
 * @param index 媒体轨道索引下标。来自于getTrackInfo的在数组中的索引。从0开始
 */
- (void)selectTrack:(int)index;

/**
 * 反选指定轨道。需配合getTrackInfo使用，使用其返回的对象索引下标作为参数
 * 现在只支持字幕类型轨道的索引
 *
 * @param index 媒体轨道索引下标。来自于getTrackInfo的在数组中的索引。从0开始
 */
- (void)deselectTrack:(int)index;

/**
 * 获取所有音频、视频、轨道
 */
- (nullable NSArray<TVKTrackInfo *> *)getTrackInfo;

/**
 * 增加上报信息,userInfo会透传添加到上报的字典
 * @param reportInfoEvent 上报信息事件
 * @param userInfo 上报信息
 */
- (void)addReportInfoWithReportInfoEvent:(TVKReportInfoEvent)reportInfoEvent userInfo:(NSDictionary *)userInfo;

/**
 * 当前播放使用的cdnId
 */
- (NSString *)cdnId;

/**
 * 当前播放使用的播放地址
 */
- (NSString *)cdnPlayUrl;

/**
 *  连接后处理组件与播放器
 *  连接条件成功条件：
 *  1. 播放器Prepared之后，
 *  2. 处理器中添加了特效
 */
- (BOOL)connetPostProcessor:(id<TVKAbstractProcessor>)processor;

/**
 *  将后处理组件断开连接，
 *  断开连接成功条件：
 *  后处理组件中不存在正在运行的特效
 */
- (BOOL)disconnectPostProcessor:(id<TVKAbstractProcessor>)processor;

/**
 *  添加播放细分事件回调
 */
- (void)addPlayerEventDelegate:(id<TVKPlayerEventDelegate>)eventDelegate;

/**
 *  移除播放细分事件回调
 */
- (void)removePlayerEventDelegate:(id<TVKPlayerEventDelegate>)eventDelegate;

/**
 *  添加上报事件回调
 */
- (void)addReportEventDelegate:(id<TVKReportEventDelegate>)delegate;

/**
 *  移除上报事件回调
 */
- (void)removeReportEventDelegate:(id<TVKReportEventDelegate>)delegate;

/// 设置RealVideoView（内部渲染视频的view）相对于父view的坐标偏移，坐标系为系统UIView坐标系
/// 水印和外挂字幕不会跟着移动
/// 设置了videoView后，再调用才能生效
/// 如果超出了父view(外部设置的videoView)，超出的部分是否被裁剪，取决于外部设置的videoView的clipsToBounds属性
/// - Parameters:
///   - offset.x: x轴相对坐标
///   - offset.y: y轴相对坐标
- (void)setDisplayOffset:(CGPoint)offset;

/// 设置RealVideoView（内部渲染视频的view）缩放
/// 水印和外挂字幕不会跟着缩放
/// 设置了videoView后，再调用才能生效
/// 如果超出了父view(外部设置的videoView)，超出的部分是否被裁剪，取决于外部设置的videoView的clipsToBounds属性
/// 每次调用该接口进行缩放都是相对于初始size进行缩放
/// - Parameters:
///   - scale: 缩放比例，1为不缩放
///   - anchorPoint: 缩放锚点，参考系统CALayer中anchorPoint定义。锚点只用于缩放，缩放完成后锚点会恢复为默认值(0.5,0.5)
- (void)setDisplayScale:(CGFloat)scale anchorPoint:(CGPoint)anchorPoint;

/// 系统播放器是否因为外部保护不足而黑屏.  
- (BOOL)outputObscuredDueToInsufficientExternalProtection;
#if TARGET_OS_VISION

/// 启用 多声道空间音频能力，默认为关闭状态「仅针对 visionOS 平台」
///
/// 注意：本接口设计的最终形态是可以支持动态插拔，即调用时机不受限制「可以是起播前，播放中等」，动态设置立即生效
/// 但鉴于目前内核不支持动态插拔，故需要在起播前调用，并且只对本次播放生效 ！！！
///
/// - Parameters:
///   - params: 声道布局模式 等参数设置
- (void)enableXRSpatialAudio:(id<ITVKXRSpatialAudioParams>)params;

/// 关闭 多声道空间音频能力「仅针对 visionOS 平台」
///
/// 注意：本接口设计的最终形态是跟 enableXRSpatialAudio 搭配使用，如果调用过 enableXRSpatialAudio，可以通过此接口关闭空间音频，否则不需要调用
/// 但鉴于目前内核不支持动态插拔，故目前不需要调用，作为预留接口 ！！！
- (void)disableXRSpatialAudio;

#endif

@end

NS_ASSUME_NONNULL_END
