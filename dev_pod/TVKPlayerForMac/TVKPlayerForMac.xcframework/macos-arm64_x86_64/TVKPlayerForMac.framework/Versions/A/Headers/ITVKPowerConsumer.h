/************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     ITVKPowerConsumer.h
 * @brief    功耗消耗者
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/10/12
 * @license     GNU General Public License (GPL)
 ***********************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, TVKPowerConsumerType) {
    // 未知
    TVKPowerConsumerTypeUnknown,
    // VVC编码格式
    TVKPowerConsumerTypeVvc,
    // 超分特性
    TVKPowerConsumerTypeSuperResolution,
    // audio vivid
    TVKPowerConsumerTypeAudioVivid,
};

/// @brief 同文件头注释
@protocol ITVKPowerConsumer <NSObject>

/// 功耗消耗者类型
@property (nonatomic, assign, readonly) TVKPowerConsumerType type;

/// 该功耗特性的功耗值，单位毫瓦
@property (nonatomic, assign, readonly) NSUInteger powerMw;

/// 是否启用
@property (nonatomic, assign, readonly) BOOL enabled;

 /**
  * @brief 建议是否启用
  *
  * @param enabled 是否启用
  * @return 是否采纳建议
  */
- (BOOL)suggestEnabled:(BOOL)enabled;

@end

NS_ASSUME_NONNULL_END
