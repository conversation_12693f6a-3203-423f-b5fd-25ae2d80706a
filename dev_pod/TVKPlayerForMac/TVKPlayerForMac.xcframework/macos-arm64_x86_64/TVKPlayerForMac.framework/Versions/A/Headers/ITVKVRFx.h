/*****************************************************************************
 * @copyright Copyright (C), 1998-2021, Tencent Tech. Co., Ltd.
 * @file     ITVKVRFx.h
 * @brief    VR
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/2/19
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITVKVideoFx.h"
#import "TVKVRControlDefine.h"

/**
 * VR效果
 */
@protocol ITVKVRFx <ITVKVideoFx>

/**
 * 设置VR显示模式
 * @param pattern 见TVKVRControlPattern定义，默认使用单目模式
 */
- (void)setVrViewPattern:(TVKVRControlPattern)pattern;

/**
 * 设置VR 参数配置, 360度模式或者180度模式，详见参考 TVKVRControlDefine.h 中的定义
 * @param config 默认是360模式。
 *        ----------------------------------
 *        key                  :  value
 *        ----------------------------------
 *        TXVVRConfigVRModeKey :  TXVVRConfigVRModeValue180
 */
- (void)setVRConfig:(NSDictionary<NSString*, NSString*> *)config;

/**
 * 图像旋转，目前仅用于vr场景
 *
 * 使用时机：在VR渲染成功后
 * @param angleX 负值左转,正值右转
 * @param angleY 负值上转,正值下转
 * @param angleZ 负值向后,正值向前 (内部暂未实现此种状况)
 */
- (void)doRotateWithAngleX:(CGFloat)angleX
                    angleY:(CGFloat)angleY
                    angleZ:(CGFloat)angleZ;

/**
 * 是否开启陀螺仪
 *
 * 使用时机：在VR渲染成功后
 * @param enable YES开启，NO关闭
 */
- (void)enableGypsensor:(BOOL)enable;

@end
