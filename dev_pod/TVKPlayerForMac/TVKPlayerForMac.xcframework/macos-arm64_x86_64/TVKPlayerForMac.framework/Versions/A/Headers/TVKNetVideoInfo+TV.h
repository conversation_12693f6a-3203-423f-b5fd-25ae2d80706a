/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKNetVideoInfo+TV.h
 Author      : liyukuan
 Version     : 1.0
 Date        : 2017/9/1
 Description : 扩展TVKNetVideoInfo，仅对腾讯视频开放
 History     : 2017/9/1 初始版本
 ***********************************************************/

#import "TVKNetVideoInfo.h"

typedef NS_ENUM(NSUInteger, TVKDrmEncryptionType) {
    TVKDrmEncryptionTypeNone     = 0,
    TVKDrmEncryptionTypeFairPlay = 4
};

@interface TVKNetVideoInfo (TV)

/**
 视频地址列表
 */
@property (nonatomic, copy) NSArray<NSURL *> *videoUrlArray;

/**
 分片时长列表，和videoUrlArray中的地址一一对应
 */
@property (nonatomic, copy) NSArray<NSNumber *> *videoTimeArray;

/**
 flowId唯一标识一次播放，主要用于数据上报
 */
@property (nonatomic, copy) NSString *flowId;

/**
 当前播放所用的cdn url
 */
@property (nonatomic, copy) NSString *cdnPlayUrl;

/**
 当前播放所用的cdnId
 */
@property (nonatomic, copy) NSString *cdnId;

/**
 是否走了代理组件
 */
@property (nonatomic, assign) BOOL isP2PPlayMode;

/**
 是否走了代理组件的离线播放
 */
@property (nonatomic, assign) BOOL isP2POfflinePlay;

/**
 drm类型，详情见TVKDrmEncryptionType
 */
@property (nonatomic, assign) int drm; 

@end

@interface TVKNetMediaDefinitionInfo (TV)

/// 是否是HDR10
@property (nonatomic, assign) BOOL isHDR10;

/// 是否HDR Vivid
@property (nonatomic, assign) BOOL isHDRVivid;

/// 是否是TIE
@property (nonatomic, assign) BOOL isTIE;

@end
