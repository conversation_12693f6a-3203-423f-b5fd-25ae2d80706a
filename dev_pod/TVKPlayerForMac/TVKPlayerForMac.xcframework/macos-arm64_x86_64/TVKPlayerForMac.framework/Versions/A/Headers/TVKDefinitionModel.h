/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKDefinitionModel.h
 Author      : Denzel
 Version     : 1.0
 Date        : 9/27/12
 Description :
 History     : 9/27/12 初始版本
 ***********************************************************/

#import <Foundation/Foundation.h>
#import "TVKCGIDefines.h"

/// @brief 清晰度详细信息
@interface TVKDefinitionModel : NSObject
/// 通常所说的FormatId
@property (atomic, copy) NSString *fileid;
/// 清晰度名字，sd、hd、shd、fhd...
@property (nonatomic, copy) NSString *fileName;
/// 清晰度显示完整文案，例：蓝光;(1080P VIP尊享)，投传给外部使用，播放器不理解
@property (nonatomic, copy) NSString *fullText;
/// 清晰显示短文案，标清、高清、超清、蓝光...
@property (nonatomic, copy) NSString *shortText;
/// 分辨率文本，比如：270P、480P、720P、1080P、HDR
@property (nonatomic, copy) NSString *resolutionText;

/// @deprecated use fullText instead。清晰度显示完整文案，由fullText解析而来，去掉了分号和括号，分号替换成空格，但这步解析不应该放在播放器内部，所以此字段不再推荐使用，请使用fullText
@property (nonatomic, copy) NSString *processedFullText;
/// 当前清晰度是否被选中，即要要播放的清晰度
@property (nonatomic, assign) NSInteger filesl;
/// 码率
@property (nonatomic, assign) NSInteger fileBr;
/// 是否是付费清晰度
@property (nonatomic, assign) BOOL isVip;
/// 清晰度权限标记 4.1.1
@property (nonatomic, assign) NSInteger fileLimit;
/// 对应文件大小
@property (nonatomic, assign) int64_t videoFileSize;
/// 是否是直播清晰度
@property (nonatomic, assign) BOOL isLive;
/// 该清晰度对应的level，仅用于排序
@property (nonatomic, assign) int level;
/// 音频编码，1:AAC, 2:Dolby Surround, 3:Dolby Atmos, 4:Dolby 2.0, 5:DTS HD, 6:DTS X, 7:PCG 2.0, 8:aac5.1, 9:audio vivid, 10:wanos
@property (nonatomic, assign) int audio;
/// 视频编码，1:H264, 2:H265, 3:HDR10, 4:DolbyVision, 20: VVC(o266)
@property (nonatomic, assign) int video;
/// 直播的画质增强类型 2:杜比 4:hdrvivid
@property (nonatomic, assign) TVKLiveHeightenType liveHeightenType;
/// 0:非加密视频 1:drm加密视频 2:数字太和drm 3:数字太和drm 8:hls加密视频 4:fairplay加密，(注意，这是getvinfo返回
@property (nonatomic, assign) int drm;
/// @deprecated 是否可以做超分运算(待废弃)
@property (nonatomic, assign) BOOL sr;
/// 支持新的超分辨率算法类型: 0:不支持，1:支持TVM超分
@property (nonatomic, assign) int videoSuperResolutionType;
/// 不支持TVM超分的介质层面的原因，主要用于上报分析
/// 0：支持TVM超分，1:未命中Server侧超分灰度策略，2：未选中该清晰度
/// 4：未配置该超分清晰度，8：介质为DRM，16:介质为VR，32:介质为高帧率
@property (nonatomic, assign) int tvmsrReasonCode;
/// 当前清晰度格式使用终端超分能力时，是否需要会员才能观看
@property (nonatomic, assign) BOOL isSuperResolutionNeedVip;
/// 是否需要应用SDR增强为HDR
@property (nonatomic, assign) BOOL hdrEnhance;
/// app清晰度推荐，默认0， 1表示推荐清晰度
@property (nonatomic, assign) BOOL recommend;
/// 视频当前帧率，一般非高帧默认25，高帧格式会对齐当前播放格式的帧率
@property (nonatomic, assign) int  vfps;
/// 该清晰度对应的视频轨码率, 嵌套m3u8资源中对应 #EXT-X-STREAM-INF:BANDWIDTH字段. 例如：5022777 单位bps
/// TODO:将来做AVS分离时，要透出videoBandwidth，audioBandwidth不用
/// 当是avs分离时（后台下发）, 后台下发的audioBandwidth=0, 视频整体码率为videoBandwidth.
/// 当不是avs分离时, 视频整体码率为mVideoBandwidth + mAudioBandwidth.
@property (nonatomic, assign) int videoBandwidth;
/// 该清晰度对应的音频码率, 例如：48322 单位bps
@property (nonatomic, assign) int audioBandwidth;
/// 是否是avs分离格式
@property (nonatomic, assign, getter=isAvsSeparate) BOOL avsSeparate;
/// master m3u8中的resolution,格式如"480x270"
@property (nonatomic, copy) NSString *m3u8Resolution;
/// 视频源存在视频和音频合流的场景，同一清晰度可能有多个不同音频的流。
/// 该值用来表示对应视频清晰度下拥有的音频类型，格式为[@"aac", @"atmos",...]，为空表示只有aac，目前仅直播可用
@property (nonatomic, copy) NSArray *liveAudioTrackList;
/// 清晰度特性，透传，播放器暂不理解
@property (nonatomic, assign) int feature;
/// 该格式实际的清晰度，比如sd、hd、shd.
/// 和filename不同，比如filename=hdr10, formatdefn=fhd
@property (nonatomic, copy) NSString *formatDefn;
/// 清晰度分组，透传给业务，比如1080和hdr如果属于同一组，在业务UI上是同一个清晰度格子
@property (nonatomic, assign) int group;
/// 当前介质宽
@property (nonatomic, assign) int width;
/// 当前介质高
@property (nonatomic, assign) int height;

/// 几个帮助属性
/// 是否是杜比音频
@property (nonatomic, assign, readonly) BOOL isDolbyAudio;
/// 是否是杜比视频
@property (nonatomic, assign, readonly) BOOL isDolbyVision;
/// 是否是SDR+
@property (nonatomic, assign, readonly) BOOL isSDRPlus;
/// 是否是SRD10
@property (nonatomic, assign, readonly) BOOL isHDR10;
/// 是否高帧率臻彩视听
@property (nonatomic, assign, readonly) BOOL isHighFrameRateHDR10;
/// 是否纯音频播放
@property (nonatomic, assign, readonly) BOOL isAudioOnly;
/// 是否HDR Vivid
@property (nonatomic, assign, readonly) BOOL isHDRVivid;
/// 是否高帧率HDR Vivid
@property (nonatomic, assign, readonly) BOOL isSfrHDRVivid;
/// 是否VVC
@property (nonatomic, assign, readonly) BOOL isVvc;
/// 是否是AV1
@property (nonatomic, assign, readonly) BOOL isAV1;
/// 是否是AVS3
@property (nonatomic, assign, readonly) BOOL isAVS3;
/// 是否超过1080p清晰度
@property (nonatomic, assign, readonly) BOOL isMoreThan1080P;
/// 是否是mv-hevc (3d)
@property (nonatomic, assign, readonly) BOOL isMvHevc;
/// 是否是tie
@property (nonatomic, assign, readonly) BOOL isTIE;

/**
 * 从后台返回的点播数据中解析清晰度信息
 * @param dict 从后台返回数据中解析出的清晰度字段
 * @return 一个TVKDefinitionModel实例
 */
+ (TVKDefinitionModel *)definitionModelFromDict:(NSDictionary *)dict;

/**
 * 从后台返回的直播数据中解析清晰度信息
 * @param dict 从后台返回数据中解析出的清晰度字段
 * @return 一个TVKDefinitionModel实例
 */
+ (TVKDefinitionModel *)definitionModelFromLiveDict:(NSDictionary *)dict;

+ (int)codeOfDefinitionName:(NSString *)def;

+ (NSString *)processFullText:(NSString *)fullText;

/**
 @brief 定义清晰度的compare元方法，用于实现哪个清晰度更高的这种逻辑
 @param object 比较的对象实例
 @return compare后的结果
 */
- (NSComparisonResult)compare:(TVKDefinitionModel *)object;

@end
