/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKMovieThumbInfo.h
 Author      : Haywood
 Version     : 1.0
 Date        : 13-3-12
 Description :
 History     : 13-3-12 初始版本
 ***********************************************************/

#import <Foundation/Foundation.h>
#import <CoreGraphics/CGBase.h>

/// 点播出图信息
@interface TVKMovieThumbInfo : NSObject
/// url 前缀
@property (nonatomic, retain) NSString *urlPrefix;
/// 质量前缀
@property (nonatomic, retain) NSString *qualityPrefix;
/// 文件类型
@property (nonatomic, retain) NSString *fileType;
/// 视频id
@property (nonatomic, retain) NSString *videoID;
/// 开始时间
@property (nonatomic, assign) NSTimeInterval beginDuration;
/// 时长
@property (nonatomic, assign) NSTimeInterval fullDuration;
/// 图片时间点
@property (nonatomic, assign) NSTimeInterval eachPicDuration;
/// 图片宽
@property (nonatomic, assign) CGFloat eachPicWidth;
/// 图片高
@property (nonatomic, assign) CGFloat eachPicHeight;
/// 行
@property (nonatomic, assign) NSUInteger columnNum;
/// 列
@property (nonatomic, assign) NSUInteger rowNum;

- (NSInteger)getPage:(NSTimeInterval)duration;
- (NSComparisonResult)compare:(TVKMovieThumbInfo *)other;
@end
