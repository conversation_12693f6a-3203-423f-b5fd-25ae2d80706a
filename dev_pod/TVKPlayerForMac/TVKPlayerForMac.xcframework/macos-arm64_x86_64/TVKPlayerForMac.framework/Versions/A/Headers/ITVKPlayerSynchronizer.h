/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     ITVKPlayerSynchronizer.h
 * @brief    播放器类型的同步源，是播放同步源的一种
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/12/02
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

#import "ITVKPlayerSyncSource.h"
#import "TVKPlayerSynchronizerConfig.h"

NS_ASSUME_NONNULL_BEGIN

/// @brief 播放器类型的同步源接口
@protocol ITVKPlayerSynchronizer <ITVKPlayerSyncSource>

/// @brief 设置同步源
/// @note 如果source为nil，表示移除主同步器
/// @note 同步源类型可以是播放器类型的同步源,参考：ITVKPlayerSynchronizer.h
/// @note 同步源类型可以是外部时钟同步源, 参考：ITVKPlayerExternalSyncClockGetter.h
/// @note 一个同步器只能有一个同步源，重复设置会移除之前的同步源
/// @param syncSource 同步源
- (void)setSyncSource:(nullable id<ITVKPlayerSyncSource>)syncSource;

/// @brief 设置同步源的配置
/// @note 同步源置，其中： offset表明自己相对于同步源的偏移量
/// 例如当前的同步源的第0秒希望播放器的第10秒，则offset=10*1000
/// @param config 同步源的配置
- (void)setConfig:(TVKPlayerSynchronizerConfig *)config;

/// @brief 重置所有同步关系
/// @note 移除所有设置的同步源
/// @note 本播放器恢复到初始状态
/// @note 同步器跟随播放器的生命周期，播放器被reset的时候，同步器也会被reset
/// @note 播放器复用的场景，如果当前播放结束，下一次播放开始，如果需要同步器，需要重新设置
- (void)reset;

@end

NS_ASSUME_NONNULL_END

