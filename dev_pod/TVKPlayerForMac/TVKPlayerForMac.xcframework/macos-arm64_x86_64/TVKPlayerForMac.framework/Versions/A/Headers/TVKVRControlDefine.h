/************************************************************
 Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 FileName    : TVKVRControlDefine.h
 Author      : cooper chen
 Version     : 1.0
 Date        : 2019/7/9
 Description : VR控制定义
 History     : 2019/7/9 初始版本
 ***********************************************************/

#import <Foundation/Foundation.h>

#ifndef TVKVRCONTROL_DEFINE_H
#define TVKVRCONTROL_DEFINE_H

typedef NS_ENUM(NSUInteger, TVKVRControlPattern) {
    TVKVRControlPatternMonocular = 0, //单目模式
    TVKVRControlPatternBinocular = 1, //双目模式
};

typedef NS_ENUM(NSUInteger, TVKVRRenderMode) {
    TVKVRRenderMetal = 0, //metal 渲染
    TVKVRRenderOpenGL = 1, //opengl 渲染
};
#pragma mark -vr config key
extern NSString *const TXVVRConfigVRModeKey;

#pragma mark -vr config value
extern NSString *const TXVVRConfigVRModeValue360;//360，球形
extern NSString *const TXVVRConfigVRModeValue180;//180

#endif /* TVKVRCONTROL_DEFINE_H */
