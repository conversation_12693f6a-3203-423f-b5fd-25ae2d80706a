/*****************************************************************************
 * @copyright Copyright (C), 1998-2021, Tencent Tech. Co., Ltd.
 * @file     ITVKTVMSuperResolutionFx.h
 * @brief    腾讯视频移动端超分辨率特效
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/5/17
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITVKVideoFx.h"

/**
 * @brief 分隔模式定义
 *  重要：不要随意修改枚举定义，必须和Monet底层保持一致
 */
typedef NS_ENUM(NSInteger, TVKSRSeparateMode) {
    // 从中间展开
    // 说明：separateRatio从0变化为1，分隔线从中间向两边展开，中间区域部分为超分画面，两边区域部分为未超分画面，直至整个画面全为超分效果
    // 超分区域：图像[0.5 - 0.5 * separataRatio, 0.5 + 0.5 * separataRatio,]区域
    // 未超分区域：[0 , 0.5 - 0.5 * separataRatio] 区域和[0.5 + 0.5 * separataRatio, 1.0] 区域
    TVKSRSeparateModeCenterToEdge = 1,
    // 分隔线从左到右
    // 说明：separateRatio从0变化为1，分隔线从左到右移动，分隔线左边区域为超分画面，分隔线右边部分为未超分画面，直至整个画面全为超分效果
    // 超分区域：图像[0，separataRatio]区域
    // 未超分区域：[separataRatio , 1] 区域
    TVKSRSeparateModeLeftToRight = 2,
};

/**
 * 腾讯视频移动端超分辨率特效
 */
@protocol ITVKTVMSuperResolutionFx <ITVKVideoFx>

/// 设置分隔线模式，用于超分效果对比，可用作演示或者动效，对比超分前后的效果
/// @param separateMode 分隔线模式，详细请参见SeparateMode，默认为TVKSRSeparateModeCenterToEdge
- (void)setSeparateMode:(TVKSRSeparateMode)separateMode;

/// 设置分隔比例，可用作演示或者动效，对比超分前后的效果
/// @param separateRatio 超分占整个画幅的比例，取值范围[0.0, 1.0]，默认为:1。
/// 不同separateMode时，separateRatio所表示的超分区域和未超分区域细节不同，详细说明请查看SeparateMode定义
/// 注意：位置设置小于0.01时，由于超分区域过小，可认为不需进行超分运算，
/// 所以超分计算将会挂起，降低性能消耗，引擎会直接将输入显示在画面中
- (void)setSeparateRatio:(float)separateRatio;

/// 是否显示超分效果对比分隔线，可用作演示或者动效，能够区分超分和未超分区域的分隔线
/// @param showSeparateLine 是否显示超分分隔线，默认为NO
- (void)showSeparateLine:(BOOL)showSeparateLine;

/// 加载处理指定视频尺寸的超分模型，如果模型加载不成功，则不会有对应的超分特效
/// 如果模型已经加载成功，不能重复调用loadSRModelWithVideoSize加载
/// @param videoSize 视频尺寸
/// @return 加载成功返回YES，否则返回NO
- (BOOL)loadSRModelWithVideoSize:(CGSize)videoSize;

/// 设置模块是否passthrough直通，跳过超分推理引擎推理，直通时没有超分特效。默认为非直通。
/// @param passthrough 是否直通
- (void)setModulePassthrough:(BOOL)passthrough;

@end
