/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TVKAudioEffectInfo.h
 * @brief    音效信息描述
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/05/25
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

#import "ITVKAudioFx.h"

NS_ASSUME_NONNULL_BEGIN

/// @brief 音效信息描述
@interface TVKAudioEffectInfo : NSObject

/// 音效类型, 可通过TVKAudioFxProcessor后处理添加音效
@property (nonatomic, assign) TVKAudioEffectType effectType;

/// 音效名称
@property (nonatomic, copy) NSString *effectName;

/// 音效展示文案
@property (nonatomic, copy) NSString *effectShowName;

/// 音效是否需要会员
/// | limit | 说明 |
/// | 0     | 可免费播放 |
/// | 1     | 会员专享 |
@property (nonatomic, assign) int vipLimit;

/// 音效是否被当前选中
@property (nonatomic, assign, getter=isSelected) BOOL selected;

@end

NS_ASSUME_NONNULL_END
