//
//  TVKVideoTrackInfo.h
//  TVKPlayer
//  具体视频轨道信息描述
//  Created by andygao on 2020/2/26.
//  Copyright © 2020 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TVKMediaInfo.h"
#import "ITVKVideoView.h"
#import "TVKNetVideoInfo.h"
#import "TVKTrackInfo.h"

/// 注意！！！该文件中定义的所有枚举均对外，千万不要改现有枚举对应的值，这会导致依赖老版本tvk的sdk和依赖新版本tvk的工程一起编译运行后走到错误的逻辑中。
/// 别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!
typedef NS_ENUM(NSInteger, TVKVideoTrackInfoEvent) {
    TVKVideoTrackInfoEventUnknow,
};

NS_ASSUME_NONNULL_BEGIN

@class TVKVideoTrackInfo;

@protocol TVKVideoTrackInfoDelegate <NSObject>

/**
 *  @brief videoTrack 开始显示
 *
 *  @param videoTrackInfo 视频轨道信息实例
 */
- (void)videoTrackInfoShowingStart:(TVKVideoTrackInfo *)videoTrackInfo;

/**
 *  @brief videoTrack 因为deSelecte不再显示或者正常展示完成
 *
 *  @param videoTrackInfo 视频轨道信息实例
 */
- (void)videoTrackInfoShowingEnd:(TVKVideoTrackInfo *)videoTrackInfo;

/**
*  @brief videoTrack videoTrackc因为各种原因报错，通过此接口进行通知
*
*  @param videoTrackInfo 视频轨道信息实例
*  @param error 错误信息
*/
- (void)videoTrackInfo:(TVKVideoTrackInfo *)videoTrackInfo error:(NSError *)error;

/**
 *  @brief video track 一些事件通知
 *
 *  @param videoTrackInfo 视频轨道信息实例
 *  @param event 事件，参考TVKVideoTrackInfoEvent
 *  @param eventInfo 事件相关的扩展信息。详情请见TVKVideoTrackInfoEvent定义
 */
- (void)videoTrackInfo:(TVKVideoTrackInfo *)videoTrackInfo eventChanged:(TVKVideoTrackInfoEvent)event withExtraInfo:(nullable id)eventInfo;

/**
 *  @brief 后台返回的视频轨道信息，包括当前清晰度，清晰度列表等信息
 *
 *  @param videoTrackInfo 视频轨道信息实例
 *  @param netVideoInfo 正片视频信息
 */
// NOLINTNEXTLINE
- (void)videoTrackInfo:(TVKVideoTrackInfo *)videoTrackInfo netVideoInfo:(TVKNetVideoInfo *)netVideoInfo;

/**
 *  @brief 视频轨道的宽和高
 *  @param videoTrackInfo 视频轨道信息实例
 *  @param width       视频轨道的宽
 *  @param height      视频轨道的高
 */
- (void)videoTrackInfo:(TVKVideoTrackInfo *)videoTrackInfo trackVideoSizeChangedWithWidth:(NSInteger)width height:(NSInteger)height;

/**
 *  @brief 视频轨道的宽和高
 *  @param videoTrackInfo 视频轨道信息实例
 *  @param position 当前播放位置
 *  @param playablePosition 可播位置，即缓冲时长
*/
- (void)videoTrackInfo:(TVKVideoTrackInfo *)videoTrackInfo
       currentPosition:(NSTimeInterval)position
      playablePosition:(NSTimeInterval)playablePosition;
@end

/**
 视频轨道信息具体类
 */
@interface TVKVideoTrackInfo : TVKTrackInfo

/**
 * trackInfo信息变动的delegate
 */
@property (atomic, weak) id<TVKVideoTrackInfoDelegate> trackInfoDelegate;

/**
 * 展示视频的view，由外面创建好，传入。建议一个播放器对应一个videoView不要复用，若要复用，复用之前请调用TXVVideoView的removeAllSubviews;
 */
@property (atomic, strong) id<ITVKVideoView> trackVideoView;

/**
 * 获取保存的mediaInfo
 */
@property (nonatomic, strong, readonly) TVKMediaInfo *trackMediaInfo;

/**
 * 获取当前的netvideoInfo
 */
@property (nonatomic, strong, readonly) TVKNetVideoInfo *currentTrackNetVideoInfo;

/**
 * 获取视频宽高
 * @return 视频宽高
 */
- (CGSize)trackVideoSize;

@end

NS_ASSUME_NONNULL_END
