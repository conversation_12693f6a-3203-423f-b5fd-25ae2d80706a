/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     ITVKQQLiveAssetRequester.h
 * @brief    播放资源请求接口
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/7/07
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKPlayerDefine.h"

@class TVKMediaInfo;
@class TVKNetVideoInfo;
@class TVKUserInfo;

/// @brief 请求时需要传入的参数
@interface TVKQQLiveAssetRequestParam : NSObject
/// 详情请看TVKMediaInfo定义。
@property (nonatomic, strong) TVKMediaInfo *mediaInfo;
/// 非必填，默认为TVKMediaFormatAuto
@property (nonatomic, assign) TVKMediaFormat requestMediaFormat;
/// 详情请看TVKUserInfo定义
@property (nonatomic, strong) TVKUserInfo *userInfo;

@end

/// @brief 视频信息Delegate，调用方实现此Delegate监听视频信息的回调
@protocol TVKQQLiveAssetRequesterDelegate <NSObject>

 /// 媒体信息获取完成
 /// @param netVideoInfo 媒体信息，详情请见TVKNetVideoInfo
- (void)onSuccess:(TVKNetVideoInfo *)netVideoInfo;

@optional
/// 媒体信息获取完成   主要用于预下载、请求dlna走下载组件本地代理的场景
/// @param  videoInfo 媒体信息，点播是TVKVodPlayInfo类型，直播是TVKLivePlayInfo
- (void)onSuccessWithPlayInfo:(id)videoInfo;

/// 媒体信息获取失败
/// @param error 错误信息
- (void)onFailure:(NSError *)error;

@end

/// @brief 播放媒体信息获取接口
@protocol ITVKQQLiveAssetRequester <NSObject>
/// 回调
@property (nonatomic, weak) id<TVKQQLiveAssetRequesterDelegate> delegate;

/// 调用该方法开始获取媒体信息
/// @param param 获取媒体信息需要的参数，请见TVKQQLiveAssetRequestParam
- (void)requestWithParam:(TVKQQLiveAssetRequestParam *)param;

/// 调用该方法开始获取dlna媒体信息
/// @param param 获取媒体信息需要的参数，请见TVKQQLiveAssetRequestParam
- (void)requestForDlnaWithParam:(TVKQQLiveAssetRequestParam *)param;

/// 调用该方法查询直播信息
/// @param param 获取媒体信息需要的参数，请见TVKQQLiveAssetRequestParam
- (void)inquireLivePreviewInfoWithParam:(TVKQQLiveAssetRequestParam *)param;

/// 停止获取媒体信息，如果在开始获取信息后想停止，可以调用此方法，就不会收到回调。
- (void)stop;

@end
