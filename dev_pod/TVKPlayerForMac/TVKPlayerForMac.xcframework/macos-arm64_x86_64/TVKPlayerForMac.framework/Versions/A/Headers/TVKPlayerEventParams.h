/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKPlayerEventParams.h
 * @brief    细分播放事件对象
 * <AUTHOR> chen
 * @version  1.0.0
 * @date     2021/4/6
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

FOUNDATION_EXPORT NSString *const kTVKPlayerEventParamMediaInfoKey;
FOUNDATION_EXPORT NSString *const kTVKPlayerEventParamUserInfoKey;
FOUNDATION_EXPORT NSString *const kTVKPlayerEventParamVideoDurationKey;
FOUNDATION_EXPORT NSString *const kTVKPlayerEventParamReportInfoEventKey;
FOUNDATION_EXPORT NSString *const kTVKPlayerEventParamReportInfoMapKey;
FOUNDATION_EXPORT NSString *const kTVKPlayerEventParamAdTypeKey;
FOUNDATION_EXPORT NSString *const kTVKPlayerEventParamAdDurationKey;
FOUNDATION_EXPORT NSString *const kTVKPlayerEventParamNetVideoInfoKey;
FOUNDATION_EXPORT NSString *const kTVKPlayerEventParamAudioDecoderTypeKey;


/**
 细分播放事件对象
 */
@interface TVKPlayerEventParams : NSObject
/**
 当前播放flowId
 */
@property (nonatomic, copy, readonly) NSString *flowId;
/**
 视频播放的时间点
 */
@property (nonatomic, assign) NSTimeInterval videoPos;
/**
 事件产生的时间点
 */
@property (nonatomic, assign) NSTimeInterval eventTime;
/**
 扩展参数，目前包括adtype ， adduration
 */
@property (nonatomic, strong, nullable) NSDictionary *extraParams;

/**
 * 创建和初始化一个EventParams实例
 * @param videoPos 当前介质播放的时间点
 * @param flowId 当前播放的FlowId
 * @return eventParams的实例
 */
- (instancetype)initWithVideoPos:(NSTimeInterval)videoPos flowId:(NSString *)flowId;
@end
