/*****************************************************************************
 * @copyright Copyright (C), 1998-2021, Tencent Tech. Co., Ltd.
 * @file     ITVKVideoFxProcessor.h
 * @brief    特效工厂
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/2/19
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#ifndef ITVKVideoFxProcessor_h
#define ITVKVideoFxProcessor_h

#import "TVKAbstractProcessor.h"
#import "ITVKVideoFx.h"

/**
 *  视频后处理模块，调用者获取后，可添加以及删除特效
 *  将processor链接至播放器中，后处理效果即可生效
 */
@protocol ITVKVideoFxProcessor <TVKAbstractProcessor>

/**
 * 添加特效
 */
- (BOOL)addVideoEffect:(id<ITVKVideoFx>)effect;

/**
 * 删除特效
 */
- (void)removeEffect:(id<ITVKVideoFx>)effect;

/**
 * 判断是否已存在特效
 */
- (BOOL)containsEffect:(id<ITVKVideoFx>)effect;

/// 判断是否存在指定类型的特效
/// @param effectType 特效类型
/// @return 存在指定类型的特效返回YES，否则返回NO
- (BOOL)containsEffectWithType:(TVKVideoFxType)effectType;

@end

#endif /* ITVKVideoFxProcessor_h */
