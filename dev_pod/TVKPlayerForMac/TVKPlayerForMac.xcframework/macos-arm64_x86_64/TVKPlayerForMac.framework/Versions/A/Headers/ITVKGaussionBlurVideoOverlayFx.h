/*****************************************************************************
 * @copyright Copyright (C), 1998-2021, Tencent Tech. Co., Ltd.
 * @file     ITVKGaussionBlurVideoOverlayFx.h
 * @brief    毛玻璃视频叠加特效
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/4/25
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITVKVideoFx.h"

/* 坐标系示意
 * (0,1)
 *   ^
 *   |Y
 *   |
 *   |
 *   |
 *   |
 *   |             X
 *   +--------------->
 *  (0,0)            (1,0)
 *  左下角为原点，x,y均在区间[0,1]中
 *  X, Y轴需归一化至区间[0,1];
 */

/**
 * 高斯模糊+视频叠加后处理
 * GPU处理管线示意：'[]'代表GPU操作,'()'代表输入,'*'代表参数
 * 1. 毛玻璃处理流程
 * (输入图像 + *裁剪区域)->[裁剪]->(裁剪图像)->[高斯处理]->(处理后图像 + *缩放大小)->[缩放]->(背景毛玻璃图)
 * 2. 混合流程：
 * (输入图像 + *叠加位置)--|
 *                     |---->[GPU混合]--->输出
 * (背景毛玻璃图) -------|
 */
@protocol ITVKGaussionBlurVideoOverlayFx <ITVKVideoFx>

/* 毛玻璃处理区域示例
 * +---------------------------------+
 * |                                 |
 * |            +-----------+        |
 * |            |           |        |
 * |            |           |        |
 * |            |h=0.7      |        |
 * |            |           |        |
 * |            |           |        |
 * |            |   w=0.4   |        |
 * |            +-----------+        |
 * |            (.4,.2)              |
 * +---------------------------------+
 */
/**
 * 设置毛玻璃处理区域(待模糊区域)，若设置为[0,0,1,1]则代表对全图进行模糊
 * @param x 矩形区域左下顶点的x坐标
 * @param y 矩形区域左下顶点的x坐标
 * @param w 矩形区域的归一化宽度
 * @param h 矩形区域的归一化高度
 */
- (void)setBlurRect:(float)x y:(float)y width:(float)w height:(float)h;

/* 放大示意
 *                                                    +------------------+
 *                                                    |                  |
 *                                                    |                  |
 * +---------------------------------+                |                  |
 * |                                 |                |                  |
 * |            +-----------+        |                |          h=1280pixel
 * |            |           |        |                |                  |
 * |            |           |        |                |                  |
 * |            |h=0.7      |        |       resize   |                  |
 * |            |           +--------+--------------> |                  |
 * |            |           |        |                |                  |
 * |            |   w=0.4   |        |                |                  |
 * |            +-----------+        |                |                  |
 * |            (.4,.2)              |                |                  |
 * +---------------------------------+                |                  |
 *                                                    |                  |
 *                                                    |        w=720pixel|
 *                                                    +------------------+
 */
/**
 * 设置毛玻璃图缩放后的宽高:单位(像素)
 * @param w 宽
 * @param h 高
 */
- (void)setBlurBackgroundSize:(int)w Height:(int)h;

/* 叠加示意
 * +---------------------+
 * |                     |
 * |                     |
 * |                     |
 * |  +----------------+ |
 * |  |                | |
 * |  |                | |
 * |  |h=0.3           | |
 * |  |                | |
 * |  |       w= 0.8   | |
 * |  +----------------+ |
 * |  (.1,.6)            |
 * |                     |
 * |                     |
 * |                     |
 * |                     |
 * |                     |
 * |                     |
 * |                     |
 * +---------------------+
 */
/**
 * 设置输入图像的*叠加位置，若设置为[0,0,1,1]则代表叠加图像将会完全覆盖模糊后的图像
 * @param x 矩形区域左下顶点的x坐标
 * @param y 矩形区域左下顶点的x坐标
 * @param w 矩形区域的归一化宽度
 * @param h 矩形区域的归一化高度
 */
- (void)setOverlayRect:(float)x y:(float)y width:(float)w height:(float)h;

@end
