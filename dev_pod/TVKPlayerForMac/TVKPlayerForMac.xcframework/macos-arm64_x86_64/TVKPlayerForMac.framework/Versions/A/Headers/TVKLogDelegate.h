/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKLogDelegate.h
 Author      : liyukuan
 Version     : 1.0
 Date        : 17/4/7
 Description : 日志打印协议和本地日志上传接口定义
 History     : 17/4/7 初始版本
 ***********************************************************/

#import <Foundation/Foundation.h>
/**
 @discussion 日志等级.
 */
typedef enum {
    TVKLogLevelVerbose,
    TVKLogLevelDebug,
    TVKLogLevelInfo,
    TVKLogLevelSystem,
    TVKLogLevelWarning,
    TVKLogLevelError,
} TVKLogLevel;

/**
@brief 播放器内部没有日志打印模块，为便于问题定位，APP要实现此日志打印接口，方便将播放器内的日志打印到APP的日志中，方便问题定位
      下面2个可选方法，只需要实现一个就可以，会优先判断是否实现了第一个接口，如果实现了第一个接口，则通过第一个接口输出日志（即使第二个接口也实现了，第二个接口也不会回调）
      新增第二个接口是为了在Swift中实现日志回调
*/
@protocol TVKLogDelegate <NSObject>

@optional

/**
@brief 日志打印接口
@param logLevel 日志打印级别
@param tag 日志tag
@param file 文件名称
@param function 函数名称
@param line 代码行
@param format format
@param args args
*/
- (void)logWithLevel:(TVKLogLevel)logLevel
                 tag:(NSString *)tag
                file:(const char *)file
            function:(const char *)function
                line:(NSUInteger)line
              format:(NSString *)format
                args:(va_list)args;

@optional
/**
@brief 日志打印接口，方便导出给Swift使用，上面的变参接口在Swift里不能使用
@param logLevel 日志打印级别
@param tag 日志tag
@param file 文件名称
@param function 函数名称
@param line 代码行
@param content 日志内容
*/
- (void)logWithLevel:(TVKLogLevel)logLevel
                 tag:(NSString * _Nonnull)tag
                file:(NSString * _Nonnull)file
            function:(NSString * _Nonnull)function
                line:(NSUInteger)line
             content:(NSString * _Nonnull)content;
@end

/**
@brief 本地日志上传定义，为便于一些播放等问题的定位，需要将本地的日志上传到后台，便于进一步的问题分析定位
*/
@protocol TVKLogReportDelegate <NSObject>

@required

/**
@brief 实现此接口后，当此接口被调用时，触发上传本地日志并携带logInfo信息到后天,用于问题分析.此接口主要用于腾讯视频
@param logInfo 上报的信息，用于腾讯视频后台解析过滤
*/
- (void)onLogReport:(NSDictionary *)logInfo;

@end
