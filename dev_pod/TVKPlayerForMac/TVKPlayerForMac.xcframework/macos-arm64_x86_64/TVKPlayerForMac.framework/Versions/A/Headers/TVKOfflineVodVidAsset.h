/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TVKOfflineVodVidAsset.h
 * @brief    离线点播资源
 * <AUTHOR>
 * @version  1.0.0
 * @date     2022/9/10
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKAssetBase.h"

NS_ASSUME_NONNULL_BEGIN

/// 离线类型
typedef NS_ENUM(NSUInteger, TVKOfflineType) {
    // 完全离线
    TVKOfflineTypeCompleteOffline = 0,
    // 部分离线，边下边播
    TVKOfflineTypeInCompleteOffline = 1,
};

/// @brief 描述见文件头
@interface TVKOfflineVodVidAsset : TVKAssetBase

/// 视频id.
@property (nonatomic, copy) NSString *vid;

/// 专辑id(cid)
@property (nonatomic, copy) NSString *cid;

/// 栏目id(lid).非必填字断，如果没有则无须设置
@property (nonatomic, copy) NSString *lid;

/// 离线类型
@property (nonatomic, assign) TVKOfflineType offlineType;

/// 初始化方法
/// @param vid 视频id
/// @param cid 专辑id
/// @param offlineType 离线类型
- (instancetype)initWithVid:(NSString *)vid
                        cid:(NSString *)cid
                offlineType:(TVKOfflineType)offlineType;

@end

NS_ASSUME_NONNULL_END
