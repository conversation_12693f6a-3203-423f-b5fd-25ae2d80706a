/************************************************************
 * @copyright Copyright (C), 1998-2025, Tencent Tech. Co., Ltd.
 * @file     ITVKPictureSRProcessor.h
 * @brief    图片超分处理器接口
 * <AUTHOR>
 * @version  1.0.0
 * @date     2025/02/19
 * @license     GNU General Public License (GPL)
 ***********************************************************/

#import <Foundation/Foundation.h>
#import "TVKPictureSRProcessResult.h"

NS_ASSUME_NONNULL_BEGIN

/// 图片超分处理完回调
typedef void(^TVKPictureSRCompletionBlock)(TVKPictureSRProcessResult *processResult);

/// 图片超分处理器事件回调
@protocol ITVKPictureSRProcessorDelegate <NSObject>
/// 图片超分处理器加载完成时回调，超分处理器内部线程
- (void)onProcessorPrepared;

/// 图片超分处理器加载失败时回调，超分处理器内部线程
/// 加载失败的情况：模型下载不成功，设备不支持等
- (void)onProcessorPrepareFailed;

/// 图片超分处理器destory回调
- (void)onProcessorDestroyed;
@end

/// @brief 图片超分处理器接口
@protocol ITVKPictureSRProcessor <NSObject>
/// delegate
@property (nonatomic, weak) id<ITVKPictureSRProcessorDelegate> delegate;

/// 异步加载图片超分处理器，使用默认内部执行超分任务的并发数
/// 图片超分处理器需要加载深度学习模型，会有一点耗时
/// 初始化完成后通过ITVKPictureSRProcessorDelegate#onProcessorReady回调消息通知加载完成，可以处理图片超分任务
- (void)prepareAsync;

/// 异步加载图片超分处理器，可以指定内部执行超分任务的并发数
/// 图片超分处理器需要加载深度学习模型，会有一点耗时
/// 初始化完成后通过ITVKPictureSRProcessorDelegate#onProcessorReady回调消息通知加载完成，可以处理图片超分任务
/// @param count 内部执行超分任务的并发数，大于等于1
- (void)prepareAsync:(NSUInteger)count;

/// 处理器是否已经加载完成，未加载完成时无法进行图片超分处理任务
- (BOOL)isPrepared;

/// 获取图像超分模型名
/// 1、如果手机不支持图片超分，此接口返回nil
/// 2、处理器未加载完成前获取的为nil，处理器加载完成后可以获取图片超分模型名称（如：1211130312.0.0.3.xnet）
- (nullable NSString *)pictureSRModelName;

/// 获取图片超分处理器并发数，即内部执行超分任务的引擎数量
/// 建议在收到onProcessorPrepared回调，或者isPrepared返回YES时调用此接口
/// @return 返回图片超分处理器执行超分任务的引擎数量
- (NSInteger)getProcessorConcurrency;

/// 是否可以处理指定宽高的图片，线程安全
/// @param width 图片宽
/// @param height 图片高
- (BOOL)canProcessImageWithWidth:(NSInteger)width height:(NSInteger)height;

/// 异步方式，对输入图像数据进行超分处理，线程安全
/// @param image 图像数据
/// @param block 处理完成回调，TVKPictureSRProcessResult.imageName为空字符串
#if TARGET_OS_OSX
- (void)processImageAsync:(NSImage *)image
          completionBlock:(TVKPictureSRCompletionBlock)block;
#else
- (void)processImageAsync:(UIImage *)image
          completionBlock:(TVKPictureSRCompletionBlock)block;
#endif

/// 异步方式，对输入图像数据进行超分处理，线程安全
/// @param image 图像数据
/// @param name 图片名称，用于与回调TVKPictureSRProcessResult.imageName对比
/// @param block 处理完成回调
#if TARGET_OS_OSX
- (void)processImageAsync:(NSImage *)image
                     name:(NSString *)imageName
          completionBlock:(TVKPictureSRCompletionBlock)block;
#else
- (void)processImageAsync:(UIImage *)image
                     name:(NSString *)imageName
          completionBlock:(TVKPictureSRCompletionBlock)block;
#endif

/// 同步方式，对输入图像数据进行超分处理，线程安全
/// @param image 图像数据
/// @param name 图片名称，用于与回调TVKPictureSRProcessResult.imageName对比
/// @param timeoutMs 超时时间，单位毫秒
/// @return 如果处理成功返回TVKPictureSRProcessResult对象，如果超时或者输入数据异常，返回nil
#if TARGET_OS_OSX
- (TVKPictureSRProcessResult *)processImageSync:(NSImage *)image
                                        timeout:(NSTimeInterval)timeoutMs;
#else
- (TVKPictureSRProcessResult *)processImageSync:(UIImage *)image
                                        timeout:(NSTimeInterval)timeoutMs;
#endif

/// 同步方式，对输入图像数据进行超分处理，线程安全
/// @param image 图像数据
/// @param name 图片名称，用于与回调TVKPictureSRProcessResult.imageName对比
/// @param timeoutMs 超时时间，单位毫秒
/// @return 如果处理成功返回TVKPictureSRProcessResult对象，如果超时或者输入数据异常，返回nil
#if TARGET_OS_OSX
- (TVKPictureSRProcessResult *)processImageSync:(NSImage *)image
                                           name:(NSString *)imageName
                                        timeout:(NSTimeInterval)timeoutMs;
#else
- (TVKPictureSRProcessResult *)processImageSync:(UIImage *)image
                                           name:(NSString *)imageName
                                        timeout:(NSTimeInterval)timeoutMs;
#endif

/// 异步销毁图片超分处理器
/// 图片超分处理器销毁过程中会清空所有的图片超分任务，释放资源
/// 销毁完成后通过ITVKPictureSRProcessorDelegate#onProcessorDestroyed回调消息通知销毁完成
/// 注意：在销毁过程中不要调用其他接口。如果销毁之后还想要进行图片超分任务，必须调用prepareAsync加载处理器
- (void)destroyAsync;

@end

NS_ASSUME_NONNULL_END
