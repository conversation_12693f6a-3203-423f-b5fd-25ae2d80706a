/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKLivePlayInfo.h
 * @brief    直播播放所需信息，主要是由CGI返回的信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2019/9/12
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "TVKMediaPlayInfo.h"
#import "TVKLiveSeeBackBaseInfo.h"

/// 直播追帧策略
@interface TVKLiveFastForwardInfo : NSObject

/// 低延时策略 0:无，1:加速播放，2:丢帧 (暂未实现)
@property (nonatomic, assign) int type;

/// 加速播放倍率，在type为1时有效
@property (nonatomic, assign) float speed;

/// 开始追帧的buffer水位
@property (nonatomic, assign) float startBufferDurationSec;

/// 结束追帧的buffer水位
@property (nonatomic, assign) float endBufferDurationSec;

@end

/// 直播播放所有的信息
@interface TVKLivePlayInfo : TVKMediaPlayInfo

/// 节目id
@property (nonatomic, copy) NSString *pid;

/// 机位id
@property (nonatomic, copy) NSString *chid;

/// 流加密参数
@property (nonatomic, copy) NSDictionary *cryptParam;

/// 直播错误码
@property (nonatomic, assign) NSInteger liveErroCode;

/// 当前频道是否需要付费，0为不付费，1为付费
@property (nonatomic, assign) BOOL needPay;

/// 请求用户是否对频道付费，1表示已付费，0为未付费，该字段仅在needPlay字段为1时有效
@property (nonatomic, assign) BOOL isUserPay;

/// 后台指定的目标延迟，单位秒
@property (nonatomic, assign) int expectDelayTime;

/// 直播流的播放限时（本次试看剩余时长)，单位秒，0为不限制
@property (nonatomic, assign) CGFloat liveCurrentPreviewDurationSec;

/// 如果节目支持试看，返回请求用户当天可试看总次数
@property (nonatomic, assign) int livePreviewCount;

/// 如果节目支持试看，返回请求用户当天剩余可试看次数
@property (nonatomic, assign) int liveRestPreviewCount;

/// 直播流的每次总的可试看时长，单位秒，0为不支持。对单个直播，值固定，比如一个直播每次试看时长为300s，如果已经请求了一次试看，在100s后又请求了一次试看，
/// 第二次请求时因为第一次试看时间没到，仍为第一次试看，此时previewDurationSec仍为300s，但是第二次实际可试看时长currentPreviewDurationSec=200s
@property (nonatomic, assign) CGFloat livePreviewDurationSec;

/// 直播回看信息
@property (nonatomic, strong) TVKLiveSeeBackBaseInfo *seeBackBaseInfo;

///直播排队 0:未排队，1:排队中，2:已出队
@property (nonatomic, assign) int queueStatus;

/// 直播排队，返回排队的排名
@property (nonatomic, assign) int64_t queueRank;

/// 开通会员后能否插队 0否 1是
@property (nonatomic, assign) int queueVipJump;

/// 排队的会员key,返回给app,轮询时使用
@property (nonatomic, copy) NSString *queueSessionKey;

///流协议。1:flv, 2:hls, 4:rtmp, 8:dash, 16:webrtc, 32:trtc
@property (nonatomic, assign) int stream;

/// 是否为全景视频,0为非全景，1为全景
@property (nonatomic, assign) int live360;

/// 音频格式。1=aac，2=dolby（新增字段）
@property (nonatomic, assign) int acode;

/// 视频编码格式。 1=h264，2=hevc（新增字段）
@property (nonatomic, assign) int vcode;

/// 画质增强类型  2:杜比 4:hdrvivid
@property (nonatomic, assign) TVKLiveHeightenType heightenType;

/// cdn名称
@property (nonatomic, copy) NSString *cdnName;

/// 清晰度名称 (sd/hd等）
@property (nonatomic, copy) NSString *defn;

/// 加密类型 (3:chacha20)
@property (nonatomic, assign) int drmType;

/// 解密因子：随机数
@property (nonatomic, copy) NSString *random;

/// 解密因子: Number once
@property (nonatomic, copy) NSString *nonce;

/// 解密因子：解密秘钥
@property (nonatomic, copy) NSString *deckey;

/// 直播类型：当前用于上报
@property (nonatomic, assign) int liveType;

/// 加密类型，透传给app。0 不加密、1 弱加密、2 强加密
@property (nonatomic, assign) NSInteger streamSecret;

/// 当前帧率
@property (nonatomic, assign) int curFps;

/// 当前流码率值。单位kbps
@property (nonatomic, assign) int currentBitrate;

/// 追帧策略
@property (nonatomic, strong) TVKLiveFastForwardInfo *fastForwardInfo;

@end
