/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TVKPreloadDefine.h
 * @brief    预加载回调、参数定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2022/8/04
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#import "TVKDownloadProgressInfo.h"

/// 预加载参数
@interface TVKPreloadParam : NSObject

/// 预下载字节数
@property (nonatomic, assign) NSInteger preloadSizeByte;

/// 预下载时长，单位:毫秒
@property (nonatomic, assign) NSInteger preloadDurationMs;

/// 预下载http启动时间，超过该值则使用http下载，单位:毫秒
@property (nonatomic, assign) int64_t preloadHttpStartTimeMs;

/// 是否仅进行cgi预加载，默认为NO，设置为YES则仅预加载cgi，不进行数据预加载
@property (nonatomic, assign) BOOL isPreloadVinfoOnly;

@end

/// 预加载回调
@protocol ITVKPreloadDelegate <NSObject>

/// 预加载成功
/// @param preloadId 预加载任务id
- (void)onPreloadSuccessWithPreloadId:(int)preloadId;

/// 预加载失败
/// @param preloadId 预加载任务id
/// @param error 错误信息
- (void)onPreloadErrorWithPreloadId:(int)preloadId error:(NSError *)error;

/// 预加载进度回调
/// @param preloadId 预加载任务id
/// @param downloadProgressInfo 预加载进度回调信息封装，详见TVKDownloadProgressInfo定义
- (void)onPreloadDownloadProgressUpdateWithPreloadId:(int)preloadId downloadProgressInfo:(TVKDownloadProgressInfo *)downloadProgressInfo;

@end
