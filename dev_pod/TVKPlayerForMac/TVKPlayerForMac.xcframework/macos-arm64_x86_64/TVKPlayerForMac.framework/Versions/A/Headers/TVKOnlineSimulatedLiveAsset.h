/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TVKOnlineSimulatedLiveAsset.h
 * @brief    伪直播（轮播）asset
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/11/7
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKAssetBase.h"

NS_ASSUME_NONNULL_BEGIN

/// 伪直播（轮播）asset
@interface TVKOnlineSimulatedLiveAsset : TVKAssetBase

/// 伪直播节目id
@property (nonatomic, copy) NSString *pid;

/// 伪直播机位id
@property (nonatomic, copy) NSString *chid;

/// 伪直播业务场景参数， 由业务侧生成，影响下发内容
@property (nonatomic, copy) NSString *appScene;

/// 默认初始化方法不可用
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

/// 初始化方法
/// @param pid 伪直播节目id
/// @param chid 伪直播机位id
/// @param appScene 伪直播业务场景参数， 由业务侧生成，影响下发内容
- (instancetype)initWithPid:(NSString *)pid chid:(NSString *)chid appScene:(NSString *)appScene;

@end

NS_ASSUME_NONNULL_END
