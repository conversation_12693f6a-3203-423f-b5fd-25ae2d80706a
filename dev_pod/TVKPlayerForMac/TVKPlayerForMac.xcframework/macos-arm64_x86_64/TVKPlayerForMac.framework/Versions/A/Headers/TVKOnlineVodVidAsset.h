/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKOnlineVodVidAsset.h
 * @brief    Vid资源类型，主要用于腾讯视频Vid播放
 * <AUTHOR> chen
 * @version  1.0.0
 * @date     2020/12/7
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKAssetBase.h"

/// @brief Vid资源类型，主要用于腾讯视频Vid播放
@interface TVKOnlineVodVidAsset : TVKAssetBase

/// 视频id.
@property (nonatomic, copy) NSString *vid;

/// 专辑id.
@property (nonatomic, copy) NSString *cid;

/// 栏目id(lid).非必填字断，如果没有则无须设置
@property (nonatomic, copy) NSString *lid;

/// 初始化方法
/// @param vid 视频id
/// @param cid 专辑id
- (instancetype)initWithVid:(NSString *)vid cid:(NSString *)cid;


@end
