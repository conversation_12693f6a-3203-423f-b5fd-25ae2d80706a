/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TVKLivePidAsset.h
 * @brief    直播pid资源类，用于腾讯视频直播
 * <AUTHOR>
 * @version  1.0.0
 * @date     2022/3/10
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#import "TVKAssetBase.h"

NS_ASSUME_NONNULL_BEGIN

/// 描述见文件头
@interface TVKLivePidAsset : TVKAssetBase

/// 直播节目id
@property (nonatomic, copy) NSString *pid;
/// 直播机位id
@property (nonatomic, copy) NSString *chid;
/// 房间id，用于公有云直播
@property (nonatomic, copy) NSString *roomid;
/// 主播id，用于公有云直播
@property (nonatomic, copy) NSString *anchorid;
/// 直播业务的appid，用于区分直播中台业务
@property (nonatomic, copy) NSString *liveAppid;

- (instancetype)initWithPid:(NSString *)pid chid:(NSString *)chid;


@end

NS_ASSUME_NONNULL_END
