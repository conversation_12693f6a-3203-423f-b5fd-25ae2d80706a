//
//  TVKVODADInfo.h
//  TVKPlayer
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/10/26.
//  Copyright © 2019 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class TVKVODPADInfo;

/// @brief getvinfo返回的广告信息，在vl.vi.ad节点下
@interface TVKVODADInfo : NSObject
/// 广告的session ID
@property (nonatomic, strong) NSString *adsid;
/// 创意中插广告列表原始数据
@property (nonatomic, strong) NSString *adPInfo;
/// 转换为对象后的 创意中插广告列表
@property (nonatomic, strong) NSArray<TVKVODPADInfo *> *pADInfos;
/// 广告透传信息
@property (nonatomic, copy) NSString *adPass;
@end

typedef NS_ENUM(NSInteger, TVKVODADOptType) {
    /// 植入广告
    TVKVODADOptTypeVideoIn = 1,
    /// 创意中插(hls标版)
    TVKVODADOptTypeVideoPlugin = 2,
};

/// @brief 广告节点下的创意中插广告节点
@interface TVKVODPADInfo : NSObject
/// 片段的vid
@property (nonatomic, strong, nullable) NSString *vid;
/// 广告开始时间
@property (nonatomic, assign) double startTime;
/// 广告分片时长
@property (nonatomic, assign) double duration;
/// 广告分片偏移，单位秒
@property (nonatomic, assign) double offsetTime;
/// 广告类型
@property (nonatomic, assign) NSInteger optType;
/// hls标版广告形态
/// 0: 未知 1: 普通 2: 高光
@property (nonatomic, assign) NSInteger hlsType;

@end

NS_ASSUME_NONNULL_END
