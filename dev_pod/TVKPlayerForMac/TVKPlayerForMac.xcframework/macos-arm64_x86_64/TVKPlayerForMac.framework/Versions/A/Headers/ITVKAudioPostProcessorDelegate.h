/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITVKAudioPostProcessorDelegate.h
 * @brief    audio后处理delegate
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/06/16
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITVKAudioFx.h"

/// @brief 音频后处理回调
@protocol ITVKAudioPostProcessorDelegate <NSObject>

/**
 * @brief 音效列表更新
 * 音效列表更新，收到此回调，可通过[TVKAudioFxProcessor getSupportedAudioEffectInfo]获取更新的音效列表
 */
- (void)onSupportedAudioEffectInfoUpdate;

/**
 * @brief 因内部原因音效被移除
 *        1. 切换清新度等重新请求后台下发的音效列表不再包含当前音效
 *        2. 因自研播放器播放失败导致使用系统播放器时，而无法使用音效
 *  @param effect 失效的音效
 */
- (void)onCurrentEffectDisable:(id<ITVKAudioFx>)effect;

@end
