/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     ITVKAsset.h
 * @brief    资源描述接口类
 * <AUTHOR> chen
 * @version  1.0.0
 * @date     2020/12/1
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

#pragma once

/// TVKPlayer资源类型
/// 注意！！！这些定的所有枚举均对外，千万不要改现有枚举对应的值，这会导致依赖老版本tvk的sdk和依赖新版本tvk的工程一起编译运行后走到错误的逻辑中。
/// 别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!
typedef NS_ENUM(NSUInteger, TVKAssetType) {
    /// --------------------------------------------------------------
    /// TVK定义的非腾讯视频资源播放                                                            合法bit 00-07
    /// --------------------------------------------------------------
    // 未定义
    TVKAssetTypeUnknow             = 0,
    // url资源
    TVKAssetTypeUrl                = 1<<1,

    /// --------------------------------------------------------------
    /// TVK定义的腾讯视频直播资源播放，利用直播后台服务的资源类型       合法bit 08-15
    /// --------------------------------------------------------------
    // 直播pid资源
    TVKAssetTypeLivePid            = 1<<8,
    // 直播sid资源
    TVKAssetTypeLiveSid            = 1<<9,

    /// --------------------------------------------------------------
    /// TVK定义的腾讯视频点播资源播放，利用点播后台服务的资源类型        合法bit 16-23
    /// --------------------------------------------------------------
    // 在线点播xml资源
    TVKAssetTypeOnlineVodXml       = 1<<16,
    // 在线点播vid资源
    TVKAssetTypeOnlineVodVid       = 1<<17,
    // 离线点播vid资源
    TVKAssetTypeOfflineVodVid      = 1<<18,
    // 点播秒播资源
    TVKAssetTypeOnlineVodQuickPlay = 1<<19,
    // 伪直播（轮播）资源,  业务体现是直播，但是走点播播控后台请求
    TVKAssetTypeOnlineSimulatedLiveAsset = 1<<20,
    
    /// 后续新增asset类型必须按照上面划分的三个区段放置
};

/// 资源描述接口
@protocol ITVKAsset <NSObject>

/// 资源类型
@property (nonatomic, assign, readonly) TVKAssetType assetType;

/// 资源是否合法
- (BOOL)isAssetValid;
@end
