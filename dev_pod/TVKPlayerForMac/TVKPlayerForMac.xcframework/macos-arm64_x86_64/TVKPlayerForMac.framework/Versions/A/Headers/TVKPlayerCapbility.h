/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKPlayerCapbility.h
 Author      : liyukuan
 Version     : 1.0
 Date        : 2018/1/2
 Description : 对外提供的播放器能力查询接口，比如是否支持hevc等
 History     : 2018/1/2 初始版本
 ***********************************************************/

#import <Foundation/Foundation.h>
#import "TVKPlayerDefine.h"
#import "ITVKAsset.h"
/// 分辨率级别
typedef NS_ENUM(NSInteger, TVKResolutionLevel) {
    kTVKResolutionLevelSD,
    kTVKResolutionLevelHD,
    kTVKResolutionLevelSHD,
    kTVKResolutionLevelFHD,
    kTVKResolutionLevelUHD,
};

/// 音频格式类型
typedef NS_ENUM(NSInteger, TVKAudioFormatType) {
    TVKAudioFormatTypeAAC,
    TVKAudioFormatTypeDolbySurround,
    TVKAudioFormatTypeDolbyAtmos,
    TVKAudioFormatTypeDolby2_0,
    TVKAudioFormatTypeAAC5_1,
    TVKAudioFormatTypeAudioVivid5_1_4,
    TVKAudioFormatTypeFlac,
};

/// HDR类型定义,与内核枚举值保持一致，不要改动
typedef NS_ENUM(NSInteger, TVKHdrType) {
    TVKHdrTypeNone             = -1,
    /// HDR10
    TVKHdrTypeHDR10            = 0,
    /// HDR DolbyVision
    TVKHdrTypeDolbyVision      = 2,
    /// HDR Vivid(CUVA)
    TVKHdrTypeHDRVivid         = 4,
};

/// @brief 对外提供的播放器能力查询接口，比如是否支持hevc等
@interface TVKPlayerCapbility : NSObject

/**
 * @brief 在线播放能力查询
 * @return 一个包含能力参数的字典，key和value都是NSString类型，key-value如下
 *         key                  : value
 *         spaudio              : 音频能力，bitset形式，1:支持纯音频，2:Dolby Surround 4:Dolby Atoms，
 *                                例：如果既支持纯音频，又支持Dolby Surround和Atoms，则取值为7
 *         spvideo              : 视频能力，bitset形式，4:支持HDR10，8:支持DolbyVision
 *         spwm                 : 支持的水印，0:不支持，1:支持静态水印 2:支持动态水印
 *         defnpayver           : 支持的清晰度付费能力，bitset形式，1:支持1080P付费，2:支持4K付费
 *         hevclv               : hevc level
 *         spsrt                : 是否支持软字幕，1:支持，0或没有该字段:不支持
 *         enable_online_vod_P2P: p2p是否打开, 0:关闭，1:打开
 */
+ (NSDictionary *)capabilityParams;

/**
 * @brief 离线线播放能力查询
 * @return 一个包含能力参数的字典，key和value都是NSString类型，key-value取值同上。
 */
+ (NSDictionary *)capabilityParamsForOffline;

/**
 * @brief 是否支持多路流同步
 */
+ (BOOL)isSyncVideoTrackSupport;

/**
 * @brief 支持的HEVC机型能力值，不同机型分配不同level，用于请求hevc
 */
+ (NSUInteger)hevcLevel;

/**
 * @brief 支持的VVC机型能力值，不同机型分配不同level，用于请求vvc
 */
+ (NSUInteger)vvcLevel;

/**
 * @brief 支持的AV1的能力值，不同机型分配不同level，用于请求av1
 */
+ (NSUInteger)av1Level;

/**
 * @brief 支持的AVS3的能力值，不同机型分配不同level，用于请求AVS3
 */
+ (NSUInteger)avs3Level;

/**
 * @brief 通过播放类型，获取播放器建议使用的媒体格式
 *
 * @param playType 播放类型，参考TVKPlayType
 */
+ (TVKMediaFormat)suggestVideoFormatWithPlayType:(TVKPlayType)playType;

/**
 * @brief 是否支持Monet后处理中模糊特效
 */
+ (BOOL)supportMonetBlurVideoOverlayFx;

/**
 * @brief 是否支持指定分辨率视频的TVM超分
 *
 * @param videoSize 视频分辨率
 * @return 支持指定分辨率TVM超分返回YES，否则返回NO
 */
+ (BOOL)isTVMSuperResolutionSupportedWithVideoSize:(CGSize)videoSize;

/**
 * @brief 是否支持指定alpha分隔模式的Alpha分隔视频
 *
 * @param alphaSeparateMode alpha分隔模式，请参考ITVKVideoFx.h#TVKAlphaSeparateMode
 * @return 支持指定alpha分隔模式返回YES，否则返回NO
 */
+ (BOOL)isAlphaSeparateModeSupported:(int)alphaSeparateMode;

#if TARGET_OS_OSX

/**
 * @brief 传入指定的清晰度和屏幕，判断当前设备的支持情况。
 *        返回值为TVKDefinitionSupportType的枚举值，详细查看TVKDefinitionSupportType定义。
 *        其中definitionModel为ITVKVodInfoGetterDelegate、ITVKLiveInfoGetterDelegate等
 *        接口回调的TVKVODPlayInfo、TVKLivePlayInfo中的清晰度信息描述。
 *
 * @param definitionModel 清晰度
 * @param screen 屏幕. 如果传nil，则不考虑屏幕，只查看设备是否支持该清晰度
 * @return TVKDefinitionSupportType类型的枚举值，详细查看TVKDefinitionSupportType定义
 */
+ (TVKDefinitionSupportType)definitionSupportTypeWithDefinitionModel:(TVKDefinitionModel *)definitionModel screen:(NSScreen *)screen;

/**
 * @brief 传入指定的清晰度和屏幕，判断当前设备的支持情况。
 *        返回值为TVKDefinitionSupportType的枚举值，详细查看TVKDefinitionSupportType定义。
 *        其中definitionInfo为TVKMediaPlaybackDelegate回调的TVKNetVideoInfo中的清晰度信息描述。
 *
 * @param definitionInfo 清晰度
 * @param screen 屏幕。如果传nil，则不考虑屏幕，只查看设备是否支持该清晰度
 * @return TVKDefinitionSupportType类型的枚举值，详细查看TVKDefinitionSupportType定义
 */
+ (TVKDefinitionSupportType)definitionSupportTypeWithDefinitionInfo:(TVKNetMediaDefinitionInfo *)definitionInfo screen:(NSScreen *)screen;

#endif

/// 查询设备是否支持播放某种点播视频格式
/// @param codecType 编码格式
/// @param resolutionLevel 分辨率级别
/// @param frameRate 帧率（片源帧率， 内部会考虑倍速播放）
/// @param hdrType hdr类型
+ (BOOL)isVodVideoSupported:(TVKVideoCodecType)codecType
            resolutionLevel:(TVKResolutionLevel)resolutionLevel
                  frameRate:(float)frameRate
                    hdrType:(TVKHdrType)hdrType;

/// 查询设备是否支持播放某种直播视频格式
/// @param codecType 编码格式
/// @param resolutionLevel 分辨率级别
/// @param frameRate 帧率（片源帧率， 内部会考虑倍速播放）
/// @param hdrType hdr类型
+ (BOOL)isLiveVideoSupported:(TVKVideoCodecType)codecType
             resolutionLevel:(TVKResolutionLevel)resolutionLevel
                   frameRate:(float)frameRate
                     hdrType:(TVKHdrType)hdrType;

/// 查询设备是否支持播放某种音频格式
+ (BOOL)supportAudioFormatType:(TVKAudioFormatType)audioFormatType;

/**
 *         查询能力相关参数（资源无关）
 *         spaudio              : 音频能力，bitset形式，1:支持纯音频，2:Dolby Surround 4:Dolby Atoms，
 *                                例：如果既支持纯音频，又支持Dolby Surround和Atoms，则取值为7
 *         spvideo              : 视频能力，bitset形式，4:支持HDR10，8:支持DolbyVision
 *         sphevcfps          : 视频hevc格式能力
 *         取值见文档https://tapd.woa.com/qqvideo_prj/markdown_wikis/show/#121011448100114648
 */
+ (NSDictionary *)vinfoCapabilityParams:(TVKAssetType)assetType;

@end
