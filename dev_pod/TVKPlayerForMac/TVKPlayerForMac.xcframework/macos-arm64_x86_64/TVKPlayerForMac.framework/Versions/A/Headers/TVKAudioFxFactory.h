//
//  TVKAudioFxFactory.h
//  TVKPlayer
//
//  Created by den<PERSON><PERSON><PERSON> on 2020/6/12.
//  Copyright © 2020 tencent. All rights reserved.
//

#ifndef TVKAudioFxFactory_h
#define TVKAudioFxFactory_h

#import "ITVKAudioFx.h"

/**
 * 创建音频特效
 */
@interface TVKAudioFxFactory : NSObject

/**
 * @param type 特效类型 @see {TVKAudioEffectTypeSurround, ITVKSurroundFx}
 *                   {TVKAudioEffectTypeClearVoice, ITVKClearVoiceFx}
 *                   {TVKAudioEffectTypeLiveConcert, ITVKLiveConcertFx}
 *                   {TVKAudioEffectTypePanoSurround, ITVKPanoSurroundFx}
 *                   {TVKAudioEffectTypeRetro, ITVKRetroFx}
 * @return 返回对应特效接口
 */
+ (id<ITVKAudioFx>)createAudioFx:(TVKAudioEffectType)type;

@end

#endif /* TVKAudioFxFactory_h */
