//
//  ITVKAudioFx.h
//  TVKPlayer
//
//  Created by den<PERSON><PERSON><PERSON> on 2020/6/11.
//  Copyright © 2020 tencent. All rights reserved.
//

#ifndef ITVKAudioFx_h
#define ITVKAudioFx_h

#import <Foundation/Foundation.h>

/// 音效类型
/// 注意！！！这些定的所有枚举均对外，千万不要改现有枚举对应的值，这会导致依赖老版本tvk的sdk和依赖新版本tvk的工程一起编译运行后走到错误的逻辑中。
/// 别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!
typedef NS_ENUM(NSUInteger, TVKAudioEffectType) {
    // 未知音效类型
    TVKAudioEffectTypeNone,
    // Surround音效
    TVKAudioEffectTypeSurround,
    // 全景环绕音效
    TVKAudioEffectTypePanoSurround,
    // 清澈人声
    TVKAudioEffectTypeClearVoice,
    // 演唱会现场
    TVKAudioEffectTypeLiveConcert,
    // 复古怀旧
    TVKAudioEffectTypeRetro
};

// 音效基类，定义了基础的音效信息
@protocol ITVKAudioFx <NSObject>

// 音效描述，子类负责写入描述信息，可用于日志，或者其他说明场景
@property (nonatomic, readonly) NSString* effectDescription;

@end

#endif /* ITVKAudioFx_h */
