/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TVKPlayerForMac.h
 * @brief    TVKPlayer组件的伞头文件
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/10/26
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

//TODO(harleyxwang, 2023.11.09): visionOS平台的公共头文件，后面framework改名时，使用TVKPlayer.h

//! Project version number for TVKPlayerForMac.
FOUNDATION_EXPORT double TVKPlayerForMacVersionNumber;

//! Project version string for TVKPlayerForMac.
FOUNDATION_EXPORT const unsigned char TVKPlayerForMacVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <TVKPlayerForMac/PublicHeader.h>

#import <TVKPlayerForMac/TVKDownloadProxyUrlBuilder.h>
#import <TVKPlayerForMac/TVKDownloadProxyConfig.h>
#import <TVKPlayerForMac/ITVKVideoFxProcessor.h>
#import <TVKPlayerForMac/TVKAudioFxProcessor.h>
#import <TVKPlayerForMac/ITVKAudioPostProcessorDelegate.h>
#import <TVKPlayerForMac/TVKAbstractProcessor.h>
#import <TVKPlayerForMac/ITVKVideoFx.h>
#import <TVKPlayerForMac/ITVKColorBlindnessFx.h>
#import <TVKPlayerForMac/ITVKSdrEnhanceFx.h>
#import <TVKPlayerForMac/ITVKSuperResolutionFx.h>
#import <TVKPlayerForMac/ITVKVRFx.h>
#import <TVKPlayerForMac/ITVKGaussionBlurVideoOverlayFx.h>
#import <TVKPlayerForMac/TVKVideoFxFactory.h>
#import <TVKPlayerForMac/ITVKAudioFx.h>
#import <TVKPlayerForMac/ITVKSurroundFx.h>
#import <TVKPlayerForMac/ITVKClearVoiceFx.h>
#import <TVKPlayerForMac/ITVKRetroFx.h>
#import <TVKPlayerForMac/ITVKPanoSurroundFx.h>
#import <TVKPlayerForMac/ITVKLiveConcertFx.h>
#import <TVKPlayerForMac/TVKAudioFxFactory.h>
#import <TVKPlayerForMac/ITVKRichMediaAsyncRequester.h>
#import <TVKPlayerForMac/ITVKRichMediaSynchronizer.h>
#import <TVKPlayerForMac/TVKRichMediaDefine.h>
#import <TVKPlayerForMac/TVKDebugConfig.h>
#import <TVKPlayerForMac/TVKDebugDefine.h>
#import <TVKPlayerForMac/TVKFactory.h>
#import <TVKPlayerForMac/ITVKDownloadAssetRequester.h>
#import <TVKPlayerForMac/ITVKMediaPlayerForSubtitle.h>
#import <TVKPlayerForMac/ITVKVideoView.h>
#import <TVKPlayerForMac/ITVKMediaPlayer.h>
#import <TVKPlayerForMac/ITVKQQLiveAssetRequester.h>
#import <TVKPlayerForMac/ITVKBeaconDataReporter.h>
#import <TVKPlayerForMac/TVKSDK.h>
#import <TVKPlayerForMac/TVKLogDelegate.h>
#import <TVKPlayerForMac/TVKLiveSeeBackBaseInfo.h>
#import <TVKPlayerForMac/TVKVODADInfo.h>
#import <TVKPlayerForMac/TVKVodAdDot.h>
#import <TVKPlayerForMac/TVKVODPlayInfo.h>
#import <TVKPlayerForMac/TVKVODExtraInfo.h>
#import <TVKPlayerForMac/TVKVODMasterDirectInfo.h>
#import <TVKPlayerForMac/TVKSection.h>
#import <TVKPlayerForMac/TVKRawWaterMarkInfo.h>
#import <TVKPlayerForMac/TVKCGIDefines.h>
#import <TVKPlayerForMac/TVKDefinitionModel.h>
#import <TVKPlayerForMac/TVKDrmModel.h>
#import <TVKPlayerForMac/TVKMediaPlayInfo.h>
#import <TVKPlayerForMac/TVKSubTitleModel.h>
#import <TVKPlayerForMac/TVKNetVideoInfo+TV.h>
#import <TVKPlayerForMac/TVKNetAdInfo.h>
#import <TVKPlayerForMac/TVKNetVideoInfo.h>
#import <TVKPlayerForMac/TVKPreloadManager.h>
#import <TVKPlayerForMac/TVKPreloadDefine.h>
#import <TVKPlayerForMac/ITVKAsset.h>
#import <TVKPlayerForMac/TVKAssetFactory.h>
#import <TVKPlayerForMac/TVKOnlineVodVidAsset.h>
#import <TVKPlayerForMac/TVKOnlineVodXmlAsset.h>
#import <TVKPlayerForMac/TVKOfflineVodVidAsset.h>
#import <TVKPlayerForMac/TVKLivePidAsset.h>
#import <TVKPlayerForMac/TVKLiveSidAsset.h>
#import <TVKPlayerForMac/TVKUrlAsset.h>
#import <TVKPlayerForMac/TVKMediaInfo.h>
#import <TVKPlayerForMac/TVKSeiInfo.h>
#import <TVKPlayerForMac/TVKVRControlDefine.h>
#import <TVKPlayerForMac/TVKUserInfo.h>
#import <TVKPlayerForMac/TVKMediaInfo+TV.h>
#import <TVKPlayerForMac/TVKMediaPlayer.h>
#import <TVKPlayerForMac/TVKMediaPlaybackDelegate.h>
#import <TVKPlayerForMac/TVKVideoView.h>
#import <TVKPlayerForMac/TVKPlayerDefine.h>
#import <TVKPlayerForMac/TVKPlayerCapbility.h>
#import <TVKPlayerForMac/TVKTrackInfo.h>
#import <TVKPlayerForMac/TVKVideoTrackInfo.h>
#import <TVKPlayerForMac/TVKPlayerEventParams.h>
#import <TVKPlayerForMac/TVKReportEvent.h>
#import <TVKPlayerForMac/TVKAudioFrameBuffer.h>
#import <TVKPlayerForMac/TVKClipInfo.h>
#import <TVKPlayerForMac/TVKMovieThumbInfo.h>
#import <TVKPlayerForMac/TVKFreeFlowInfo.h>
#import <TVKPlayerForMac/TVKAudioTrackModel.h>
#import <TVKPlayerForMac/TVKLivePlayInfo.h>
#import <TVKPlayerForMac/TVKVideoFrameBuffer.h>
#import <TVKPlayerForMac/TVKPixelFormat.h>
#import <TVKPlayerForMac/ITVKBatchVinfoRequester.h>
#import <TVKPlayerForMac/ITVKTVMSuperResolutionFx.h>
#import <TVKPlayerForMac/ITVKPowerConsumer.h>
#import <TVKPlayerForMac/ITVKPowerController.h>
#import <TVKPlayerForMac/TVKPowerMetrics.h>
#import <TVKPlayerForMac/ITVKPowerConsumerRegistrationDelegate.h>
#import <TVKPlayerForMac/TVKSubtitleRenderInfo.h>
#import <TVKPlayerForMac/ITVKPlayerSynchronizer.h>
#import <TVKPlayerForMac/TVKPlayerSynchronizerConfig.h>
#import <TVKPlayerForMac/ITVKDrawableContainer.h>
#import <TVKPlayerForMac/TVKPictureSRProcessResult.h>
#import <TVKPlayerForMac/ITVKPictureSRProcessor.h>
#import <TVKPlayerForMac/TVKPictureSRCapability.h>

#if TARGET_OS_VISION
#import <TVKPlayerForMac/ITVKXRKitBridge.h>
#endif
