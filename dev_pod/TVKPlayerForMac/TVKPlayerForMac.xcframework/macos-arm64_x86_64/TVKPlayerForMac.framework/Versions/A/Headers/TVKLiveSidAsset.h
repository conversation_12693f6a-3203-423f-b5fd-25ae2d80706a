/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TVKLiveSidAsset.h
 * @brief    直播流id资源类，用于腾讯视频直播
 * <AUTHOR>
 * @version  1.0.0
 * @date     2022/8/10
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKAssetBase.h"

NS_ASSUME_NONNULL_BEGIN

/// @brief 描述见文件头
@interface TVKLiveSidAsset : TVKAssetBase

/// 直播流id
@property (nonatomic, copy) NSString *sid;

/// 直播节目id
@property (nonatomic, copy) NSString *livePid;

/// 默认初始化方法不可用
- (instancetype)init NS_UNAVAILABLE;

/// 初始化方法
/// @param sid 直播流id
/// @param livePid 直播节目id
- (instancetype)initWithSid:(NSString *)sid livePid:(NSString *)livePid;

@end

NS_ASSUME_NONNULL_END
