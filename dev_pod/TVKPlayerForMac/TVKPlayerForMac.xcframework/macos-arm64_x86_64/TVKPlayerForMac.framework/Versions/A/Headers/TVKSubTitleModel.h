/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKSubTitleModel.h
 Author      : liyukuan
 Version     : 1.0
 Date        : 2017/11/21
 Description :
 History     : 2017/11/21 初始版本
 ***********************************************************/

#import <Foundation/Foundation.h>
#import <CoreGraphics/CGBase.h>

typedef NS_ENUM(NSUInteger, TVKSubtitleType) {
    // 未知
    TVKSubtitleTypeUnknown,
    // srt
    TVKSubtitleTypeSrt,
    // ass
    TVKSubtitleTypeAss,
    // vtt
    TVKSubtitleTypeVtt,
};

/// @brief 软字幕信息
@interface TVKSubTitleModel : NSObject
/// 字幕唯一标识符，透传给下载
@property (nonatomic, copy) NSString *keyId;
/// 软字幕id
@property (nonatomic, copy) NSString *formatId;
/// 软字幕名字. avs分离情况下的master m3u8中的字幕name对应的也是这个，可以用于匹配。
@property (nonatomic, copy) NSString *name;
/// 字幕编码id
@property (nonatomic, copy) NSString *langId;
/// 1：srt，2：ass，3：vtt
@property (nonatomic, assign) TVKSubtitleType type;
/// 软字幕文件url
@property (nonatomic, copy) NSString *url;
/// 用于离线下载情况，如果localPath不为空，则用localPath
@property (nonatomic, copy) NSString *localPath;
/// 软字幕语种
@property (nonatomic, copy) NSString *subtitleLanguage;
/// 表示硬字幕顶部距离视频画面顶部的高度百分比，取值[0,100]
@property (nonatomic, assign) CGFloat captionTopHPercent;
/// 表示硬字幕底部距离视频画面顶部的高度百分比，取值[0,100]
@property (nonatomic, assign) CGFloat captionBottomHPercent;
/// 后台返回本字幕是否是被优先选中
@property (nonatomic, assign) BOOL selected;
/// 是否离线
@property (nonatomic, assign) BOOL offline;
/// 是否是avs分离格式
@property (nonatomic, assign, getter=isAvsSeparate) BOOL avsSeparate;
/// 外挂字幕轨道的直出m3u8
@property (nonatomic, copy) NSString *m3u8;

/// 标识字幕特性
/// 0x01，标识是AI生成字幕
@property (nonatomic, assign) int feature;

/// 字幕的权益标识
@property (nonatomic, assign) int limit;

/// 当前用户是否有字幕播放付费权益，0-表示无需付费（可播）1-表示需要付费（不可播）
@property (nonatomic, assign) int charge;

/// 从后台返回的软字幕列表构建TVKSubTitleModel列表
/// @param srtArray 从json中解析出的软字幕列表
/// @return 一个TVKSubTitleModel的Array
+ (NSArray<TVKSubTitleModel *> *)subTitleModelArrayWithArray:(NSArray *)srtArray;
@end
