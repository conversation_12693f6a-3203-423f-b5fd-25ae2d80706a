/*****************************************************************************
 * @copyright Copyright (C), 1998-2021, Tencent Tech. Co., Ltd.
 * @file     ITVKRichMediaAsyncRequester.h
 * @brief    富媒体请求的接口定义.此接口主要是面向类似明星识别这样的功能， 不需要和播放器同步，而是直接通过此类的接口获取指定时间或者指
 *           定时间区间的富媒体数据。接口调用和ITVKRichMediaSynchronizer调用逻辑类似，需要设置资源地址后，发起prepareAsync后，
 *           等onRichMediaPrepared后，才可以调用requestFeatureDataAsyncWithIndex:timeMs:等接口
 * <AUTHOR>
 * @version  1.0.0
 * @date     2022/01/2
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#import "TVKRichMediaDefine.h"

@protocol ITVKRichMediaAsyncRequester;

/// @brief 富媒体请求的回调
@protocol ITVKRichMediaAsyncRequesterDelegate <NSObject>

/// 在调用PrepareAsync后，如果资源加载成功，则通过此接口进行通知
/// @param requester 富媒体requester请求实例
- (void)onRequesterPrepared:(id<ITVKRichMediaAsyncRequester>)requester;

/// 富媒体运行出错，无法再继续工作时，通过此接口回调消息通知
/// @param requester 富媒体requester请求实例
/// @param errorCode 错误码，用于调试和上报
- (void)onRequesterError:(id<ITVKRichMediaAsyncRequester>)requester errorCode:(int)errorCode;

/// 在调用requestFeatureDataAsyncWithIndex:timeMs:等接口请求富媒体功能数据后，通过此接口返回返回富媒体数据
/// @param requester 富媒体requester请求实例
/// @param requestId 请求id. 对应requestFeatureDataAsyncWithIndex:timeMs:等接口返回的请求id
/// @param featureIndex 富媒体功能下标
/// @param featureData 富媒体返回内容数据
- (void)onFeatureDataRequestSuccess:(id<ITVKRichMediaAsyncRequester>)requester
                          requestId:(int)requestId
                       featureIndex:(int)featureIndex
                        featureData:(TVKRichMediaFeatureData *)featureData;

/// 在调用requestFeatureDataAsyncWithIndex:timeMs:等接口请求富媒体功能数据后，如果请求失败，将通过此接口进行通知
/// @param requester 富媒体requester请求实例
/// @param requestId 请求id. 对应requestFeatureDataAsyncWithIndex:timeMs:等接口返回的请求id
/// @param featureIndex 富媒体功能下标
/// @param errorCode 错误码
- (void)onFeatureDataRequestFailure:(id<ITVKRichMediaAsyncRequester>)requester
                          requestId:(int)requestId
                       featureIndex:(int)featureIndex
                          errorCode:(int)errorCode;

@end


/// @brief 同头文件描述
@protocol ITVKRichMediaAsyncRequester <NSObject>

/// 用于监听富媒体请求的监听
@property (nonatomic, weak) id<ITVKRichMediaAsyncRequesterDelegate> delegate;

/// 设置富媒体资源（富媒体网关地址）
/// @param url 设置富媒体资源（富媒体网关地址）
/// @return 设置是否成功，见TVKRichMediaActResult定义
- (TVKRichMediaActResult)setRichMediaSource:(NSString *)url;

/// 设置富媒体资源地址后，通过此接口的调用prepare富媒体。
/// 需要等到ITVKRichMediaAsyncRequesterDelegate的onRequesterPrepared回调成功后才能features、
/// requestFeatureAsyncWithIndex等接口
/// @return 成功与否的错误码标识。错误的原因一般是状态错误，比如在此之前没有设置资源或者重复调用等等
- (TVKRichMediaActResult)prepareAsync;

/// 通过prepareAsync加载富媒体资源后，在ITVKRichMediaAsyncRequesterDelegate的onRequesterPrepared方法回调后，
/// 访问此接口，获取加载数据后，富媒体资源中的功能
/// @return 富媒体资源中的功能列表
- (NSArray<TVKRichMediaFeature *> *)features;

/// 请求指定富媒体功能、指定时间点的内容. 内容将通过
/// ITVKRichMediaAsyncRequesterDelegate的onFeatureDataRequestSuccess 或者
/// ITVKRichMediaAsyncRequesterDelegate的onFeatureDataRequestFailure 进行成果和失败的通知
/// @param featureIndex 富媒体功能索引，来自于 的features返回功能列表的下标索引
/// @param timeMs 指定时间。单位毫秒
/// @return 请求id。 用于唯一标识此次请求，如果请求失败，将会返回负值
- (int)requestFeatureDataAsyncWithIndex:(int)featureIndex timeMs:(int64_t)timeMs;

/// 请求指定富媒体功能、多个时间点的内容. 内容将通过
/// ITVKRichMediaAsyncRequesterDelegate的onFeatureDataRequestSuccess 或者
/// ITVKRichMediaAsyncRequesterDelegate的onFeatureDataRequestFailure 进行成果和失败的通知
/// @param featureIndex 富媒体功能索引，来自于 的features返回功能列表的下标索引
/// @param timeMsArray 指定时间数组。单位毫秒，-1表示到文件尾
/// @return 请求id。 用于唯一标识此次请求，如果请求失败，将会返回负值
- (int)requestFeatureDataAsyncWithIndex:(int)featureIndex timeMsArray:(NSArray<NSNumber *> *)timeMsArray;

/// 请求指定富媒体功能、时间区间的内容. 内容将通过
/// ITVKRichMediaAsyncRequesterDelegate的onFeatureDataRequestSuccess 或者
/// ITVKRichMediaAsyncRequesterDelegate的onFeatureDataRequestFailure 进行成果和失败的通知
/// @param featureIndex 富媒体功能索引，来自于 的features返回功能列表的下标索引
/// @param timeRange 指定时间区间，-1表示到文件尾
/// @return 请求id。 用于唯一标识此次请求，如果请求失败，将会返回负值
- (int)requestFeatureDataAsyncWithIndex:(int)featureIndex timeRange:(TVKRichMediaTimeRange *)timeRange;

/// 请求指定富媒体功能、多个时间区间的内容. 内容将通过
/// ITVKRichMediaAsyncRequesterDelegate的onFeatureDataRequestSuccess 或者
/// ITVKRichMediaAsyncRequesterDelegate的onFeatureDataRequestFailure 进行成果和失败的通知
/// @param featureIndex 富媒体功能索引，来自于 的features返回功能列表的下标索引
/// @param timeRangeArray 指定时间区间数组
/// @return 请求id。 用于唯一标识此次请求，如果请求失败，将会返回负值
- (int)requestFeatureDataAsyncWithIndex:(int)featureIndex timeRangeArray:(NSArray<TVKRichMediaTimeRange *> *)timeRangeArray;

/// 取消指定的请求id的请求
/// @param requestId 请求id
- (void)cancelRequestWithRequestId:(int)requestId;

/// 清理并释放资源。注意调用后，此实例将不能再继续使用
- (void)releaseRequester;

@end
