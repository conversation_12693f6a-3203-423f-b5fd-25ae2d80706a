/************************************************************
 Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 FileName    : TVKAudioEffectModel.h
 Author      : eriktang
 Version     : 1.0
 Date        : 2023/05/24 
 Description : 音效model
 History     : 2023/05/24 初始版本
 ***********************************************************/

#import <Foundation/Foundation.h>

/// TVKAudioEffectModel.effectName : 清澈人声音效名称
static NSString *const kTVKAudioEffectNameClearVoice = @"clear_voice";
/// TVKAudioEffectModel.effectName : 演唱会现场音效名称
static NSString *const kTVKAudioEffectNameLiveConcert = @"live_concert";
/// TVKAudioEffectModel.effectName : 全景环绕音效名称
static NSString *const kTVKAudioEffectNamePanoSurround = @"pano_surround";
/// TVKAudioEffectModel.effectName : 全景环绕音效名称
static NSString *const kTVKAudioEffectNameSurround = @"surround";
/// TVKAudioEffectModel.effectName : 复古怀旧音效名称
static NSString *const kTVKAudioEffectNameRetro = @"retro";

/// @brief 音效详细信息
@interface TVKAudioEffectModel : NSObject

/// 音效名称
@property (nonatomic, copy) NSString *effectName;

/// 音效展示文案
@property (nonatomic, copy) NSString *effectShowName;

/// 音效滤波参数
@property (nonatomic, copy) NSString *effectFilterParam;

/// 音效是否需要会员
/// | limit | 说明 |
/// | 0     | 可免费播放 |
/// | 1     | 会员专享 |
@property (nonatomic, assign) int vipLimit;

@end
