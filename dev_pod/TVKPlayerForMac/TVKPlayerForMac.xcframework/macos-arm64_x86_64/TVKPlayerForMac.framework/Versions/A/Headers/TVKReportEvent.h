/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKReportEvent.h
 * @brief    播放器上报事件定义，主要用于对外透传播放器内部一些步骤的耗时计算结果，外部用于上报
 * <AUTHOR> chen
 * @version  1.0.0
 * @date     2021/6/13
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#pragma mark - 事件参数定义
/**
 * TVKReportEventAdCGIed参数
 */
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyHasAd; // 是否有广告
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyAdCgiDurationMs; // CGI请求总时间，从Open到广告CGIED

/**
 * TVKReportEventVideoCGIed参数
 */
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyVideoCgiDurationMs; // 正片CGI请求总时间，从Open到正片CGIED
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyIsAdCGIed; // 表示收到正片CGIED时，广告CGI是否已完成
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyTotalRequestDurationMs; // 整个CGI请求所总时间
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyRequestDurationMs; // 发起请求getvinfo请求总时间
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyIsGgiCached; // 是否已经有缓存的vinfo的xml文件
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyReadCgiCacheDurationMs; // 缓存文件读取耗时
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyParseCgiTimeMs; // xml文件解析耗时
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeySaveCgiTimeMs; // xml存储耗时

/**
 * TVKReportEventPreAdPreparedError参数
 */
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyOpenToAdPrepareErrorDurationMs; // 从open到prepared错误耗时
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyAdPrepareErrorDurationMs; // 广告CGIED到广告Prepared错误耗时

/**
 * TVKReportEventPrepared参数
 */
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyMediaType; // 当前媒体类型
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyOpenToPreparedDurationMs; // 从open到prepared耗时
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyPreparedDurationMs; // 从CGIED到Prepared耗时
/**
 * TVKReportEventPlaying参数
 */
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyOpenToStartDurationMs; // 从open到App调用start方法的耗时
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyStartDurationMs; // 从Prepared到App调用start方法耗时
/**
 * TVKReportEventRealPlaying参数
 */
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyOpenToRealPlayDurationMs; // 从Open到收到内核EVENT_RUNING事件耗时
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyRealPlayDurationMs; // 从App调用Start到收到RealPlay事件耗时
/**
 * TVKReportEventTransition参数
 */
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyAdCompleteToVideoRealPlayDurationMs; // 从广告播放结束到正片收到RealPlay事件耗时

/// TVKReportEventPreAdRequestBegin参数
/// open到requestBegin时间
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyOpenToAdRequestBeginDurationMs;

/// TVKReportEventVinfoResponseToAdStart和TVKReportEventVinfoResponseToAdEnd参数
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyVinfoSourceDesc;
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyVinfoResponseToAdHasAdPass;
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyVinfoResponseToAdHasAdSeessionId;
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyVinfoResponseToAdHasHlsAdList;

FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyAdType;
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyAdState;
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyPlayerInterruptReason;
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyPlayerStartCallType;
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyIsPIP;
FOUNDATION_EXPORT NSString *const kTVKReportEventParamKeyIsFirstStart;

typedef NS_ENUM(NSUInteger, TVKReportEventMediaType) {
    TVKReportEventMediaTypeAd = 0,  // 广告
    TVKReportEventMediaTypeVideo = 1, // 正片
};

typedef NS_ENUM(NSUInteger, TVKReportEventBoolType) {
    TVKReportEventBoolTypeFalse = 0,
    TVKReportEventBoolTypeTrue = 1,
};

typedef NS_ENUM(NSUInteger, TVKReportEventAdType) {
    TVKReportEventAdTypeUnknown = 0,
    TVKReportEventAdTypePreAd = 1,
    TVKReportEventAdTypePauseAd = 2,
    TVKReportEventAdTypeMidAd = 3,
    TVKReportEventAdTypePostAd = 4,
};

typedef NS_ENUM(NSUInteger, TVKReportEventAdState) {
    TVKReportEventAdStateUnknown = 0,
    TVKReportEventAdStateCgiing = 1,
    TVKReportEventAdStateReceived = 2,
    TVKReportEventAdStatePreparing = 3,
    TVKReportEventAdStatePrepared = 4,
    TVKReportEventAdStateStart = 5,
    TVKReportEventAdStatePause = 6,
    TVKReportEventAdStateResume = 7,
    TVKReportEventAdStateFinish = 8,
    TVKReportEventAdStateStop = 9,
};

#pragma mark - 事件定义
/**
 事件定义
 */
typedef NS_ENUM(NSUInteger, TVKReportEvent) {
    /**
     * 未知事件
     */
    TVKReportEventUnknown,
    
    /// 正片openMediaPlayer事件
    TVKReportEventVideoOpenMediaPlayer,
    
    /// 正片传递open事件到广告 (播放器拉起广告open之前）
    TVKReportEventPlayerOpenAd,
    
    /// 开始加载前贴广告 (前贴广告收到正片open的时候)
    TVKReportEventPreAdOpen,
    
    /// 前贴片广告开始请求cgi
    /// 参数：@{kTVKReportEventParamKeyOpenToAdRequestBeginDurationMs: NSNumber *}
    TVKReportEventPreAdRequestBegin,
    
    /// 开始发前贴广告cgi网络请求
    TVKReportEventPreAdSendCGIRequest,
    
    /// 前贴广告开始调用广告播放器
    TVKReportEventPreAdCallPlayerOpen,
    
    /**
     * 广告CGI完成事件
     * 参数：@{kTVKReportEventParamKeyHasAd: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyAdCgiDurationMs: NSNumber *}
     */
    TVKReportEventPreAdCGIed,
    
    /// 正片CGI发起HTTP请求事件
    TVKReportEventCGISendHttpRequest,
    
    /// 正片CGI收到 HTTP response事件
    TVKReportEventCGIHttpResponseReceived,
    
    /// 正片CGIXML数据解析完成事件
    TVKReportEventCGIParseDataDone,
    
    /// 正片CGIXML数据回抛事件
    TVKReportEventCGIParseDataResponse,
    
    /// 正片CGI线程回调播放线程事件
    TVKReportEventCGIThreadToPlayerThread,
    
    /**
     * 正片CGI完成事件
     * 参数：@{kTVKReportEventParamKeyVideoCgiDurationMs: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyIsAdCGIed: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyTotalRequestDurationMs: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyRequestDurationMs: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyIsGgiCached: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyReadCgiCacheDurationMs: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyParseCgiTimeMs: NSNumber *}
     * 参数：@{kTVKReportEventParamKeySaveCgiTimeMs: NSNumber *}
     */
    TVKReportEventVideoCGIed,
    /**
     * 广告Prepare错误事件
     * 参数：@{kTVKReportEventParamKeyHasAd: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyOpenToAdPrepareErrorDurationMs: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyAdPrepareErrorDurationMs: NSNumber *}
     */
    TVKReportEventPreAdPreparedError,
    /**
     * Prepared事件
     * 参数：@{kTVKReportEventParamKeyHasAd: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyMediaType: @(TVKReportEventMediaType)}
     * 参数：@{kTVKReportEventParamKeyOpenToPreparedDurationMs: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyPreparedDurationMs: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyAdPrepareErrorDurationMs: NSNumber *}
     */
    TVKReportEventPrepared,
    
    /**
     * Playing事件
     * 参数：@{kTVKReportEventParamKeyHasAd: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyMediaType: @(TVKReportEventMediaType)}
     * 参数：@{kTVKReportEventParamKeyOpenToStartDurationMs: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyStartDurationMs: NSNumber *}
     */
    TVKReportEventPlaying,
    
    /**
     * 真正起播事件
     * 参数：@{kTVKReportEventParamKeyHasAd: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyMediaType: @(TVKReportEventMediaType)}
     * 参数：@{kTVKReportEventParamKeyOpenToRealPlayDurationMs: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyRealPlayDurationMs: NSNumber *}
     */
    TVKReportEventRealPlaying,
    
    /**
     * 广告结束转场到正片播放事件
     * 参数：@{kTVKReportEventParamKeyHasAd: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyAdCompleteToVideoRealPlayDurationMs: NSNumber *}
     */
    TVKReportEventTransition,
    
    /**
     * 正片首帧事件
     * 参数：@{kTVKReportEventParamKeyHasAd: NSNumber *}
     * 参数：@{kTVKReportEventParamKeyMediaType: @(TVKReportEventMediaType)}
     */
    TVKReportEventFirstVideoFrameRendered,
    
    /*** **************下面是为了对数据新增的事件，用完删*********************/
    TVKReportEventVinfoRequestStartToAdStart,
    TVKReportEventVinfoRequestStartToAdEnd,
    
    /**
     * 参数：@{kTVKReportEventParamKeyVinfoSourceDesc: NSStirng *}
     */
    TVKReportEventVinfoStart,
    /**
     * 参数：@{kTVKReportEventParamKeyVinfoSourceDesc: NSStirng *}
     * 参数：@{kTVKReportEventParamKeyVinfoResponseToAdHasAdPass: NSStirng *}
     * 参数：@{kTVKReportEventParamKeyVinfoResponseToAdHasAdSeessionId: NSStirng *}
     * 参数：@{kTVKReportEventParamKeyVinfoResponseToAdHasHlsAdList: NSStirng *}
     */
    TVKReportEventVinfoResponseToAdStart,
    /**
     * 参数：@{kTVKReportEventParamKeyVinfoSourceDesc: NSStirng *}
     * 参数：@{kTVKReportEventParamKeyVinfoResponseToAdHasAdPass: NSStirng *}
     * 参数：@{kTVKReportEventParamKeyVinfoResponseToAdHasAdSeessionId: NSStirng *}
     * 参数：@{kTVKReportEventParamKeyVinfoResponseToAdHasHlsAdList: NSStirng *}
     */
    TVKReportEventVinfoResponseToAdEnd,
    
    TVKReportEventStartPlayerBegin,
    TVKReportEventStartPlayerToAdStart,
    TVKReportEventStartPlayerToAdEnd,
    TVKReportEventPausePlayerBegin,
    TVKReportEventPauseWithAdBegin,
    TVKReportEventPauseWithAdEnd,
    TVKReportEventPauseAdStart,
    TVKReportEventPausePlayerStart,
    TVKReportEventCompleteToAdStart,
    TVKReportEventCompleteToAdEnd,
    
    /**
     * 参数：@{kTVKReportEventParamKeyPlayerInterruptReason: NSStirng *}
     * 参数：@{kTVKReportEventParamKeyAdType: NSNumber *, 参考TVKReportEventAdType}
     * 参数：@{kTVKReportEventParamKeyAdState: NSNumber *, 参考TVKReportEventAdState}
     */
    TVKReportEventPlayerInterrupt,
    /**
     * 参数：@{kTVKReportEventParamKeyPlayerStartCallType: NSNumber}
     * 参数：@{kTVKReportEventParamKeyIsPIP: NSNumber}
     * 参数：@{kTVKReportEventParamKeyAdType: NSNumber *, 参考TVKReportEventAdType}
     * 参数：@{kTVKReportEventParamKeyAdState: NSNumber *, 参考TVKReportEventAdState}
     */
    TVKReportEventPlayerStartCall,
    /**
     * 参数    @{kTVKReportEventParamKeyIsFirstStart: NSNumber}
     * 参数    @{kTVKReportEventParamKeyIsPIP: NSNumber}
     * 参数：@{kTVKReportEventParamKeyAdType: NSNumber *, 参考TVKReportEventAdType}
     * 参数：@{kTVKReportEventParamKeyAdState: NSNumber *, 参考TVKReportEventAdState}
     */
    TVKReportEventPlayerStartNotifyAd,
    
    /**
     * 参数：@{kTVKReportEventParamKeyAdType: NSNumber *, 参考TVKReportEventAdType}
     */
    TVKReportEventAdPreparing,
    /**
     * 参数：@{kTVKReportEventParamKeyAdType: NSNumber *, 参考TVKReportEventAdType}
     */
    TVKReportEventAdPrepared,
    
    /**
     * 参数：@{kTVKReportEventParamKeyAdType: NSNumber *, 参考TVKReportEventAdType}
     * 参数：@{kTVKReportEventParamKeyAdState: NSNumber *, 参考TVKReportEventAdState}
     */
    TVKReportEventAdStartCall,
    
    /**
     * 广告CGIing事件
     */
    TVKReportEventPreAdCgiing,
};



/**
 播放上报事件对象
 */
@interface TVKReportEventParams : NSObject
/**
 当前播放flowId
 */
@property (nonatomic, copy, readonly, nullable) NSString *flowId;
/**
 信息采集时的系统实时时间，随系统实时时间改变而改变，即从UTC1970-1-1 0:0:0开始计时，单位毫秒
 */
@property (nonatomic, assign) NSTimeInterval timeSince1970Ms;
/**
 事件携带的上报参数。app用于透传上报该reportMap
 */
@property (nonatomic, copy, nullable) NSDictionary *reportMap;
/**
 初始化方法
 */
- (instancetype)initWithFlowId:(nullable NSString *)flowId;

@end

NS_ASSUME_NONNULL_END

