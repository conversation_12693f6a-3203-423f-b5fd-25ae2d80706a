/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKMediaPlayInfo.h
 * @brief    cgi解析出的播放所需信息，基类
 * <AUTHOR>
 * @version  1.0.0
 * @date     2019/9/12
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import <CoreGraphics/CGBase.h>

#import "TVKMediaInfo.h"
#import "TVKCGIDefines.h"
#import "TVKDefinitionModel.h"
#import "TVKRawWaterMarkInfo.h"
#import "TVKSubTitleModel.h"
#import "TVKDrmModel.h"
#import "TVKMovieThumbInfo.h"
#import "TVKSection.h"
#import "TVKFreeFlowInfo.h"

/// cgi解析出的播放所需信息
@interface TVKMediaPlayInfo : NSObject

/// getvinfo返回的原始数据
@property (nonatomic, copy) NSString *vinfoDataString;

/// 点播时为video id，直播时为流id
@property (atomic, copy) NSString *vid;

/// 专辑id.点播时为视频专辑id(cid),直播时为pid
@property (atomic, copy) NSString *coverID;

/// url地址列表
@property (atomic, strong) NSArray<TVKSection *> *sectionArray;

/// 是否试看
@property (nonatomic, assign, readonly) BOOL isPreview;

/// 客户端是否开启p2p，0不开启，大于0开启
@property (nonatomic, assign) int fp2p;

/// 清晰度列表
@property (atomic, copy) NSArray<TVKDefinitionModel *> *defnModelList;

/// 当前使用的视频清晰度
@property (nonatomic, strong) TVKDefinitionModel *currentDefinition;

/// 切换前的清晰度，如果未发生切换则为空
@property (nonatomic, strong) TVKDefinitionModel *preDefinition;

/// 水印信息
@property (nonatomic, strong) TVKWaterMarkModel *waterMarkModel;

/// drm加密的信息
@property (nonatomic, strong) TVKDrmModel *drmModel;

/// 是否为HEVC
@property (nonatomic, assign) BOOL isHevc;

/// 是否为VVC
@property (nonatomic, assign) BOOL isVvc;

/// vr渲染类型，0:无vr，1:普通vr，2:360全景
@property (nonatomic, assign) NSInteger vr;

/// medialabvr是否启用, 0:不启用，1：启用
@property (nonatomic, assign) NSInteger medialabVR;

/// 业务透传字段，用于后台与app交互，播放器不理解
@property (nonatomic, copy) NSString *bizExt;

/// getVInfo返回的视频宽高(目前仅点播cgi返回该字段，考虑后续在直播扩展性，定义在基类里)
@property (nonatomic, assign) CGSize videoSize;

/// 外部传入的mediaInfo TODO:能不能不放这里, hemanli
@property (nonatomic, strong) TVKMediaInfo *mediaInfo;

/// cgi返回的媒体格式
@property (nonatomic, assign) TVKMediaFormat mediaType;

/// cgi返回的富媒体url
@property (nonatomic, copy) NSString *richMediaUrl;

/// 当前播放类型，点播或者直播
@property (nonatomic, assign, readonly) TVKMediaPlayBizType bizType;

/// 本次播放的flowId
@property (nonatomic, copy) NSString *flowId;

/// getVinfo结果来源类型
@property (nonatomic, assign) TVKMediaPlayInfoFromType fromType;

/// report字段，用于后台透传上报，客户端不理解具体内容
@property (nonatomic, copy) NSString* report;

/// 免流信息
@property (nonatomic, strong) TVKFreeFlowInfo *freeInfo;

/// 截屏/录屏方式 0: 使用 app 逻辑判断
/// 1: 不可截屏/录屏
/// 2: 系统不可但 app 可截屏/录屏
/// 3: 系统和 app 均可截屏/录屏
@property (nonatomic, assign) TVKCGISShot sshot;

/// 截图方式 0: 播放器截图，1: 后台截图
@property (nonatomic, assign) NSInteger mshot;

/// 后台返回的AB实验信息
/// key: 实验名称
/// value: 实验ID
@property (nonatomic, strong) NSDictionary<NSString *, NSNumber *> *abTestInfo;

/// 透传后台反作弊签名应用于app上报, 避免外部厂商恶意刷榜导致热度值外显异常
@property (nonatomic, copy) NSString* signature;

/// 请求CDN附加包头字段，由后台返回
@property (nonatomic, copy) NSDictionary *cdnHttpHeaders;

/// 源站标识 0:自建cdn 1:腾讯云。透传给下载，播放器本身不理解
@property (nonatomic, assign) NSInteger cdnOrigin;

/// 视频标题，透传给app
@property (nonatomic, copy) NSString *title;

/// 安全相关，下载组件上报ts序号列表, 透传给下载，播放器无需理解
@property (nonatomic, copy) NSString *seReport;

/// 安全相关，cdn交互协议需要的加密后的私有参数, 透传给下载，播放器无需理解
@property (nonatomic, copy) NSString *sescy;

///  chacha20 新方案密钥加密串，透传给下载，播放器无需理解
@property (nonatomic, copy) NSString *sse;
@end

