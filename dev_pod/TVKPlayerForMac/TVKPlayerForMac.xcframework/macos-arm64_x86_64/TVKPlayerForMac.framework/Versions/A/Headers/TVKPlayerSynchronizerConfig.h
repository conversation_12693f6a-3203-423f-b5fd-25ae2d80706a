/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TVKPlayerSynchronizerConfig.h
 * @brief    同步源的配置
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/7/07
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKPlayerSyncStrategy.h"

/// @brief 同步源的配置
@interface TVKPlayerSynchronizerConfig : NSObject
/// 同步时间偏移量，单位ms 反映是源的时间戳偏移
/// 比如：播放器是60分钟的视频源，同步源播放器是20分钟视频源，但是内容基于主视频的第10分钟开始，那么时间偏移就是10分钟
/// 默认是0
@property (nonatomic, assign) int64_t offsetMs;

/// 同步策略
/// 默认为TVKPlayerSyncStrategy, 如果需要自定义策略，需要继承TVKPlayerSyncStrategy，然后设置strategy
@property (nonatomic, strong) TVKPlayerSyncStrategy *syncStrategy;

/// 同步进度检查时间间隔, 默认1000ms
@property (nonatomic, assign) int64_t intervalMs;

@end

