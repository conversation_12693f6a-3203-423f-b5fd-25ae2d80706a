/*****************************************************************************
 * @copyright Copyright (C), 1998-2019, Tencent Tech. Co., Ltd.
 * @file     TVKRichMediaDefine.h
 * @brief    富媒体相关定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2020/4/10
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

@class TPRichMediaFeature;
@class TVKPlayerQueue;

/**
 @brief 富媒体类型 - app依赖，下版本删除
 */
typedef NSString *TVKRichMediaType NS_STRING_ENUM;
FOUNDATION_EXPORT TVKRichMediaType const TVKRichMediaTypeUnknown;
FOUNDATION_EXPORT TVKRichMediaType const TVKRichMediaTypeObjectRecognition;
FOUNDATION_EXPORT TVKRichMediaType const TVKRichMediaTypeSmartSpeed;

/**
 @brief 调用富媒体接口返回的结果
 TVKRichMediaActResultOK : 调用OK
 TVKRichMediaActResultIllegalState: 调用状态不正确，当前调用不生效
 TVKRichMediaActResultIllegalArgument: 调用参数错误，当前调用不生效
 TVKRichMediaActResultDataSourceInvalid: 富媒体地址信息错误，或者当前视频没有富媒体相关信息
 */
typedef NS_ENUM(NSUInteger, TVKRichMediaActResult) {
    TVKRichMediaActResultOK = 0,
    TVKRichMediaActResultIllegalState,
    TVKRichMediaActResultIllegalArgument,
    TVKRichMediaActResultDataSourceInvalid
};

/**
 @brief 富媒体OnInfo信息定义，暂空
 */
typedef NS_ENUM(NSUInteger, TVKRichMediaInfoType) {
    TVKRichMediaInfoTypeUnKnown = 0,
};

/**
 @brief 富媒体的详细内容定义
 */
@interface TVKRichMediaFeatureContent : NSObject

/**
 * 富媒体内容的开始时间，单位为毫秒。
 */
@property (nonatomic, assign) int64_t startTimeMs;

/**
 * 富媒体内容的结束时间，单位为毫秒。为负值表示富媒体资源持续到到音视频资源结尾。
 */
@property (nonatomic, assign) int64_t endTimeMs;

/**
 * 富媒体的详细、具体内容，参考协议https://iwiki.woa.com/pages/viewpage.action?pageId=327232578中detail
 * 的result字段
 */
@property (nonatomic, copy) NSString *content;

@end

/// @brief TVKRichMediaFeature 数据
@interface TVKRichMediaFeatureData : NSObject

/**
 *富媒体类型（功能），对应富媒体协议中的https://iwiki.woa.com/pages/viewpage.action?pageId=327232578
 *type字段
 */
@property (nonatomic, copy) NSString *featureType;

/**
 * 富媒体的算法全局信息。参考协议中的https://iwiki.woa.com/pages/viewpage.action?pageId=327232578中的
 * env字段
 */
@property (nonatomic, copy) NSString *featureEnv;

/**
 * 富媒体协议版本号。参考协议中的version字段
 */
@property (nonatomic, copy) NSString *featureVersion;

/**
 * 富媒体内容列表（某些功能，同一时间，后台可能会有多个内容）
 */
@property (nonatomic, copy) NSArray<TVKRichMediaFeatureContent *> *featureContents;

@end

/**
 富媒体功能的描述
 */
@interface TVKRichMediaFeature : NSObject

/**
 富媒体功能类型
 */
@property (nonatomic, copy) NSString *featureType;

/**
 富媒体功能是否选中
 */
@property (nonatomic, assign, readonly) BOOL isSelected;

/**
 富媒体功能是否内部功能
 */
@property (nonatomic, assign, readonly) BOOL isInternal;

/**
 构造方法，通过Thumbplayer数据结构直接转换
 */
- (instancetype)initWithTPRichMediaFeature:(TPRichMediaFeature *)feature;

@end

/// @brief 富媒体onInfo消息携带的数据结构
@interface TVKRichMediaInfoData : NSObject
/// 参数 param1
@property (nonatomic, assign) int64_t param1;
/// 参数 param2
@property (nonatomic, assign) int64_t param2;
/// 参数params3
@property (nonatomic, assign) int64_t param3;
/// 通用结构
@property (nonatomic, strong) id object;
@end


/// @brief 富媒体时间区间定义
@interface TVKRichMediaTimeRange : NSObject
/// 开始时间，单位毫秒
@property (nonatomic, assign) int64_t startTimeMs;
/// 结束时间，单位毫秒
@property (nonatomic, assign) int64_t endTimeMs;
@end
