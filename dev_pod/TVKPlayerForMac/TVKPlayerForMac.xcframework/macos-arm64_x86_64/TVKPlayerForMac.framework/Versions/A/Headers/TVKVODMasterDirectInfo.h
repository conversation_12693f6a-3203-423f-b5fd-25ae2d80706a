/*****************************************************************************
 * @copyright Copyright (C), 1998-2021, Tencent Tech. Co., Ltd.
 * @file     TVKVODMasterDirectInfo.h
 * @brief    点播master直出相关信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/12/27
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class TVKVODM3u8DirectInfo;

/// @brief 详细说明见头文件中的描述
@interface TVKVODMasterDirectInfo : NSObject

/// master m3u8一级直出内容
@property (nonatomic, strong) TVKVODM3u8DirectInfo *masterM3u8DirectInfo;

/// master m3u8中的二级音频m3u8直出内容列表
@property (nonatomic, copy) NSArray<TVKVODM3u8DirectInfo *> *audioM3u8DirectInfos;

/// master m3u8中的二级视频m3u8直出内容列表
@property (nonatomic, copy) NSArray<TVKVODM3u8DirectInfo *> *videoM3u8DirectInfos;

/// master m3u8中的二级字幕m3u8直出内容列表
@property (nonatomic, copy) NSArray<TVKVODM3u8DirectInfo *> *subtitleM3u8DirectInfos;

@end

/// @brief 点播m3u8直出信息
@interface TVKVODM3u8DirectInfo : NSObject
/// m3u8地址
@property (nonatomic, copy) NSString *m3u8Url;
/// 直出m3u8内容
@property (nonatomic, copy) NSString *m3u8Content;
/// master的keyId
@property (nonatomic, copy) NSString *keyId;

@end

NS_ASSUME_NONNULL_END