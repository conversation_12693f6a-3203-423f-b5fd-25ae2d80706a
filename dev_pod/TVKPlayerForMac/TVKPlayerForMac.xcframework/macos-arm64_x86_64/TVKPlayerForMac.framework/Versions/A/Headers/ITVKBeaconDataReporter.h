/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     ITVKBeaconDataReporter.h
 * @brief    灯塔上报注入协议
 * <AUTHOR>
 * @version  1.0.0
 * @date     2022/11/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 灯塔上报注入协议
@protocol ITVKBeaconDataReporter <NSObject>

/// 上报方法
/// @param appKey   上报关联的appKey
/// @param eventId   上报事件id
/// @param kvs   上报数据
- (void)trackCustomKVEvent:(NSString *)appKey eventId:(NSString *)eventId props:(NSDictionary *)kvs;

@end
