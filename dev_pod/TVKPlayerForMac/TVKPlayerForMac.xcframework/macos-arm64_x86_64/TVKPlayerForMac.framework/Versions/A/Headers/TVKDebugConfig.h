/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKDebugConfig.h
 Author      : liyukuan
 Version     : 1.0
 Date        : 2017/7/31
 Description :
 History     : 2017/7/31 初始版本
 ***********************************************************/

#import <Foundation/Foundation.h>
#import "TVKPlayerDefine.h"
#import "TVKDebugDefine.h"

// 主要用于调试和测试阶段，外面控制播放器内部的配置
@interface TVKDebugConfig : NSObject

/**
 * 设置播放器类型配置
 */
+ (void)setPlayerTypeConfig:(TVKPlayerTypeConfig)config;

/**
 * 播放器类型配置
 */
+ (TVKPlayerTypeConfig)playerTypeConfig;

/**
 * 设置视频的类型配置
 */
+ (void)setMediaFormatConfig:(TVKMediaFormatConfig)config;

/**
 * 视频的类型配置
 */
+ (TVKMediaFormatConfig)mediaFormatConfig;

/**
 * 设置播放器的类型配置
 */
+ (void)setPlayModeConfig:(TVKPlayModeConfig)config;

/**
 * 播放器的类型配置
 */
+ (TVKPlayModeConfig)playModeConfig;

/**
 * 设置自研播放器的解码类型配置
 */
+ (void)setSelfPlayerDecodeModeConfig:(TVKSelfPlayerDecodeModeConfig)config;

/**
 * 自研播放器的解码类型配置
 */
+ (TVKSelfPlayerDecodeModeConfig)selfPlayerDecodeModeConfig;

/**
 * 设置渲染方式配置
 */
+ (void)setRenderTypeConfig:(TVKRenderTypeConfig)config;

/**
 * 渲染方式配置
 */
+ (TVKRenderTypeConfig)renderTypeConfig;

/**
 * 设置getvinfo环境配置
 */
+ (void)setGetVInfoEnvConfig:(TVKGetVInfoEnvConfig)config;

/**
 * getvinfo环境配置
 */
+ (TVKGetVInfoEnvConfig)getVInfoEnvConfig;

/**
 * 设置播放器缓冲size, 小于等于0 表示使用默认值
 */
+ (void)setPlayerBufferSize:(NSInteger)bufferSize;

/**
 * 播放器缓冲size, 小于等于0 表示使用默认值
 */
+ (NSInteger)playerBufferSize;

/**
 * 设置超分开关配置
 */
+ (void)setSuperResolutionConfig:(TVKSuperResolutionConfig)config;

/**
 * 超分开关配置
 */
+ (TVKSuperResolutionConfig)superResolutionConfig;

/**
 * 起播优化临时开关，提高cgi线程优先级
 */
+ (void)setEnableIncreaseCGIThreadPriority:(BOOL)enable;

/**
 * 恢复指定key值的默认值
 */
+ (void)restoreDefaultForKey:(NSString *)key;

/**
 * 设置自定义开关的key和value
 */
+ (void)setValue:(NSString *)value customKey:(NSString *)key;

/**
 * 自定义开关key对应的value值
 */
+ (NSString *)valueForCustomKey:(NSString *)key;

/**
 * 自定义开关key的列表
 */
+ (NSArray<NSString *> *)customKeyList;
@end
