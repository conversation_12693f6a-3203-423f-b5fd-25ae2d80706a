/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKMediaInfo.h
 Author      : liyukuan
 Version     : 1.0
 Date        : 17/1/6
 Description :视频信息描述
 History     : 17/1/6 初始版本
 ***********************************************************/

#import <Foundation/Foundation.h>
#import "TVKPlayerDefine.h"
#import "ITVKAsset.h"

/**
 * TVKMediaInfo由外部传入，包含播放必须的字段。
 */
@interface TVKMediaInfo : NSObject

/**
 * @brief TVKMediaInfo原始初始化方法不可用，使用initWithAsset方法
 */
- (instancetype)init NS_UNAVAILABLE;

/**
 * @brief 通过Asset创建TVKMediaInfo对象
 * @param asset 资源类,通过TVKAssetFactory创建获得
 */
- (instancetype)initWithAsset:(id<ITVKAsset>)asset NS_DESIGNATED_INITIALIZER;

/**
 * @brief asset资源类，创建参考TVKAssetType和TVKAssetFactory.h
 *        注意！！！ 在xml直出、离线转在线场景下asset会被替换，可能会有多线程访问的问题，如果外部需要通过asset读取vid等id信息，需要用一个临时变量记录asset，并访问该临时变量
 */
@property (nonatomic, strong) id<ITVKAsset> asset;

/**
 * @brief 播放类型，定义见TVKPlayType
 */
@property (nonatomic, assign) TVKPlayType playType __attribute__((deprecated("该属性内部已弃用，调用方需理解自己的业务场景创建对应ITVKAsset")));

/**
 * @brief 视频播放清晰度,定义见TVKPlayerDefine.h，清晰度有后台控制
 */
@property (nonatomic, copy) NSString *definition;

/**
 * @brief 播放的起始点，单位s.默认值为0
 */
@property (nonatomic, assign) NSTimeInterval startPosition;

/**
 * @brief 跳过结尾skipEndPosition时长，单位s.例如电视剧尾部有2分钟需要跳过，则值为2*60.默认值为0
 */
@property (nonatomic, assign) NSTimeInterval skipEndPosition;

/**
 * @brief 是否是付费视频
 */
@property (nonatomic, assign) BOOL isNeedCharge;

/**
 * @brief 广告用到的playmode，表示场景，用于广告的展现形式.默认不用设置.
 */
@property (nonatomic, assign) int adPlayMode;

/**
 * @brief 数据上报使用.
 * @discussion（注:飞天上报的进入详情页上报的key，请设置为feiTianDetailPageInfo,value为NSDictionary,value的格式如下
 *----------------
 *key      | value
 *----------------
 *stime    | （进入详情页时间点(单位:ms, 格林威冶时间)）
 *----------
 *code     | （错误码）
 *----------------
 * 注意：由于内部使用的上报组件（默认是odk）,对上报格式和类型有一定的要求，所以要确保上报的格式和字段类型等要符合上报的要求，否则可能会导致上报丢失。
 * 详见odk的上报要求：http://tapd.oa.com/webboss/markdown_wikis/#1010019311007970485
 * 本类的isValidReportInfoMap是对上报的基本检验。如果校验失败，则肯定不会设置给odk，这部分数据就会丢失。校验通过，则设置给odk，由odk进行上报。
 * odk本身没有提供校验函数，isValidReportInfoMap只是做了基本的校验，是否能被odk正确处理，请尽量参考odk的上报要求。
 */
@property (nonatomic, strong) NSDictionary<NSString *, NSObject *> *reportInfoMap;

/**
 * @brief 请求时url附带的参数，用在广告请求中，方便后续的扩展
 */
@property (atomic, strong) NSDictionary<NSString *, NSString *> *adRequestParamMap;

/**
 * @brief 视频请求的额外信息，用来适配一些小渠道的额外信息以及其他需要携带的扩展字断
 * extraRequestParamsMap 一个包含j扩展参数的字典，key和value都是NSString类型!!!
 *        key                :  value
 *        ----------------------------------
 *        defnsrc            : 清晰度来源
 *        incver             : app版本号
 *        srccontenid    : 短带长短视频id，短带长场景必须传入
 *        atmos             :是否请求杜比音频，仅直播可用。为0表示不需要杜比音频的流，为1表示需要
 */
@property (nonatomic, strong) NSDictionary<NSString *, NSString *> *extraRequestParamsMap;

/**
 * @brief 一些额外配置字段
 * configMap 包含扩展配置的字典，key和value都是NSString类型!!!
 *        key                 :  value
 *        -------------------------------------------------
 *        airplay_min_defn    : airplay最低清晰度
 *        history_vid         : 历史记录videoId，秒播时传入
 *        enable_quick_play   : 秒播开关, 1:开启，0:关闭
 *        skip_start_and_end  : 是否跳过片头, 1:跳过，0:不跳过(仅秒播用)
 *        live_type           : 1:直播答题
 *        force_online        : 用于离线下载的视频，0:不强制走在线，1:强制走在线
 *        play_mode           : 播放模式。1:高铁播放模式
 *        see_back_time       : 直播回看时间，仅直播用, -1:正常直播，> 0:直播回看时间
 *        is_airplay          : 本次播放是否为airplay,播放器内部会选择合适的播放器，1: 当前为airplay播放
 *        is_offline_airplay  : 表示离线视频是否走有线投射等场景，会使用系统AVQueuePlayer。1: 是，0:不是
 *        force_use_app_skip_start_and_end  : 主要用于秒播场景下，是否强制使用App传入的跳过片头片尾。1: 使用 0: 不使用
 *        skip_start_end_strategy           : 目前主要为非秒播使用,跳过片头片尾策略。0: 使用app传入;1: 根据vinfo设置 默认为0
 *        app_testid         :实验id，字符串中内容为数字
 *        enable_audio_frame_out :     是否开启音频帧回调，1:开启，0:关闭
 *        enable_xr_spatial_audio:     是否开启空间音频能力，1:开启，0:关闭
 *        is_xr_video_immersive :  是否在沉浸式空间播放视频「即展示视频的可绘制容器对象为 ITVKVideoPlayerComponentHolder（仅针对vision os平台）」。1: 是，0:不是
 *        enable_ts_to_fmp4_convert:  是否开启ts转fmp4格式转换，1:开启，0:关闭
 *        enable_subtitle_data_out :  是否开启字幕数据回调「目前只在vision os平台有效，后续再放开」，1:开启，0:关闭
 *        force_avplayer_video_view :  是否强制走AVPlayerViewController「只针对vision os平台」，1:开启，0:关闭
 *        subtitle_data_type :  设置透出字幕数据类型「目前只在vision os平台有效，后续再放开」，0 ：透出文本类型字幕，1 ：透出图片类型字幕，默认为 -1
 *        audio_frame_callback_nb_samples_per_channel:        回抛的音频帧个数
 *        audio_channel_layout:          回调的音频声道布局，取值为TVKAudioChannelLayout枚举定义
 *        audio_sample_format:          回调的音频格式，取值为TVKAudioSampleFormat枚举定义
 *        firstBootFromOtherApp: 是否是外部拉起，1:外部拉起，0:非外部拉起。
 *        url_flv_video_size_changed:  播放媒体源为flv流时, 需要onVideoSizeChanged回调需传1, 默认为0不用设置.
 *        disable_start_position_correction  : 1：起播时如果传入startpositon超出试看范围，直接抛错。   0（默认）：起播时如果传入startpositon超出试看范围，纠正到试看起点开始播
 *        customized_sei_list : 业务定制的sei，原始数据是int数组，需要转为字符串设置进来，各个数之间用","间隔，例如:数组[1, 2, 3]->@"1,2,3"
 *        enable_preview_accurate_start :试看场景是否启用精准起播  1：开启 0：关闭   默认开启
 *        multi_network_card_open   :开启多网卡  1：开启  0：关闭  默认关闭
 *        download_proxy_biz_id: 设置这次播放需要使用的下载组件的biz id, 需要@selector(TPDataTransportMgr registerBizId:)注册获取
 *        scene_id: 场景ID，方便统计场景流量。该KEY对应的值为string类型，业务方自定义，字符串类型
 *        pcdn_charge_id: pcdn雾计算ID，方便各业务方单独进行雾计算结算（区分广告与正片），字符串类型
 *        save_path: 存储路径，主要是缓存完成后需要移动到磁盘的路径，由业务方提供，有拷贝到磁盘需求时才设置，字符串类型
 *        cache_copy_first: 是否优先缓存拷贝，有拷贝到磁盘需求时才设置.1表示移动文件时使用拷贝，本地目录文件还可以提供P2P上传，0表示使用rename方式移动，不能提供P2P，默认是0
 *        enable_show_watermark:  是否允许TVKPlayer绘制水印  0：关闭水印绘制   1：开启水印绘制
 *        open_with_picture_in_picture：是否在画中画中起播，用于画中画自动续播场景   1:是在画中画起播   0 :不是   不设置默认为0
 *        enable_picture_in_picture_seek_button: 设置画中画里是否显示快进快退按钮 1：显示快进快退按钮 0：不显示。  不设置默认显示
 *        enable_open_picture_in_picture_seamless:  设置是否开启画中画无缝拉起能力   1：开启  0：不开启   如果打开 起播后播放器内会做一些准备工作
 *        dlna_device_hevc_level : 配置dlna投射外部设备Hevc能力。能力值参考TVKHEVCLevel。仅通过ITVKQQLiveAssetRequester请求dlna地址时生效
 *        need_cache: 是否需要在播放过程中把数据下载到本地   1：下载到本地   0：不下载   不设置默认为0，设为其它非法值当做0值处理，仅点播可用
 *        is_play_in_detail_page: 是否详情页播放  1: 是   0：否
 *        xp2p_key: 腾讯云xp2p所使用的key，播放器不理解，透传给下载组件
 *        enable_audio_frame_out : 是否开启音频帧回调，1:开启，0:关闭。数据回抛将通过delegate的mediaPlayer:audioFrameOut:回调。
 *                                 会通过mediaPlayer:audioFrameOutAvailable:回调通知
 *        enable_video_frame_out : 是否开启视频帧回调，1:开启，0:关闭。数据回抛将通过delegate的mediaPlayer:onVideoFrameOut:回调。一些情况下，视频帧数据无法回调（比如加密视频），
 *                                 会通过mediaPlayer:videoFrameOutAvailable:回调通知
 *        out_video_frame_pixel_format:设置mediaPlayer:onVideoFrameOut:回调视频帧的像素格式，取值为TPPixelFormat枚举定义。不设置，默认为TVKPixelFormatUnknown，由内部根据视频源自行决策。
 *        real_user_play: 配置当前播放是否是真实用户点击播放. 默认为 "1" 即真实用户点击播放 1 真实用户点击播放，0 非真实用户播放（例如业务预加载播放）
 *        is_airplay_audio_output: 本次播放是否通过airplay协议将音频输出到homepod/homepod mini等设备上进行音频输出。"0" 否。"1" 是。默认为"0"
 *        subtitle_language_id: 选中的字幕语言ID 业务需要记住最新一次的TVKNetVideoInfo.currentSubtitle.langId
 *        platform_specific_features: 平台特有的功能.值为TVKPlatformSpecificFeature中的定义。此字段用于平台特有的功能，不同平台可能有不同的功能，需要根据具体的功能来设置。设置前请联系播放器开发同学，确认在此平台支持相关功能。
 *        datong_data_report_key:  大同唯一标识，用于大同上报播放数据与业务数据绑定，字符串类型
 *
 * key请使用TVKPlayerDefine.h中的kTVKMediaInfoConfigMapKeyXXX，每个key有详细注释说明
 */
@property (nonatomic, strong) NSDictionary<NSString *, NSString *> *configMap;

/**
 * @brief 广告场景，用于特殊场景调用,默认不设置即可.SDK透传.
 */
@property (nonatomic, copy) NSDictionary *adParamMap;

/**
 * @brief 离线下载播放信息
 * @discussion 仅仅playType为TVKPlayTypeDidDownLoadVod(腾讯视频原生完整下载后播放)和TVKPlayTypeWillDownLoadVod(腾讯视频原生边下载边播放)设置生效.
 *   downloadVodInfo 格式如下
 *        ----------------
 *        key      | value
 *        ----------------
 *        duration | （总时长）
 *        ----------
 *        count    | （总的分片地址数量）
 *        ----------------
 *        index(分片索引)| （具体分片信息字典（clipInfo））
 *        ---------------
 *
 *        具体分片信息字典(clipInfo)格式：
 *        ----------------
 *        key     | value
 *        ----------------
 *        url     | 此分片地址
 *        ----------------
 *        clipDuration | 此分片时长
 *        -------------------
 */
@property (nonatomic, copy) NSDictionary *downloadVodInfo;

/**
 * @brief 是否是feeds流视频
 */
@property (nonatomic, assign) BOOL isFeedVideo;

/**
 * @brief 视频软字幕语种,视频有软字幕时才有效。 参考TVKPlayerDefine.h中软字幕语种TVKMediaSubtitleLanguage的定义
 */
@property (nonatomic, copy) NSString *subtitleLanguage;

/**
 * @brief 音轨名称,具体多音轨的列表在起播之后，在TVKNetVideoInfo中的audioTrackInfoList有列举。音轨列表由后台下发。
 * 不设置情况下，会默认使用视频源中的音轨。
 */
@property (nonatomic, copy) NSString *audioTrack;

/**
 @brief optional. 数据上报中使用，用于区分同一个平台号下不同业务场景
 0：默认 1：腾讯视频app内游戏场景（后续如果新增依次递增）
 */
@property (nonatomic, assign) int bizId;

/// 额外的请求头，会拼到vinfo请求时的http header里。 主要用于vinfo服务过载保护，APP将用户等级等参数通过header传给后台，后台能在第一时间针对用户等级采取相应的过载保护措施
@property (nonatomic, strong) NSDictionary<NSString *, NSString *> *extraVinfoRequestHeadersMap;

/// 广告初始化参数，透传给广告
@property (nonatomic, strong) NSDictionary *adInitConfigMap;

@end
