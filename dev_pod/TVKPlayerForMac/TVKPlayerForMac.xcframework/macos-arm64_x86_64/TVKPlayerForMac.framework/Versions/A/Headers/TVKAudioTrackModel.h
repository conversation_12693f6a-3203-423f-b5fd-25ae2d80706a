/************************************************************
 Copyright (C), 1998-2019年, Tencent Tech. Co., Ltd.
 FileName   : TVKAudioTrackModel.h
 Author     : andygao
 Version    : 1.0
 Date       : 2019/3/18
 Description: 多音轨信息
 History    : 2019/3/18 初始版本
 ************************************************************/

#import <Foundation/Foundation.h>
#import "TVKPlayerDefine.h"

/// @brief 音频信息
@interface TVKAudioTrackModel : NSObject
/// 1:当前选中, 0:未选中
@property (nonatomic, assign) int sl;
/// 音轨动态水印，该节点为空或不存在则无动态水印，否则每次切换到该独立音轨成功后需要展示动态水印
@property (nonatomic, copy) NSString *action;
/// 音轨格式号
@property (nonatomic, copy) NSString *formatId;
/// 音轨属性(1:aac，2:DolbySurround，3:Dolby Atmos，4:Dolby2.0，5:DTS HD，6:DTS X，7:pcg2.0，8:aac5.1，9:audiovivid 5.1.4)
@property (nonatomic, assign) int audio;
/// 是否需要付费，非零需要付费。（付费音频用户未付费时不给url地址）
@property (nonatomic, assign) int lmt;
/// 音轨特性
@property (nonatomic, assign) int feature;
/// 展示文案（音轨名）
@property (nonatomic, copy) NSString *name;
/// 试听时长，-1：表示无限制（免费或用户已付费），0：表示无试听，大于0为试听最大时长
@property (nonatomic, assign) int preview;
/// 音频流名称
@property (nonatomic, copy) NSString *track;
/// 下载地址 非选中音频或无权播放时无下载地址
@property (nonatomic, copy) NSArray<NSString *> *urls;
/// 用于p2p
@property (nonatomic, copy) NSString *keyId;
/// 是否是avs分离格式
@property (nonatomic, assign, getter=isAvsSeparate) BOOL avsSeparate;
/// 音轨类型(0.外挂音轨，1.合流音轨)
@property (nonatomic, assign) TVKAudioTrackType type;
/// 外挂轨道的直出m3u8
@property (nonatomic, copy) NSString *m3u8;
/// 是否离线
@property (nonatomic, assign) BOOL offline;
@end
