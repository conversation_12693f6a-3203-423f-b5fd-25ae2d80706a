/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKSDK.h
 Author      : liyukuan
 Version     : 1.0
 Date        : 17/2/17
 Description :
 History     : 17/2/17 初始版本
 ***********************************************************/

#import <Foundation/Foundation.h>
#import "TVKPlayerDefine.h"
#import "ITVKBeaconDataReporter.h"
#import "ITVKPowerController.h"

@protocol TVKLogDelegate;
@protocol TVKLogReportDelegate;

/**
@brief SDK配置类，设置和获取SDK的一些公共参数
*/
@interface TVKSDK : NSObject

/**
@brief 获取SDK版本号
*/
+ (NSString *)sdkVersion;

/**
@brief guid，cgi请求会带上，若不填写则sdk会使用自己生成的guid。在registWithAppkey之前设置生效，否则不生效
*/
@property (class, nonatomic, strong) NSString *guid;

/**
@brief 用户id.qq登陆的时，请传递qq号;微信登陆时，请传递微信openid。请求相关SDK配置时使用。在registWithAppkey之前设置生效，否则不生效
*/
@property (class, nonatomic, strong) NSString *uid;

/**
 * @brief 用于联通大王卡、移动、电信免流参数的传递.各免流参数传递说明如下：
 * 联通免流
 * 参数：
 * 键    ：  值
 * unicom：联通免流参数（字符串）
 * unicomtype：联通免流类型（数字）
 * 联通名流类型unicomtype：
 * 值    含义
 * 0    普通免流订购业务(默认)
 * 1    小王卡免流订购业务
 * 2    大王卡免流订购业务
 *
 * 电信免流
 * 键    :   值
 * telcom：电信免流参数 (字符串）
 *
 * 移动免流
 * 键  :   值
 * cmcc：移动免流参数(字符串）
 */
@property (class, atomic, strong) NSDictionary<NSString *, NSString *> *freeFlowParam;

/**
 * @brief 免流状态
 */
@property (class, atomic, assign) TVKFreeFlowType freeFlowType;

/**
 * @brief 免流状态是否已经同步到服务器的免流状态. 0为未同步, 1为已同步
 */
@property (class, atomic, assign) int freeFlowSynBackEndState;

/// 初始化sdk
/// @param appKey ,产品申请单AppKey，用户鉴权，需在其他方法前调用
/// @param guid 设备标识
+ (BOOL)registerWithAppKey:(NSString *)appKey guid:(NSString *)guid;

/// 是否已经初始化sdk
+ (BOOL)isRegistered;

/**
@brief 设置日志打印
@param logDelegate   代理
*/
+ (void)setLogDelegate:(id<TVKLogDelegate>)logDelegate;

/**
@brief 设置日志上传
@param logReportDelegate   代理
*/
+ (void)setLogReportDelegate:(id<TVKLogReportDelegate>)logReportDelegate;

/**
 @brief   获取平台号
 @return  平台号
*/
+ (NSString *)getPlatform;

/**
 @brief 播放来源，运维统计流量用
 @return  播放来源
*/
+ (NSString *)sdtfrom;

/// 设置sdk属性参数，key见tvkplayerDefine.h
/// @param propertyMap  属性map
+ (void)setSdkPropertyMap: (NSDictionary<NSString *, NSString *> *)propertyMap;

/// 设置AB实验分流ID
/// @param abUserId AB实验分流ID
+ (void)setAbUserId:(NSString *)abUserId;

/// 设置qimei36, 请在初始化sdk（registerWithAppKey）之前设置
+ (void)setQimei36:(NSString *)qimei36;

/// 获取蜂窝网卡流量消耗
/// @return 返回获取蜂窝网卡流量消耗, 单位为Byte, 无效值-1.
+ (NSInteger)cellularDataCost;

/// 设置灯塔数据上报实例。不设置则默认走tvk内部的TVKBeacon上报，如果设置的话  走业务设置进来的灯塔通路
/// 如果要设置的话 须要在init SDK（registerWithAppKey）之前调用
+ (void)setBeaconDataReporterBeforeInit:(id<ITVKBeaconDataReporter>)beaconDataReporter;

/// 设置debug状态，正式发布需要设置成false
/// 会影响初始化逻辑，需要在初始化前设置
+ (void)setDebugEnable:(BOOL)isDebug;

/// 设置播放器配置
/// @param config json格式的播放器配置，格式如下
/// {
///   "playerConfig": {
///     "tpDemuxerThreadPriority": 23,
///     "tpVideoDecoderThreadPriority": 23
///   }
/// }
+ (void)setPlayerConfig:(NSString *)config;

/**
 * @brief 功耗控制器属性
 */
+ (id<ITVKPowerController>)powerController;

@end
