/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITVKBatchVinfoRequester.h
 * @brief    批量vinfo请求接口
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/4/17
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TVKMediaInfo.h"
#import "TVKUserInfo.h"

NS_ASSUME_NONNULL_BEGIN

/// batch vinfo请求返回信息
@interface TVKBatchVinfoResponseInfo : NSObject

/// vid
@property (nonatomic, strong) NSString *vid;

/// 选中的清晰度
@property (nonatomic, strong) NSString *selectedDefinition;

/// url有效时长
@property (nonatomic, assign) NSInteger effectiveUrlDurationSec;

/// vinfo回包 xml格式
@property (nonatomic, strong) NSString *vinfo;

@end


/// @brief 批量获取视频信息Delegate，调用方实现此Delegate监听视频信息的回调
@protocol TVKBatchVinfoRequesterDelegate <NSObject>

/// 媒体信息获取完成
/// @param batchVinfo 返回的批量媒体信息.  key：业务定义的key，value：TVKBatchVinfoResponseInfo 后台返回的批量视频信息
- (void)onSuccess:(NSInteger)taskId batchVinfo:(NSDictionary<NSString *, TVKBatchVinfoResponseInfo *> *)batchVinfo;

/// 媒体信息获取失败
/// @param error 错误信息
- (void)onFailure:(NSInteger)taskId error:(NSError *)error;

@end

/// 批量vinfo请求接口，批量返回vinfo原始字符串
@protocol ITVKBatchVinfoRequester <NSObject>

/// delegate
@property (nonatomic, weak) id<TVKBatchVinfoRequesterDelegate> delegate;

/// 调用该方法开始批量获取媒体信息
/// - Parameters:
///   - batchVideoInfo:   key：业务定义的key，value：视频信息TVKMediaInfo
///   - userInfo: 用户信息
///     return :标识这次请求的taskId
- (NSInteger)requestBatchVinfo:(NSDictionary<NSString *, TVKMediaInfo *> *)batchVideoInfo userInfo:(nullable TVKUserInfo *)userInfo;
@end

NS_ASSUME_NONNULL_END
