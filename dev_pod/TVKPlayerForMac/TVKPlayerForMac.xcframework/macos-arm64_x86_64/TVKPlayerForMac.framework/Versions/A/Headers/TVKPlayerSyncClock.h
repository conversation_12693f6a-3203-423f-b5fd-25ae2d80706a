/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TVKPlayerSyncClock.h
 * @brief    播放器同步时钟
 * <AUTHOR>
 * @version  1.0.0
 * @date     2025/02/14
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// @brief 播放器同步时钟
@interface TVKPlayerSyncClock : NSObject

/// 播放器的播放位置, 单位ms
@property (nonatomic, assign) int64_t playerPositionMs;

/// 获取播放器器位置的系统时间，since 1970-01-01T00:00:00Z
@property (nonatomic, assign) int64_t systemTimeSince1970Ms;

@end

NS_ASSUME_NONNULL_END
