/************************************************************
 Copyright (C), 1998-2018, Tencent Tech. Co., Ltd.
 FileName    : TVKMediaPlayer.h
 Author      : hemanli,andygao
 Version     : 1.0
 Date        : 2017-1-6
 Description : 播放器接口文件
 History     : 2017-1-6 初始版本
 ************************************************************/

#import <Foundation/Foundation.h>
#import "TVKMediaPlaybackDelegate.h"
#import "TVKPlayerDefine.h"
#import "TVKMediaInfo.h"
#import "TVKVideoView.h"
#import "TVKUserInfo.h"
#import "TVKNetVideoInfo.h"
#import "ITVKMediaPlayer.h"
#import "ITVKVideoView.h"

NS_ASSUME_NONNULL_BEGIN
@protocol TVKAudioFxProcessor;
@protocol ITVKVideoFxProcessor;

/// 播放器接口定义
@interface TVKMediaPlayer : NSObject <ITVKMediaPlayer>

/**
 * @brief 创建一个播放器实例，请不要自己通过alloc和init创建，而是调用此方法
 * @return 播放器实例
 */
+ (TVKMediaPlayer *)mediaPlayer;

/**
 * @brief 正片播放回调
 */
@property (atomic, weak) id<TVKMediaPlaybackDelegate> playbackDelegate;

/**
 * @brief 广告播放回调
 */
@property (atomic, weak) id<TVKAdPlayDelegate> adPlayDelegate;

/**
 * @brief 广告业务透传回调, 需要遵守QADVideoSDKBusinessDelegate
 */
@property (nonatomic, weak) id adBusinessDelegate;

/**
 * @brief 截图回调
 */
@property (atomic, weak) id<TVKCaptureDelegate> captureDelegate;

/**
 * @brief 播放权限回调
 */
@property (atomic, weak) id<TVKPermissionDelegate> permissionDelegate;

/**
 * @brief playbackDelegate、adPlayDelegate、captureDelegate、permissionDelegate等delegated的回调队列。
 * @discussion 这里delegateCallBackQueue应该是个序列队列，这样能保证回调的方法是按照回调顺序回调。
 * 如果不设置或者设置为nil, 则内部会默认使用主线程。
 *
 * 注意： 对于mediaPlayer:showOutsideController:、mediaPlayerAdVideoView:等需要返回值的回调方法，
 * 为保证内部的运行统一，是在播放器的工作线程回调的，这里处理的时候要特别注意。
 */
@property (atomic, strong, nullable) dispatch_queue_t delegateCallBackQueue;
/**
 * @brief 展示视频的可绘制容器对象，由外面创建好，传入。可以是view或componentHolder
 * @see ITVKVideoView
 * @see ITVKVideoPlayerComponentHolder(仅针对vision os平台)
 * 如果是view，建议一个播放器对应一个videoView不要复用，若要复用，复用之前请调用TXVVideoView的removeAllSubviews;
 */
@property (atomic, strong) id<ITVKDrawableContainer> videoView;

/**
 * @brief 获取播放器内部保存的mediaInfo
 */
@property (nonatomic, strong) TVKMediaInfo *mediaInfo;

/**
 * @brief 播放器保存的userInfo,当cookie发生变化时，请相应也更新一下userinfo，保证播放期间使用的cookie和APP使用相一致
 */
@property (nonatomic, strong) TVKUserInfo *userInfo;

/**
 * @brief 播放进度回调时间间隔，默认1.0s
 */
@property (nonatomic, assign) NSTimeInterval progressInterval;

/**
 * @brief 是否可开启pip，你应该收到prepared之后再获取该属性，此时比较准确。
 */
@property (nonatomic, assign, readonly) BOOL pictureInPicturePossible;

/**
 * @brief pip是否激活
 */
@property (nonatomic, assign, readonly) BOOL pictureInPicureActive;

/**
 *  @brief 视频拉伸模式，请见TVKVideoStretchMode定义
 */
@property (nonatomic, assign) TVKVideoStretchMode stretchMode;

/**
 *  @brief 是否允许airplay投射，默认为YES
 */
@property (nonatomic, assign) BOOL allowsExternalPlayback;

/**
 *  @brief airplay镜像是否使用airplay投射，默认为YES
 */
@property (nonatomic, assign) BOOL usesExternalPlaybackWhileExternalScreenIsActive;

/**
 *  @brief 音量，取值范围[0, 1]，0意味着静音(mute)，play 后设置才能生效
 */
@property (nonatomic, assign) float volume;

/**
 *  @brief 广告音量，取值范围[0, 1]，0.0意味着静音(mute)
 */
@property (nonatomic, assign) float adVolume;

/**
 *  @brief 是否支持色盲，请在prepared之后获取该值。
 */
@property (nonatomic, assign, readonly) BOOL supportColorBlind;

#ifndef TVKPLAYER_NO_MONET
/**
 * @brief 获取后处理接口，在第一次获取时创建
 * @discusstion videoFxProcessor的生命周期仅在一次播放过程中有效，如果调用了播放stop方法，则内部会销毁videoFxProcessor，如果调用方持用了videoFxProcessor，
 * 则播放器stop之后，需要通过该property重新获取才能使用。
 */
@property (nonatomic, strong, readonly) id<ITVKVideoFxProcessor> videoFxProcessor;

#endif

/// 获取字幕渲染控制器
@property (nonatomic, strong, readonly) id<ITVKSubtitleRendererController> subtitleRenderController;

/**
 * @brief 获取音效处理组件，获取后需要添加特效，若要开始处理，调用connect接口，链接播放器
 */
@property (nonatomic, strong, readonly) id<TVKAudioFxProcessor> tvkAudioFxProcessor;

/**
 * @brief 播放入口，传入视频信息，准备播放，调用完该方法之后，须调用play方法开始播放
 * @param mediaInfo 视频信息，请看TVKMediaInfo定义
 * @param userInfo 用户账号信息
 */
- (void)openMediaPlayerWithMediaInfo:(TVKMediaInfo *)mediaInfo userInfo:(TVKUserInfo *)userInfo;

/**
 *  @brief 启动播放
 */
- (void)play;

/**
 *  @brief 暂停播放,默认会请求暂停广告
 */
- (void)pause;

/**
 *  @brief 暂停播放，不会请求“暂停广告”
 */
- (void)pauseWithoutAD;

/**
 *  @brief seek操作，设置播放时间点（实现快进快退）
 *  @param position 要seek到的位置，单位秒
 *  @warning 此方法即将废掉，不再推荐使用，请使用seekTo:mode:
 */
- (void)seekTo:(NSTimeInterval)position;

/**
 *  @brief seek操作，设置播放时间点（实现快进快退）
 *  @param position 播放时间点，单位秒
 *  @param mode     seek模式
 */
- (void)seekTo:(NSTimeInterval)position mode:(TVKSeekMode)mode;

/**
 *  @brief 用于直播回看
 *  @param position 要seek到的server时间，单位为毫秒，传入-1表示seek到正常直播，请参考TVKNetLiveSeebackInfo
 */
- (void)seekLive:(int64_t)position;

/**
 *  @brief 停止播放. 停掉播放的时候，务必调用，用于清理内部资源、必要的数据上报等
 */
- (void)stop;

/**
 * @brief 倍速播放
 * @param rate 倍速率，比如传入2表示以正常速度的2倍播放
 */
- (void)setPlayRate:(CGFloat)rate;

/**
 * @brief 跳过广告，只有在一边播放视频一边登陆而且是好莱坞回来才能调用，只对腾讯视频开放
 */
- (void)skipAd;

/**
 * @brief 循环播放
 * @param loopBack 是否循环播放，如果设置YES，则播放结束后会从头开始播放
 */
- (void)setLoopBack:(BOOL)loopBack;

/**
 * @brief 循环播放
 * @param loopBack 是否循环播放，如果设置YES，则播放结束后会从头开始播放
 * @param startPositionMs 跳过片头时间
 * @param skipEndPostionMs 跳过片尾时间
 */
- (void)setLoopBack:(BOOL)loopBack startPosition:(int64_t)startPositionMs skipEndPostion:(int64_t)skipEndPostionMs;

/**
 * 获取当前播放视频的总时长
 * @return 当前视频的总时长，单位秒
 */
- (NSTimeInterval)duration;

/**
 * @brief 获取当前可播放时长，即已缓冲的时长，调用方可据此向用户展示已缓冲的部分
 * @return 当前可播放时长，单位秒
 */
- (NSTimeInterval)playableDuration;

/**
 * @brief 实时获取所有的DCL上报信息，返回值是包含key/value键值对的json字符串，key与value都是String类型。
 * key可直接作为DCL反馈的上报字段，value是对应的值，主要是为了方便app能实时获取播放器的信息并透传上报到DCL反馈上
 * 
 * @return 获取所有的DCL上报信息
 */
- (NSString *)allDCLReportInfos;

/**
 * @brief 获取当前播放视频的播放位置
 * @return 当前播放视频的播放位置，单位秒
 */
- (NSTimeInterval)currentPosition;

/**
 * @brief 获取视频宽高
 * @return 视频宽高
 */
- (CGSize)videoSize;

/**
 * @brief 切换清晰度
 * @param definition 要切换的清晰度格式，清晰度类型见TVKPlayerDefine.h. 默认内部走无缝切换
 */
- (BOOL)switchMediaDefinition:(NSString *)definition;

/**
 * 切换清晰度。默认使用无缝切换清晰度，如果视频不支持无缝切换清晰度，将采用重新打开播放器的方式进行清晰度切换
 * @param mediaInfo 要切换清晰度的视频信息
 * @param userInfo 切换时，需要更新的用户信息
 * @return 调用是成功执行
 */
- (BOOL)switchDefinitionWithMediaInfo:(TVKMediaInfo *)mediaInfo userInfo:(TVKUserInfo *)userInfo;

/**
 * 切换清晰度。使用重新打开播放器的方式进行清晰度切换。不会走到无缝切换清晰度
 * @param mediaInfo 要切换清晰度的视频信息
 * @param userInfo 切换时，需要更新的用户信息
 * @return 调用是成功执行
 */
- (BOOL)switchDefinitionReopenWithMediaInfo:(TVKMediaInfo *)mediaInfo userInfo:(TVKUserInfo *)userInfo;

/**
 * @brief 截取当前播放的画面图像，结果通过captureDelegate返回
 * @param width 需要采集的宽，0则使用原始图像宽高
 * @param height 需要采集的高， 0则使用原始图像宽高
 * @return 返回此次截图的id
 */
- (int)captureImageWithWidth:(CGFloat)width height:(CGFloat)height;

/**
 * @brief 边下边播时，视频可播放时长，即已经下载的部分
 * @return 边下边播的可播放时长，单位秒
 */
- (NSTimeInterval)downPlayableDuration;

/**
 * @brief 通知播放器将要处的场景，主要用于广告的显示和消失
 * @param viewState 0:小窗; 1:全屏。
 */
- (void)informPlayerWillLayout:(int)viewState;

/**
 * @brief 通知播放器已经所处的场景，主要用于广告的显示和消失
 * @param viewState 0:小窗; 1:全屏。
 */
- (void)informPlayerDidLayout:(int)viewState;

/**
 * @brief 启动画中画
 */
- (void)startPictureInPicture;

/**
 * @brief 关闭画中画
 */
- (void)stopPictureInPicture;

/**
 * @brief 视频view旋转
 * @param angleX X方向转角，负值左转,正值右转
 * @param angleY Y方向转角，负值上转,正值下转
 * @param angleZ Z方向转角
 */
- (void)rotateViewWithAngleX:(float)angleX angleY:(float)angleY angleZ:(float)angleZ;

/**
 * @brief 播放过程中，场景等实时信息通知。
 * @param infoKey 是一个枚举值，请见TVKRealTimeInfoKey的定义
 * @param infoValue 根据key的不同，类型不同，具体如下
 *        key                            :  value
 *        ---------------------------------------
 *        TVKRealTimeInfoKeyIsPreload    : NSNumber类型，0表示非预加载，1代表预加载
 *        TVKRealTimeInfoKeySkipPos      : a NSDictionary，包含两个key-value对，key为字符串，value为NSNumber<float>，单位为秒,
 *                                         @{kTVKSkipStartPosKey : skipStartPos,
 *                                         kTVKSkipEndPosKey : skipEndPos}，
 *                                         kTVKSkipStartPosKey和kTVKSkipEndPosKey请见TVKPlayerDefine.h
 *        TVKRealTimeInfoKeyBackgroundAudioPlay    : NSNumber类型，BOOL值，YES为开启音频后台播放，NO为关闭。
 */
- (void)informRealTimeInfoChangedWithInfoKey:(TVKRealTimeInfoKey)infoKey infoValue:(NSObject *)infoValue;

/**
 * 通知广告层播放器详情页相关事件状态, 具体查看QADPlayerBaseEventInfo
 */
- (void)notifyAdEventInfo:(id)adEventInfo;

/// 设置RealVideoView（内部渲染视频的view）相对于父view的坐标，坐标系为系统UIView坐标系
/// - Parameters:
///   - offset.x: x轴相对坐标
///   - offset.y: y轴相对坐标
- (void)setDisplayOffset:(CGPoint)offset;

/// 设置RealVideoView（内部渲染视频的view）缩放
///  - Parameters:
///   - scale: 缩放比例，1为不缩放
///   - anchorPoint: 缩放锚点，参考系统CALayer中anchorPoint定义。锚点只用于缩放，缩放完成后锚点会恢复为默认值(0.5,0.5)
- (void)setDisplayScale:(CGFloat)scale anchorPoint:(CGPoint)anchorPoint;

@end

NS_ASSUME_NONNULL_END
