/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TVKDownloadProgressInfo.h
 * @brief    下载进度回调信息封装
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/11/7
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 下载进度回调信息封装
@interface TVKDownloadProgressInfo : NSObject

/// 可播时长，单位：毫秒。 hls 播放时可以返回准确的可播时长，其他格式不可用
@property (nonatomic, assign) int playableDurationMs;

/// 下载速度 KB/s
@property (nonatomic, assign) int downloadSpeedKBs;

/// 当前下载字节数
@property (nonatomic, assign) int64_t currentDownloadSizeByte;

/// 视频文件总字节数，有 vinfo 时一般通过 vinfo 获取总文件大小，其他情况可能没有
@property (nonatomic, assign) int64_t totalFileSizeByte;

/// 下载组件透传的额外信息
@property (nonatomic, copy) NSString *extraInfo;

@end

NS_ASSUME_NONNULL_END
