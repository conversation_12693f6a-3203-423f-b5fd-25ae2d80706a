//
//  ISonaAudioProcessor.h
//  TVKPlayer
//
//  Created by den<PERSON><PERSON><PERSON> on 2020/6/11.
//  Copyright © 2020 tencent. All rights reserved.
//

#ifndef TVKAudioFxProcessor_h
#define TVKAudioFxProcessor_h

#import "ITVKAudioFx.h"
#import "TVKAbstractProcessor.h"
#import "TVKAudioEffectInfo.h"
#import "ITVKAudioPostProcessorDelegate.h"

/// 音效处理器返回状态
/// 注意！！！这些定的所有枚举均对外，千万不要改现有枚举对应的值，这会导致依赖老版本tvk的sdk和依赖新版本tvk的工程一起编译运行后走到错误的逻辑中。
/// 别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!
typedef NS_ENUM(NSUInteger, TVKAudioProcessorErrorCode) {
    // 正常
    TVKAudioProcessorOK,
    // 通用异常
    TVKAudioProcessorError,
    // 初始化特效失败
    TVKAudioProcessorPrepareFailed,
    // 链接失败
    TVKAudioProcessorConnectFailed,
    // 音效已存在，请移除已add的音效
    TVKAudioProcessorAlreadyExsit,
    // 音效对象不存在
    TVKAudioProcessorNoExsit,
    // 音效在当前播放无效
    TVKAudioProcessorUnAvailable,
};

// 基于音效处理器，用于加载、删除特效
// 处理器与播放器链接后，音频特效才可生效
// 当需要停止音效处理时,主动删除所添加的特效，
// 否则无法断开与播放器的链接
@protocol TVKAudioFxProcessor <TVKAbstractProcessor>

/// 音频后处理信息回调
@property (atomic, weak, nullable) id<ITVKAudioPostProcessorDelegate> delegate;

/// 添加特效
- (TVKAudioProcessorErrorCode)addEffect:(id<ITVKAudioFx>)effect;

/// 删除特效
- (TVKAudioProcessorErrorCode)removeEffect:(id<ITVKAudioFx>)effect;

/// 删除所有特效
- (void)removeAllEffects;

/// 获取音效信息列表, 请在收到 ITVKAudioPostProcessorDelegate.onSupportedAudioEffectInfoUpdate 通知后调用此接口获取
- (nullable NSArray<TVKAudioEffectInfo *> *)getSupportedAudioEffectInfo;

@end

#endif /* TVKAudioFxProcessor_h */
