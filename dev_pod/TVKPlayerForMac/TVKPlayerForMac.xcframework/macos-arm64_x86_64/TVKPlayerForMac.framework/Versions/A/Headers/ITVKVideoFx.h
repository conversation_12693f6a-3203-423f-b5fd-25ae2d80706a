/*****************************************************************************
 * @copyright Copyright (C), 1998-2021, Tencent Tech. Co., Ltd.
 * @file     TVKColorBlindnessFxImpl.h
 * @brief    VideoFx
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/2/19
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 视频特效类型
/// 注意！！！这些定的所有枚举均对外，千万不要改现有枚举对应的值，这会导致依赖老版本tvk的sdk和依赖新版本tvk的工程一起编译运行后走到错误的逻辑中。
/// 别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!  别动老枚举值，新枚举一律在末尾新增!
typedef NS_ENUM(NSUInteger, TVKVideoFxType) {
    // 色盲矫正
    TVKColorBlindnessEffect,
    // 色彩增强
    TVKColorSDREnhanceEffect,
    // 超分辨率
    TVKSuperResolutionEffect,
    // 毛玻璃视频叠加特效
    TVKGaussionBlurVideoOverlayEffect,
    // VR特效
    TVKVREffect,
    // TVM超分辨率特效
    TVKTVMSuperResolutionEffect,
    // 色温特效
    TVKColorTemperatureEffect,
    // 颜色查找表
    TVKLookupTableEffect,
    // Alpha Video有透明效果的视频
    // 此特效对输入的视频数据有一定要求，详细见{@link ITVKAlphaVideoFx}
    TVKAlphaVideoEffect
};

/**
 * 视频特效基类，定义了基础的特效信息以及特效参数
 */
@protocol ITVKVideoFx <NSObject>

/**
 * 特效描述，子类负责写入描述信息，可用于日志，或者其他说明场景
 */
@property (nonatomic, readonly) NSString* effectDescription;

/**
 * 特效类型
 */
@property (nonatomic, readonly) TVKVideoFxType effectType;

@end

