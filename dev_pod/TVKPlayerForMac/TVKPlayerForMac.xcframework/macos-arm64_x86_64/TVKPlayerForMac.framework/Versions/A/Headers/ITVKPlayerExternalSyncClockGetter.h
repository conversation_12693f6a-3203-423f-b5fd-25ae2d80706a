/*****************************************************************************
 * @copyright Copyright (C), 1998-2025, Tencent Tech. Co., Ltd.
 * @file     ITVKPlayerExternalSyncClockGetter.h
 * @brief    获取外部时钟同步器的时钟
 * <AUTHOR>
 * @version  1.0.0
 * @date     2025/02/14
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

#import "ITVKPlayerSyncSource.h"
#import "TVKPlayerSyncClock.h"

NS_ASSUME_NONNULL_BEGIN

/// @brief 获取外部时钟同步器的时钟, 实现ITVKPlayerSyncSource的接口
@protocol ITVKPlayerExternalSyncClockGetter <ITVKPlayerSyncSource>

/// @brief 获取外部时钟同步器的时钟
/// @return 返回外部时钟同步器的时钟
- (TVKPlayerSyncClock *)getExternalSyncClock;

@end

NS_ASSUME_NONNULL_END
