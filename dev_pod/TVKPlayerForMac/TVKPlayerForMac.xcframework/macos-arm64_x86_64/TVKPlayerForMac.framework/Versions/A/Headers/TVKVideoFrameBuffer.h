/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TVKVideoFrameBuffer.h
 * @brief    视频帧数据定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/6/6
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "TVKPixelFormat.h"
#import <CoreVideo/CVPixelBuffer.h>
#import <Foundation/Foundation.h>

#if TARGET_OS_OSX
#import <AppKit/AppKit.h>
#else
#import <UIKit/UIKit.h>
#endif

NS_ASSUME_NONNULL_BEGIN

/// 视频帧数据定义
@interface TVKVideoFrameBuffer : NSObject

/// 当format为TVKPixelFormatCVPixelBuffer时，该值才有效，并且等于data[3]
@property (nonatomic, assign) CVPixelBufferRef pixelBuffer;

/// 原始视频帧数据
/// 当数据为非planar时，data[0]存放数据，
/// 当数据为planar时，data[0] 至 data[planeCount-1] 存放每个通道的数据。
/// 当format为TVKPixelFormatCVPixelBuffer时，data[3]指向CVPixelBufferRef数据。
@property (nonatomic, assign) uint8_t **data;

/// 每个通道的linesize
@property (nonatomic, strong) NSArray<NSNumber *> *lineSize;

/// 旋转角度，取值为0、90、180、270
@property (nonatomic, assign) int rotation;

/// 显示宽
/// 部分带有SAR信息的视频，数据宽高不一定等于显示宽高，需要根据该值来决定如何做显示
@property (nonatomic, assign) int displayWidth;

/// 显示高
/// 部分带有SAR信息的视频，数据宽高不一定等于显示宽高，需要根据该值来决定如何做显示
@property (nonatomic, assign) int displayHeight;

/// presentation time stamp, 单位毫秒
@property (nonatomic, assign) int64_t ptsMs;

/// 当前帧的轨道id，与ITVKPlayer中的getTrackInfo返回的数组的下标对应
@property (nonatomic, assign) int trackID;

/// 视频格式
@property (nonatomic, assign) TVKPixelFormat format;

/// 视频原始宽度，即数据宽度
@property (nonatomic, assign) int width;

/// 视频原始高度，即数据高度
@property (nonatomic, assign) int height;

/// 将TVKVideoFrameBuffer转化为UIImage
/// 目前仅支持TVKPixelFormatRGBA、TVKPixelFormatRGB24和TVKPixelFormatCVPixelBuffer
/// @return 转换后的UIImage或NSImage，若转化失败则返回nil
#if TARGET_OS_OSX
- (nullable NSImage *)toImage;
#else
- (nullable UIImage *)toImage;
#endif
@end

NS_ASSUME_NONNULL_END
