//
//  TVKFactory.h
//  TVKPlayer
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/10/30.
//  Copyright © 2019 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "ITVKMediaPlayer.h"
#import "ITVKDownloadAssetRequester.h"
#import "ITVKVideoView.h"
#import "ITVKQQLiveAssetRequester.h"
#import "ITVKRichMediaAsyncRequester.h"
#import "ITVKBatchVinfoRequester.h"
#import "ITVKVideoFrameCapture.h"
#import "ITVKSnapshotor.h"
#import "ITVKPictureSRProcessor.h"

/// @brief 播放器工厂
@interface TVKFactory : NSObject

/**
 * 获取播放器实例
 */
+ (id<ITVKMediaPlayer>)mediaPlayer;

/**
 * 获取播放器视图，用于设置给播放器
 */
+ (id<ITVKVideoView>)videoViewWithFrame:(CGRect)frame;

/**
 * 获取ITVKPlayPreparer实例，用于获取视频后台的相关信息
 */
+ (id<ITVKQQLiveAssetRequester>)qqLiveAssetRequester;

/**
 * 获取腾讯视频离线点播资源请求器
 */
+ (id<ITVKDownloadAssetRequester>)downloadAssetRequester;

/**
 * 获取ITVKRichMediaAsyncRequester的实例，用于富媒体信息的异步请求
 */
+ (id<ITVKRichMediaAsyncRequester>)richMediaAsyncRequester;

/// 获取batvinfo资源请求器
+ (id<ITVKBatchVinfoRequester>)batchVinfoRequester;

/// 获取视频截取器
+ (id<ITVKVideoFrameCapture>)videoFrameCapture;

/// 获取不依赖播放实例的视频截图器
/// @param asset URL视频资源
/// @param delegate 截图回调
+ (id<ITVKSnapshotor>)videoSnapshotor:(id<ITVKAsset>)asset
                             delegate:(id<ITVKSnapshotorDelegate>)delegate;

/// 获取不依赖播放实例的视频截图器
/// @param asset URL视频资源
/// @param delegate 截图回调
/// @param delegateQueue 截图回调工作队列
+ (id<ITVKSnapshotor>)videoSnapshotor:(id<ITVKAsset>)asset
                             delegate:(id<ITVKSnapshotorDelegate>)delegate
                        delegateQueue:(dispatch_queue_t)delegateQueue;

/// 获取图片超分处理器实例
+ (id<ITVKPictureSRProcessor>)sharedPictureSRProcessor;

@end
