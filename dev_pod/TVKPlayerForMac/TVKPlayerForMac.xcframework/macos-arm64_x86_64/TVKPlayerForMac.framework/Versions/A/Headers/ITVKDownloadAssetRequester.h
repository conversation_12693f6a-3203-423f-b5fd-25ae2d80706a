//
//  ITVKDownloadAssetRequester.h
//  TVKPlayer
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/10/30.
//  Copyright © 2019 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TVKMediaInfo.h"
#import "TVKUserInfo.h"
#import "TVKVODPlayInfo.h"

/// @brief Vinfo离线请求参数
@interface TVKDownloadAssetRequestParam : NSObject
/// 视频信息
@property (nonatomic, strong) TVKMediaInfo *mediaInfo;
/// 用户信息
@property (nonatomic, strong) TVKUserInfo *userInfo;
/// 视频格式
@property (nonatomic, assign) TVKMediaFormat mediaFormat;
/// sdtfrom
@property (nonatomic, copy) NSString *offlineSdtfrom;
/// 平台号
@property (nonatomic, copy) NSString *offlinePlatform;

@end

/// @brief 视频信息Delegate，调用方实现此Delegate监听视频信息的回调
@protocol TVKDownloadAssetRequesterDelegate <NSObject>

/// 媒体信息获取完成
/// @param vodPlayInfo 媒体信息
- (void)onSuccess:(TVKVODPlayInfo *)vodPlayInfo opaque:(id)opaque;

/// 媒体信息获取失败
/// @param error 错误信息
- (void)onFailure:(NSError *)error opaque:(id)opaque;

@end


/// 专为下载用的cgi请求接口
@protocol ITVKDownloadAssetRequester <NSObject>

/// 回调
@property (nonatomic, weak) id<TVKDownloadAssetRequesterDelegate> delegate;


/// 发起视频信息获取请求。同一个实例，在delegate返回之前，不可再发起请求，否则会取消之前的请求。
/// 如果需要同时请求多个实例，建议通过TVKFactory构建多个实例。
/// @param requestParam 请求参数
/// @param opaque 调用者自定义的一个值，底层不理解它的含义，在TVKDownloadAssetRequesterDelegate返回时，会将这个值带回给调用者。调用者可以用它来区分是哪一次requestWithMediaInfo调用
- (void)requestWithMediaInfo:(TVKDownloadAssetRequestParam *)requestParam opaque:(id)opaque;

/// 取消当前的请求
- (void)cancel;

@end
