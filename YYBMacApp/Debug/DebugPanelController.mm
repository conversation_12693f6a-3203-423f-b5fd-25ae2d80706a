//  DebugPanelController.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/5/29.
//

// DebugPanelController.m

#import "DebugFunctionMenuViewController.h"
#import "DebugMainMenuViewController.h"
#import "DebugNotificationTestViewController.h"
#import "DebugPanelController.h"
#import "KSNavigationController.h"
#import "DebugIPCViewController.h"
#import "DebugAria2TestVeiwController.h"
#import "DebugAria2DownloadVeiwController.h"
#import <YYBMacFusionSDK/YYBMacLogUpload.h>
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import <YYBMacFusionSDK/ResHubCommonDefines.h>
#import "YYBRoutes.h"
#import "YYBAlert.h"
#import "YYBDiagnosticLog.h"
#import "EngineDownloadHelper.h"
#import "EngineDownloadProxy.h"
#import "MacroUtils.h"
#import "FileUtils.h"
#import "EngineInfoCenter.h"
#import "DebugDownloadButtonTestViewController.h"

// 日志标签
static NSString *const kLogTag = @"DebugPanelController";

@interface DebugPanelController () <NSPageControllerDelegate, DebugMainMenuDelegate, DebugFunctionMenuDelegate, NSWindowDelegate>

@property (nonatomic, weak) NSViewController<KSNavigationControllerCompatible> *currentVC;
@property (nonatomic, strong) NSButton *backButton;
@property (nonatomic, strong) NSTextField *titleLabel;
@property (nonatomic, strong) NSTextField *engineDownloadLabel;
@property (nonatomic, strong) NSTextField *vmsDownloadLabel;
@property (nonatomic, strong) KSNavigationController *navVC;
@property (nonatomic, strong) NSPanel *aria2Panel;

@end

@implementation DebugPanelController

#pragma mark - 生命周期管理

- (void)viewDidLoad {
    [super viewDidLoad];
    // 创建导航栏
    [self setupNavigationBar];
    // 创建内容容器视图
    [self setupContentView];
    // 注册引擎下载监听
    [self registerEngineDownloadCallback];
    // 注册vms下载监听
    [self registerVmsDownloadCallback];
}

#pragma mark - 视图设置

- (void)setupContentView {
    // 创建导航容器
    DebugMainMenuViewController *debugMainMenuVC = [[DebugMainMenuViewController alloc] init];
    debugMainMenuVC.delegate = self;
    self.navVC = [[KSNavigationController alloc] initWithRootViewController:debugMainMenuVC];
    self.navVC.view.frame = self.view.bounds;
    [self.view addSubview:self.navVC.view];
    self.currentVC = debugMainMenuVC;
    // 自动布局约束
    self.navVC.view.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.navVC.view.topAnchor constraintEqualToAnchor:self.view.topAnchor constant:40],
        [self.navVC.view.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],
        [self.navVC.view.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.navVC.view.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor]
    ]];
    [self addChildViewController:self.navVC];
}

#pragma mark - 导航栏设置

- (void)setupNavigationBar {
    // 返回按钮
    self.backButton = [NSButton buttonWithTitle:@"返回" target:self action:@selector(goBack)];
    self.backButton.bezelStyle = NSBezelStyleRoundRect;
    self.backButton.hidden = YES;
    self.backButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.backButton];
    // 标题标签
    self.titleLabel = [self setUpCommonLabel:@"调试面板"];
    self.engineDownloadLabel = [self setUpCommonLabel:@""];
    self.vmsDownloadLabel = [self setUpCommonLabel:@""];
    [self.view addSubview:self.titleLabel];
    [self.view addSubview:self.engineDownloadLabel];
    [self.view addSubview:self.vmsDownloadLabel];
    // 导航栏约束
    [NSLayoutConstraint activateConstraints:@[
        [self.backButton.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.backButton.topAnchor constraintEqualToAnchor:self.view.topAnchor constant:10],
        [self.backButton.widthAnchor constraintEqualToConstant:80],
        [self.backButton.heightAnchor constraintEqualToConstant:30],
        [self.titleLabel.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.view.topAnchor constant:10],
        [self.titleLabel.widthAnchor constraintEqualToConstant:200],
        [self.titleLabel.heightAnchor constraintEqualToConstant:30],
        [self.engineDownloadLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:120],
        [self.engineDownloadLabel.topAnchor constraintEqualToAnchor:self.view.topAnchor constant:10],
        [self.vmsDownloadLabel.leadingAnchor constraintEqualToAnchor:self.engineDownloadLabel.trailingAnchor constant:10],
        [self.vmsDownloadLabel.topAnchor constraintEqualToAnchor:self.view.topAnchor constant:10]
    ]];
}

- (NSTextField *)setUpCommonLabel:(NSString*)name {
    NSTextField * label = [NSTextField labelWithString:name];
    label.font = [NSFont systemFontOfSize:12 weight:NSFontWeightBold];
    label.alignment = NSTextAlignmentCenter;
    label.editable = NO;
    label.selectable = NO;
    label.drawsBackground = NO;
    label.bezeled = NO;
    label.translatesAutoresizingMaskIntoConstraints = NO;
    return label;
}

#pragma mark - 导航控制

- (void)pushToVC:(NSViewController<KSNavigationControllerCompatible> *)vc {
    if (!vc) {
        return;
    }
    [self.navVC pushViewController:vc animated:YES];
    self.currentVC = vc;
    [self updateNavigationBar];
}

- (void)goBack {
    NSViewController<KSNavigationControllerCompatible> *backVC = [self.navVC popViewControllerAnimated:YES];
    if (!backVC) {
        return;
    }
    self.currentVC = backVC;
    [self updateNavigationBar];
}

- (void)updateNavigationBar {
    // 更新返回按钮状态（不在根页面时显示）
    self.backButton.hidden = (self.navVC.viewControllers.count <= 1);
    // 更新标题（从当前对象获取）
    if (self.currentVC.title.length > 0) {
        self.titleLabel.stringValue = self.currentVC.title;
    } else {
        self.titleLabel.stringValue = @"";
    }
}

#pragma mark - DebugMainMenuDelegate

- (void)debugMainMenuDidSelect:(NSString *)itemName {
    NSViewController<KSNavigationControllerCompatible> *vc = nil;
    if ([itemName isEqualToString:@"功能调试"]) {
        DebugFunctionMenuViewController *debugFunctionMenuVC = [[DebugFunctionMenuViewController alloc] init];
        debugFunctionMenuVC.title = itemName;
        debugFunctionMenuVC.delegate = self;
        debugFunctionMenuVC.view.frame = self.navVC.view.bounds;
        vc = debugFunctionMenuVC;
        [self pushToVC:vc];
    } else if ([itemName isEqualToString:@"ipc调试"]) {
        DebugIPCViewController *debugFunctionMenuVC = [[DebugIPCViewController alloc] init];
        debugFunctionMenuVC.title = itemName;
        debugFunctionMenuVC.view.frame = self.navVC.view.bounds;
        vc = debugFunctionMenuVC;
        [self pushToVC:vc];
    } else if ([itemName isEqualToString:@"自定义商店URL"]) {
        [self showCustomStoreURLInputPanel];
    }
}

#pragma mark - DebugFunctionMenuDelegate

- (void)debugFunctionMenuDidSelect:(NSString *)itemName {
    NSViewController<KSNavigationControllerCompatible> *vc = nil;
    if ([itemName isEqualToString:@"通知调试"]) {
        DebugNotificationTestViewController *debugNotificationTestVC = [[DebugNotificationTestViewController alloc] init];
        debugNotificationTestVC.title = itemName;
        debugNotificationTestVC.view.frame = self.navVC.view.bounds;
        //         debugNotificationTestVC.delegate = self;
        vc = debugNotificationTestVC;
    } else if ([itemName isEqualToString:@"下载服务调试"]) {
        DebugAria2TestVeiwController *debugAria2TestVC = [[DebugAria2TestVeiwController alloc] init];
        debugAria2TestVC.title = itemName;
        NSPanel *panel = [[NSPanel alloc] initWithContentRect:NSMakeRect(0, 0, 1280, 980)
                                                    styleMask:(NSWindowStyleMaskTitled |
                                                               NSWindowStyleMaskClosable |
                                                               NSWindowStyleMaskResizable)
                                                      backing:NSBackingStoreBuffered
                                                        defer:NO];
        panel.contentViewController = debugAria2TestVC;
        panel.delegate = self;
        [self setAria2Panel:panel];
        [panel center];
        [panel makeKeyAndOrderFront:nil];
    }  else if ([itemName isEqualToString:@"终端下载能力调试"]) {
        DebugAria2DownloadVeiwController *debugAria2TestVC = [[DebugAria2DownloadVeiwController alloc] init];
        debugAria2TestVC.title = itemName;
        NSPanel *panel = [[NSPanel alloc] initWithContentRect:NSMakeRect(0, 0, 1280, 980)
                                                    styleMask:(NSWindowStyleMaskTitled |
                                                               NSWindowStyleMaskClosable |
                                                               NSWindowStyleMaskResizable)
                                                      backing:NSBackingStoreBuffered
                                                        defer:NO];
        panel.contentViewController = debugAria2TestVC;
        panel.delegate = self;
        [self setAria2Panel:panel];
        [panel center];
        [panel makeKeyAndOrderFront:nil];
    } else if ([itemName isEqualToString:@"下载按钮调试"]) {
        DebugDownloadButtonTestViewController *debugDownloadButtonTestVC = [[DebugDownloadButtonTestViewController alloc] init];
        debugDownloadButtonTestVC.title = itemName;
        NSPanel *panel = [[NSPanel alloc] initWithContentRect:NSMakeRect(0, 0, 1280, 980)
                                                    styleMask:(NSWindowStyleMaskTitled |
                                                               NSWindowStyleMaskClosable |
                                                               NSWindowStyleMaskResizable)
                                                      backing:NSBackingStoreBuffered
                                                        defer:NO];
        panel.contentViewController = debugDownloadButtonTestVC;
        panel.delegate = self;
        [self setAria2Panel:panel];
        [panel center];
        [panel makeKeyAndOrderFront:nil];
    }
    if (vc) {
        [self pushToVC:vc];
    }
}



#pragma mark - NSWindowDelegate

- (void)windowWillClose:(NSNotification *)notification {
    if (notification.object == self.aria2Panel) {
        // 延迟释放，避免事件循环冲突
        __weak typeof(self) weakSelf = self;
        DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
            weakSelf.aria2Panel = nil;
        });
    }
}

#pragma mark - 自定义商店URL输入弹窗

- (void)showCustomStoreURLInputPanel {
    NSString *defaultUrl = @"https://testmac.yyb.qq.com/";
    NSAlert *alert = [[NSAlert alloc] init];
    alert.messageText = @"请输入自定义商店URL";
    alert.alertStyle = NSAlertStyleInformational;
    NSTextField *inputField = [[NSTextField alloc] initWithFrame:NSMakeRect(0, 0, 300, 24)];
    inputField.stringValue = defaultUrl;
    inputField.editable = YES;
    inputField.allowsEditingTextAttributes = YES;
    alert.accessoryView = inputField;
    [alert addButtonWithTitle:@"确定"];
    [alert addButtonWithTitle:@"取消"];
    [alert beginSheetModalForWindow:self.view.window completionHandler:^(NSModalResponse response) {
        if (response == NSAlertFirstButtonReturn) {
            NSString *customURL = defaultUrl;
            NSString *value = inputField.stringValue;
            if (value.length != 0) {
                customURL = value;
            }
            YYBMacLogInfo(kLogTag, @"用户输入的自定义商店URL: %@", customURL);
            [YYBAlert showAlert:@"设置成功" message:[NSString stringWithFormat:@"商店URL已设置为: %@", customURL]];
            [YYBRoutes routeURLWithModule:yybRouteModuleApp cmd:yybRouteCmdRouteUrl params:
             @{
                yybRouteQueryUrl: customURL,
                yybRouteQueryIsRoot: @(YES)
             }];
        }
    }];
}

-(void)registerEngineDownloadCallback {
//    if ([self enableUseEngine]) {
//        YYBMacLogInfo(@"DebugController", @"enableUseEngine false");
//        return;
//    }
    [[EngineDownloadHelper sharedInstance] registerDownloadCallback:^(NSNumber * _Nonnull progress, EngineDownloadStatus status) {
        NSString* statusTips;
        switch (status) {
            case NOT_DOWNLOADED:
                statusTips = @"下载中";
                break;
            case DOWNLOADED:
                statusTips = @"解压中";
                break;
            case UNZIPPED:
                statusTips = @"可打开";
                break;
            case ENGINE_DOWNLOAD_FAILED:
                statusTips = @"下载失败";
            default:
                statusTips = @"未下载";
                break;
        }
        NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
        formatter.minimumFractionDigits = 2;
        formatter.maximumFractionDigits = 2;
        formatter.numberStyle = NSNumberFormatterDecimalStyle;

        NSString *formatted = [formatter stringFromNumber:progress];
        NSString* text = [NSString stringWithFormat:@"引擎进度：%@ 状态：%@", formatted, statusTips];
        [self.engineDownloadLabel setStringValue:text];
        [self.titleLabel setStringValue:@""];
    }];
}

-(void)registerVmsDownloadCallback {
//    if ([self enableUseVms]) {
//        YYBMacLogInfo(@"DebugController", @"enableUseVms true");
//        return;
//    }
    
    [[EngineDownloadHelper sharedInstance] registerVmsDownloadCallback:^(NSNumber * _Nonnull progress, NSString * _Nonnull downloadStatus, ResHubLocalResStatus reshubStatus) {
        NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
        formatter.minimumFractionDigits = 2;
        formatter.maximumFractionDigits = 2;
        formatter.numberStyle = NSNumberFormatterDecimalStyle;

        NSString *formatted = [formatter stringFromNumber:progress];
        NSString* text = [NSString stringWithFormat:@"vms进度：%@ 状态：%@", formatted, downloadStatus];
        [self.vmsDownloadLabel setStringValue:text];
        [self.titleLabel setStringValue:@""];
    }];
}

- (BOOL)enableUseEngine {
    // 当且仅当engine路径存在且状态记录未UNZIPPED时无需启动下载
    NSString* status = [[YYBMacMMKV sharedInstance] getStringForKey:@"yybMacEngineDownloadStatus" defaultValue:@"NOT_DOWNLOAD"];
    
    // todo: 临时代码
    NSString* tmpUrl = [[YYBMacMMKV sharedInstance] getStringForKey:@"tmpEngineDownloadUrl" defaultValue:@""];
    return [[EngineInfoCenter shareInstance]isEngineExist] && [[EngineDownloadHelper sharedInstance] stringToEngineDownloadStatus:status] == UNZIPPED && [tmpUrl isEqualToString:kTempEngineDownloadUrl];
}

@end
