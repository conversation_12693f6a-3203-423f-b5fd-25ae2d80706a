//
//  DebugDownloadButtonTestViewController.m
//  YYBMacApp
//
//  Created by 企业-iOS on 2025/8/23.
//  下载按钮全场景同步测试，支持手动配置Apk初始数据
//

#import <Cocoa/Cocoa.h>
#import "YYBDownloadButton.h"
#import "YYBApkInfoViewModelPool.h"
#import "YYBApkInfoModel.h"

static NSString *const kBtnTestLogTag = @"DebugDownloadButtonTestViewController";

@interface DebugDownloadButtonTestViewController : NSViewController

// ==== 顶部输入项 ====
@property (nonatomic, strong) NSTextField *pkgNameField;
@property (nonatomic, strong) NSTextField *nameField;
@property (nonatomic, strong) NSTextField *versionNameField;
@property (nonatomic, strong) NSTextField *versionCodeField;
@property (nonatomic, strong) NSTextField *md5Field;
@property (nonatomic, strong) NSTextField *apkUrlField;
@property (nonatomic, strong) NSTextField *iconField;

// ==== 批量添加个数 ====
@property (nonatomic, strong) NSTextField *batchCountField;

// ==== 按钮显示区 ====
@property (nonatomic, strong) NSStackView *buttonArea;

// ==== 日志区 ====
@property (nonatomic, strong) NSTextView *logTextView;

@end

@implementation DebugDownloadButtonTestViewController

- (void)loadView {
    self.view = [[NSView alloc] initWithFrame:NSMakeRect(0, 0, 900, 780)];
    self.view.wantsLayer = YES;
    self.view.layer.backgroundColor = [NSColor windowBackgroundColor].CGColor;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self buildHeaderPanel];
    [self buildConfigInputPanel];
    [self buildBatchPanel];
    [self buildButtonArea];
    [self buildLogPanel];
    [self logLine:@"测试页面已初始化"];
}

#pragma mark ========== 顶部Header ==========

- (void)buildHeaderPanel {
    NSTextField *titleLabel = [self labelWithText:@"下载安装按钮主流程测试"
                                              font:[NSFont boldSystemFontOfSize:20]
                                             color:[NSColor labelColor]];
    titleLabel.alignment = NSTextAlignmentCenter;
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:titleLabel];
    [NSLayoutConstraint activateConstraints:@[
        [titleLabel.topAnchor constraintEqualToAnchor:self.view.topAnchor constant:18],
        [titleLabel.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor]
    ]];
}

#pragma mark ========== 配置输入区 ==========

- (void)buildConfigInputPanel {
    // ========== 构建各输入项 ==========

    // 预设默认值，可据此复写
    _pkgNameField = [self inputFieldWithLabel:@"包名:" defaultText:@"com.tencent.qqmusic"];
    _nameField = [self inputFieldWithLabel:@"名称:" defaultText:@"QQ音乐"];
    _versionNameField = [self inputFieldWithLabel:@"版本名称:" defaultText:@"14.7.5.8"];
    _versionCodeField = [self inputFieldWithLabel:@"版本号:" defaultText:@"6458"];
    _md5Field = [self inputFieldWithLabel:@"MD5:" defaultText:@"B0B3E646093E7371F7813BA1BDB24495"];
    _apkUrlField = [self inputFieldWithLabel:@"下载地址:" defaultText:@"http://dd.myapp.com/sjy.00010/sjy.00004/16891/apk/B0B3E646093E7371F7813BA1BDB24495.apk?fsname=com.tencent.qqmusic_14.7.5.8.apk"];
    _iconField = [self inputFieldWithLabel:@"Icon URL:" defaultText:@"https://pp.myapp.com/ma_icon/0/icon_6259_1755247155/256"];

    NSStackView *configRow1 = [NSStackView stackViewWithViews:@[
        _pkgNameField, _nameField, _versionNameField, _versionCodeField
    ]];
    configRow1.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    configRow1.spacing = 18;
    configRow1.alignment = NSLayoutAttributeCenterY;

    NSStackView *configRow2 = [NSStackView stackViewWithViews:@[
        _md5Field, _apkUrlField, _iconField
    ]];
    configRow2.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    configRow2.spacing = 18;
    configRow2.alignment = NSLayoutAttributeCenterY;

    NSStackView *configStack = [NSStackView stackViewWithViews:@[configRow1, configRow2]];
    configStack.orientation = NSUserInterfaceLayoutOrientationVertical;
    configStack.spacing = 12;
    configStack.edgeInsets = NSEdgeInsetsMake(10, 20, 10, 20);
    configStack.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:configStack];
    [NSLayoutConstraint activateConstraints:@[
        [configStack.topAnchor constraintEqualToAnchor:self.view.topAnchor constant:56],
        [configStack.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:18],
        [configStack.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-18]
    ]];
}

#pragma mark ========== 批量添加、清空操作区 ==========

- (void)buildBatchPanel {
    _batchCountField = [[NSTextField alloc] initWithFrame:NSZeroRect];
    _batchCountField.placeholderString = @"批量添加数量(默认3)";
    _batchCountField.stringValue = @"3";
    _batchCountField.font = [NSFont systemFontOfSize:13];
    [_batchCountField.widthAnchor constraintEqualToConstant:140].active = YES;


    NSButton *addSmallBtnOnlyPkgName = [self buttonWithTitle:@"添加单个按钮(只依赖包名)" tag:100 action:@selector(onAddSingleButtonOnlyPkgName:)];
    NSButton *addSingleBtn = [self buttonWithTitle:@"添加单个按钮" tag:102 action:@selector(onAddSingleButton:)];
    NSButton *addSmallBtn = [self buttonWithTitle:@"添加小按钮" tag:101 action:@selector(onAddSmallButton:)];
    NSButton *removeAllBtn = [self buttonWithTitle:@"全部移除" tag:103 action:@selector(onRemoveAllButtons:)];
    
    NSStackView *opStack = [NSStackView stackViewWithViews:@[
        _batchCountField, addSmallBtnOnlyPkgName, addSingleBtn, addSmallBtn, removeAllBtn
    ]];
    opStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    opStack.spacing = 22;
    opStack.edgeInsets = NSEdgeInsetsMake(10, 20, 10, 20);
    opStack.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:opStack];
    [NSLayoutConstraint activateConstraints:@[
        [opStack.topAnchor constraintEqualToAnchor:self.view.topAnchor constant:142],
        [opStack.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:18],
        [opStack.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-18],
        [opStack.heightAnchor constraintEqualToConstant:44]
    ]];
}

#pragma mark ========== 按钮生成区 ==========

- (void)buildButtonArea {
    _buttonArea = [[NSStackView alloc] initWithFrame:NSZeroRect];
    _buttonArea.orientation = NSUserInterfaceLayoutOrientationVertical;
    _buttonArea.spacing = 18;
    _buttonArea.edgeInsets = NSEdgeInsetsMake(18, 20, 18, 20);
    _buttonArea.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:_buttonArea];
    [NSLayoutConstraint activateConstraints:@[
        [_buttonArea.topAnchor constraintEqualToAnchor:self.view.topAnchor constant:190],
        [_buttonArea.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:44],
        [_buttonArea.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-44],
        [_buttonArea.heightAnchor constraintGreaterThanOrEqualToConstant:120]
    ]];
}

#pragma mark ========== 日志区 ==========

- (void)buildLogPanel {
    NSTextField *logTitleLabel = [self labelWithText:@"日志" font:[NSFont boldSystemFontOfSize:15] color:[NSColor labelColor]];
    logTitleLabel.alignment = NSTextAlignmentLeft;
    
    _logTextView = [[NSTextView alloc] initWithFrame:NSZeroRect];
    _logTextView.editable = NO;
    _logTextView.font = [NSFont userFixedPitchFontOfSize:12];
    _logTextView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    _logTextView.backgroundColor = [NSColor colorWithWhite:0.97 alpha:1.0];

    NSButton *clearLogBtn = [self buttonWithTitle:@"清空日志" tag:201 action:@selector(onClearLog:)];
    
    NSStackView *logOpStack = [NSStackView stackViewWithViews:@[logTitleLabel, clearLogBtn]];
    logOpStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    logOpStack.spacing = 14;
    logOpStack.alignment = NSLayoutAttributeCenterY;

    NSScrollView *logScroll = [[NSScrollView alloc] initWithFrame:NSZeroRect];
    logScroll.documentView = _logTextView;
    logScroll.hasVerticalScroller = YES;
    logScroll.autohidesScrollers = YES;
    logScroll.borderType = NSBezelBorder;
    logScroll.translatesAutoresizingMaskIntoConstraints = NO;
    [logScroll.heightAnchor constraintEqualToConstant:160].active = YES;

    NSStackView *logStack = [NSStackView stackViewWithViews:@[logOpStack, logScroll]];
    logStack.orientation = NSUserInterfaceLayoutOrientationVertical;
    logStack.spacing = 8;
    logStack.edgeInsets = NSEdgeInsetsMake(8, 8, 8, 8);
    logStack.translatesAutoresizingMaskIntoConstraints = NO;

    [self.view addSubview:logStack];
    [NSLayoutConstraint activateConstraints:@[
        [logStack.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:18],
        [logStack.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-18],
        [logStack.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor constant:-24],
        [logStack.heightAnchor constraintGreaterThanOrEqualToConstant:160]
    ]];
}

#pragma mark ========== 工具：输入字段和标签 ==========

// 带label的输入框横向组合
- (NSTextField *)inputFieldWithLabel:(NSString *)label defaultText:(NSString *)defValue {
    NSView *container = [[NSView alloc] initWithFrame:NSZeroRect];
    NSTextField *lbl = [self labelWithText:label font:[NSFont systemFontOfSize:13] color:[NSColor controlTextColor]];
    lbl.alignment = NSTextAlignmentRight;
    [lbl.widthAnchor constraintEqualToConstant:66].active = YES;
    NSTextField *field = [[NSTextField alloc] initWithFrame:NSZeroRect];
    field.stringValue = defValue ?: @"";
    field.font = [NSFont systemFontOfSize:13];
    [field.widthAnchor constraintEqualToConstant:230].active = YES;
    field.translatesAutoresizingMaskIntoConstraints = NO;
    lbl.translatesAutoresizingMaskIntoConstraints = NO;

    NSStackView *stack = [NSStackView stackViewWithViews:@[lbl, field]];
    stack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    stack.spacing = 6;
    stack.alignment = NSLayoutAttributeCenterY;
    stack.translatesAutoresizingMaskIntoConstraints = NO;
    [container addSubview:stack];
    [NSLayoutConstraint activateConstraints:@[
        [stack.topAnchor constraintEqualToAnchor:container.topAnchor constant:0],
        [stack.bottomAnchor constraintEqualToAnchor:container.bottomAnchor constant:0],
        [stack.leadingAnchor constraintEqualToAnchor:container.leadingAnchor constant:0],
        [stack.trailingAnchor constraintEqualToAnchor:container.trailingAnchor constant:0]
    ]];
    [container setTranslatesAutoresizingMaskIntoConstraints:NO];
    container.frame = NSMakeRect(0,0,320,28);
    return field;
}

- (NSTextField *)labelWithText:(NSString *)text font:(NSFont *)font color:(NSColor *)color {
    NSTextField *label = [[NSTextField alloc] initWithFrame:NSZeroRect];
    label.stringValue = text ?: @"";
    label.font = font ?: [NSFont systemFontOfSize:11];
    label.textColor = color ?: [NSColor controlTextColor];
    label.editable = NO;
    label.bezeled = NO;
    label.drawsBackground = NO;
    label.selectable = NO;
    label.lineBreakMode = NSLineBreakByTruncatingTail;
    label.translatesAutoresizingMaskIntoConstraints = NO;
    return label;
}

- (NSButton *)buttonWithTitle:(NSString *)title tag:(NSInteger)tag action:(SEL)sel {
    NSButton *btn = [NSButton buttonWithTitle:title target:self action:sel];
    btn.tag = tag;
    btn.bezelStyle = NSBezelStyleRounded;
    [btn.widthAnchor constraintGreaterThanOrEqualToConstant:98].active = YES;
    btn.translatesAutoresizingMaskIntoConstraints = NO;
    return btn;
}

#pragma mark ========== 操作事件 ==========

// 单个按钮(pkgName)
- (void)onAddSingleButtonOnlyPkgName:(id)sender {
    [self addButtonWithSuffix:nil onlyPkgName:YES  size:CGSizeMake(220, 44)];
}

// 单个按钮(MODEL)
- (void)onAddSingleButton:(id)sender {
    [self addButtonWithSuffix:nil onlyPkgName:NO size:CGSizeMake(220, 44)];
}

- (void)onAddSmallButton:(id)sender {
    [self addButtonWithSuffix:nil onlyPkgName:NO size:CGSizeMake(70, 32)];
}

- (void)onRemoveAllButtons:(id)sender {
    for (NSView *sub in _buttonArea.arrangedSubviews) {
        [_buttonArea removeArrangedSubview:sub];
        [sub removeFromSuperview];
    }
    [self logLine:@"所有按钮已移除"];
}

- (void)onClearLog:(id)sender {
    _logTextView.string = @"";
    [self logLine:@"日志已清空"];
}

#pragma mark ========== 动态生成按钮方案/日志 ==========

// 新建一个按钮并显示
- (void)addButtonWithSuffix:(nullable NSString *)suffix
                onlyPkgName:(BOOL)onlyPkgName
                       size:(CGSize)buttonSize {
    YYBApkInfoModel *model = [[YYBApkInfoModel alloc] init];
    model.pkgName = [_pkgNameField.stringValue stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    model.name = [_nameField.stringValue stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    model.versionName = [_versionNameField.stringValue stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    model.versionCode = [_versionCodeField.stringValue stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    model.md5 = [_md5Field.stringValue stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    model.apkUrl = [_apkUrlField.stringValue stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    model.icon = [_iconField.stringValue stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];

    NSStackView *row = [[NSStackView alloc] initWithFrame:NSZeroRect];
    row.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    row.spacing = 16;
    row.edgeInsets = NSEdgeInsetsMake(6, 4, 6, 4);

    NSString *display = model.pkgName;
    if (suffix.length > 0) { display = [NSString stringWithFormat:@"%@ [%@]", model.pkgName, suffix]; }
    NSTextField *title = [self labelWithText:[NSString stringWithFormat:@"包名: %@", display]
                                        font:[NSFont systemFontOfSize:14]
                                       color:[NSColor secondaryLabelColor]];
    title.alignment = NSTextAlignmentLeft;
    title.maximumNumberOfLines = 2;

    YYBDownloadButton *btn = [[YYBDownloadButton alloc] initWithFrame:NSMakeRect(0, 0, 220, 44)];
    [btn updateButtonSize:buttonSize];
    if (onlyPkgName) {
        [btn setPkgName:model.pkgName];
    } else {
        [btn setApkInfoModel:model];;
    }

    [row addArrangedSubview:title];
    [row addArrangedSubview:btn];
    [_buttonArea addArrangedSubview:row];
    [self logLine:[NSString stringWithFormat:@"已添加按钮-包名:%@，组:%@", model.pkgName, suffix ?: @"单"]];
}

#pragma mark ========== 日志方法 ==========

- (void)logLine:(NSString *)line {
    NSString *old = _logTextView.string ?: @"";
    NSString *logString = [old stringByAppendingFormat:@"%@\n", line];
    _logTextView.string = logString;
    [_logTextView scrollRangeToVisible:NSMakeRange(_logTextView.string.length, 0)];
    NSLog(@"[%@] %@", kBtnTestLogTag, line);
}

@end
