//
//  YYBMacApp
//
//  Created by 凌刚 on 2025/5/29.
//

#import "DebugFunctionMenuViewController.h"

@interface DebugFunctionMenuViewController () <NSTableViewDelegate, NSTableViewDataSource>

@property (nonatomic, strong) NSTableView *tableView;
@property (nonatomic, strong) NSArray *items;

@end

@implementation DebugFunctionMenuViewController

@synthesize navigationController;

- (void)viewDidLoad {
    [super viewDidLoad];
    
    _items = @[@"通知调试", @"下载服务调试", @"终端下载能力调试", @"下载按钮调试", @"性能监控"];
    
    // 创建滚动视图容器
    NSScrollView *scrollView = [[NSScrollView alloc] initWithFrame:self.view.bounds];
    scrollView.hasVerticalScroller = YES;
    scrollView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    
    // 创建表格视图
    _tableView = [[NSTableView alloc] init];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    _tableView.headerView = nil; // 隐藏表头
    
    // 创建自适应列
    NSTableColumn *column = [[NSTableColumn alloc] initWithIdentifier:@"FuncDebugColumn"];
    column.resizingMask = NSTableColumnAutoresizingMask;
    [_tableView addTableColumn:column];
    
    scrollView.documentView = _tableView;
    [self.view addSubview:scrollView];
    
    // 自动调整表格大小
    _tableView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    [_tableView sizeLastColumnToFit];
}

- (void)tableViewSelectionDidChange:(NSNotification *)notification {
    NSInteger row = _tableView.selectedRow;
    if (row >= 0 && row < _items.count) {
        [self.delegate debugFunctionMenuDidSelect:_items[row]];
    }
}

#pragma mark - NSTableViewDataSource
- (NSInteger)numberOfRowsInTableView:(NSTableView *)tableView {
    return _items.count;
}

- (id)tableView:(NSTableView *)tableView objectValueForTableColumn:(NSTableColumn *)tableColumn row:(NSInteger)row {
    return _items[row];
}

- (CGFloat)tableView:(NSTableView *)tableView heightOfRow:(NSInteger)row {
    return 45;
}


@end
