//
//  YYBDownloadModule.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/8.
//

#import "YYBDownloaderModule.h"
#import "YYBJSAPIMacros.h"
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "YYBLibAria2ServiceFacade.h"

@implementation YYBDownloaderModule

YYB_EXPORT_MODULE(downloader)

/**
 * @description 获取下载根目录路径
 * @param 无
 * @return {String} 下载根目录的完整路径
 */
YYB_EXPORT_METHOD(DownloadRootPath) {
    NSString *path = [[YYBLibAria2ServiceFacade sharedService] sharedDownloadDir];
    YYB_SEND_SUCCESS(path);
}

/**
 * @description 获取APK下载目录名称
 * @param 无
 * @return {String} APK下载目录名称
 */
YYB_EXPORT_METHOD(ApkDirName) {
    YYB_SEND_SUCCESS([[YYBLibAria2ServiceFacade sharedService] apkDirName]);
}

@end
