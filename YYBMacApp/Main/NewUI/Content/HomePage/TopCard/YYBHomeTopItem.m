//
//  YYBHomeTopItem.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBHomeTopItem.h"
#import "YYBTopView.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "Masonry.h"
#import "YYBComponent.h"
#import "YYBHomeTopItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kTopItemLogTag = @"YYBHomeTopItem";

@interface YYBHomeTopItem ()
@property (nonatomic, strong) YYBTopView *topView;
@end

@implementation YYBHomeTopItem

- (void)viewDidLoad {
    [super viewDidLoad];
    self.topView = [[YYBTopView alloc] initWithFrame:NSZeroRect];
    [self.view addSubview:self.topView];
    [self.topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    YYBMacLogInfo(kTopItemLogTag, @"首页TOP区块Item初始化完成");
}

/// Top区块业务渲染
- (void)configureWithComponent:(YYBComponent *)component atRowIndex:(NSInteger)rowIdx atItemIndex:(NSInteger)itemIdx {
    [super configureWithComponent:component atRowIndex:rowIdx atItemIndex:itemIdx];
    [self.topView configureWithComponent:component];
    YYBMacLogInfo(kTopItemLogTag, @"TOP区块渲染: rowIdx=%ld itemIdx=%ld cardId=%@", rowIdx, itemIdx, component.cardId);
}

- (void)configureWithComponentGroup:(NSArray<YYBComponent *> *)componentGroup atRowIndex:(NSInteger)rowIdx {
    [super configureWithComponentGroup:componentGroup atRowIndex:rowIdx];
}


@end
