//
//  YYBMacBlurView.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBMacBlurView.h"
#import <AppKit/AppKit.h>

@interface YYBMacBlurView ()

@property (nonatomic, strong) NSImageView *blurImgView;

@end

@implementation YYBMacBlurView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        self.material = NSVisualEffectMaterialContentBackground;
        self.blendingMode = NSVisualEffectBlendingModeWithinWindow;
        self.state = NSVisualEffectStateActive;

        self.blurImgView = [[NSImageView alloc] initWithFrame:self.bounds];
        self.blurImgView.imageScaling = NSImageScaleAxesIndependently;
        self.blurImgView.alphaValue = 0.65;

        [self addSubview:self.blurImgView positioned:NSWindowBelow relativeTo:nil];

        self.wantsLayer = YES;
        self.layer.masksToBounds = YES;
    }
    return self;
}

- (void)setImage:(NSImage *)image {
    self.blurImgView.image = image;
}

@end
