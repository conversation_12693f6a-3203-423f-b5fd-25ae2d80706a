//
//  YYBRankComponentCardCollectionView.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBRankComponentCardCollectionView.h"
#import "YYBRankItemsCardCell.h"
#import "YYBComponent.h"
#import "YYBBaseCardData.h"
#import "Masonry.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kCardLogTag = @"YYBRankComponentCardCollectionView";

// MARK: 自定义Layout
@interface YYBRankComponentCardLayout : NSCollectionViewLayout

@property (nonatomic, assign) NSUInteger itemCount;
@property (nonatomic, assign) NSSize collectionViewSize;
@property (nonatomic, strong) NSArray<NSValue *> *itemFrames;

@end

@implementation YYBRankComponentCardLayout

// 不同场景可以通过参数化这种Layout
- (void)prepareLayout {
    [super prepareLayout];
    NSMutableArray *frames = [NSMutableArray array];

    // 1. 获取 collectionView 父视图的当前尺寸，如数值太小则用兜底默认值防止第一次拉伸错误
    NSSize cvs = self.collectionView.frame.size;
    if (cvs.width < 10 || cvs.height < 10) cvs = NSMakeSize(800, 400); // 默认最小尺寸做防守

    // 2. 业务层约定：横向和纵向间隙分别为24；3大区等宽分布
    CGFloat interBlockSpacing = 24;         // 横向块间距
    CGFloat interSmallCardSpacing = 24;     // 两小卡间上下间距

    NSUInteger totalBlocks = 3;  // 横向分区，以视觉稿为3等分块
    CGFloat totalHorizontalSpacing = (totalBlocks - 1) * interBlockSpacing; // 左/中/右间的所有空隙（2边贴边了）

    CGFloat blockWidth = (cvs.width - totalHorizontalSpacing) / totalBlocks; // 每块理论最大宽度，3等分
    CGFloat blockHeight = cvs.height - 2 * interBlockSpacing;                // 上下分别有间隔

    // 3. 定义每块的起点X和宽度
    CGFloat leftBlockX = 0;                                                 // 左大卡起点
    CGFloat centerBlockX = leftBlockX + blockWidth + interBlockSpacing;      // 中2小卡块起点
    CGFloat rightBlockX = centerBlockX + blockWidth + interBlockSpacing;     // 右大卡起点

    // 4. 中间小卡的高度与Y
    CGFloat totalVerticalSpacing = interSmallCardSpacing;        // 中间2小卡，上下间一个分割线
    CGFloat smallCardHeight = (blockHeight - totalVerticalSpacing) / 2;      // 等高分割
    CGFloat smallTopCardY = interBlockSpacing + smallCardHeight + interSmallCardSpacing/2;
    CGFloat smallBottomCardY = interBlockSpacing;

    // 5. 卡片布局
    for (NSUInteger i = 0; i < self.itemCount; i++) {
        NSRect frame = NSZeroRect;
        if (i == 0) {
            // 左侧大卡（如：热门游戏）
            frame = NSMakeRect(leftBlockX, interBlockSpacing, blockWidth, blockHeight);
        } else if (i == 1) {
            // 中间 上方小卡
            frame = NSMakeRect(centerBlockX,
                               smallTopCardY, // Y为中块的上半部分
                               blockWidth, smallCardHeight);
        } else if (i == 2) {
            // 中间 下方小卡
            frame = NSMakeRect(centerBlockX,
                               smallBottomCardY, // Y为中块的下半部分
                               blockWidth, smallCardHeight);
        } else if (i == 3) {
            // 右侧大卡（如：推荐应用等）
            frame = NSMakeRect(rightBlockX, interBlockSpacing, blockWidth, blockHeight);
        } else {
            // 超额数据只和左大卡同位置（自定义业务处理或隐藏）
            frame = NSMakeRect(leftBlockX, interBlockSpacing, blockWidth, blockHeight);
        }

        // 6. 防守：如任意卡宽高超界则裁切
        if (NSMaxX(frame) > cvs.width) frame.size.width = cvs.width - frame.origin.x - interBlockSpacing;
        if (NSMaxY(frame) > cvs.height) frame.size.height = cvs.height - frame.origin.y - interBlockSpacing;

        [frames addObject:[NSValue valueWithRect:frame]];
    }

    self.itemFrames = frames;
    self.collectionViewSize = cvs;

    YYBMacLogInfo(kCardLogTag, @"卡片布局刷新: count=%ld, size=%@", self.itemCount, NSStringFromSize(cvs));
    for (NSUInteger i = 0; i < frames.count; i++) {
        NSRect f = [frames[i] rectValue];
        YYBMacLogInfo(kCardLogTag, @"卡%lu frame: %@ %@", (unsigned long)i, NSStringFromRect(f),
                      (NSMaxX(f) > cvs.width || NSMaxY(f) > cvs.height) ? @"[超父视图]" : @"[正常]");
    }
}

- (CGSize)collectionViewContentSize {
    // 内容尺寸严格不超过父视图
    return CGSizeMake(self.collectionViewSize.width, self.collectionViewSize.height);
}

- (NSArray<NSCollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(NSRect)rect {
    NSMutableArray *attributesArr = [NSMutableArray array];
    for (NSUInteger i = 0; i < self.itemFrames.count; i++) {
        NSRect frame = [self.itemFrames[i] rectValue];
        if (NSIntersectsRect(frame, rect)) {
            NSCollectionViewLayoutAttributes *attr = [NSCollectionViewLayoutAttributes layoutAttributesForItemWithIndexPath:[NSIndexPath indexPathForItem:i inSection:0]];
            attr.frame = frame;
            [attributesArr addObject:attr];
        }
    }
    return attributesArr;
}
- (NSCollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.item < self.itemFrames.count) {
        NSRect frame = [self.itemFrames[indexPath.item] rectValue];
        NSCollectionViewLayoutAttributes *attr = [NSCollectionViewLayoutAttributes layoutAttributesForItemWithIndexPath:indexPath];
        attr.frame = frame;
        return attr;
    }
    return nil;
}

@end


@interface YYBRankComponentCardCollectionView () <NSCollectionViewDataSource, NSCollectionViewDelegate>

@property (nonatomic, strong) NSArray<YYBComponent *> *components;

@end

@implementation YYBRankComponentCardCollectionView

- (instancetype)initWithFrame:(NSRect)frameRect {
    YYBRankComponentCardLayout *layout = [[YYBRankComponentCardLayout alloc] init];
    self = [super initWithFrame:frameRect];
    if (self) {
        self.collectionViewLayout = layout;
        self.backgroundColors = @[[NSColor clearColor]];
        self.dataSource = self;
        [self registerClass:[YYBRankItemsCardCell class] forItemWithIdentifier:@"YYBRankItemsCardCell"];
    }
    return self;
}

- (void)reloadWithComponents:(NSArray<YYBComponent *> *)components {
    self.components = components;
    YYBMacLogInfo(kCardLogTag, @"刷新榜单卡组：数量=%ld", components.count);
    // 更新layout数据，重载
    YYBRankComponentCardLayout *layout = (YYBRankComponentCardLayout *)self.collectionViewLayout;
    layout.itemCount = components.count;
    [layout invalidateLayout];
    [self reloadData];
}

// MARK: - NSCollectionViewDataSource
- (NSInteger)numberOfSectionsInCollectionView:(NSCollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(NSCollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.components.count;
}

- (NSCollectionViewItem *)collectionView:(NSCollectionView *)collectionView itemForRepresentedObjectAtIndexPath:(NSIndexPath *)indexPath {
    YYBRankItemsCardCell *itemCell = [collectionView makeItemWithIdentifier:@"YYBRankItemsCardCell" forIndexPath:indexPath];
    YYBComponent *data = self.components[indexPath.item];
    // 判断位置/类型，布局样式
    YYBRankItemsCardLayoutType layoutType;
    if (indexPath.item == 0 || indexPath.item == 3) {
        layoutType = YYBRankItemsCardLayoutTypeHorizontalLarge; // 大样式
    } else {
        layoutType = YYBRankItemsCardLayoutTypeVerticalSmall;   // 小卡
    }
    [itemCell configureWithData:data.data layoutType:layoutType];
    YYBMacLogInfo(kCardLogTag, @"渲染榜单卡 idx=%ld cardId=%@ layoutType=%lu", indexPath.item, data.cardId, (unsigned long)layoutType);
    return itemCell;
}

@end
