//
//  YYBRankAppListCollectionView.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBRankAppListCollectionView.h"
#import "YYBRankAppCell.h"
#import "YYBAppItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kAppListLog = @"YYBRankAppListCV";

@interface YYBRankAppListCollectionView () <NSCollectionViewDataSource, NSCollectionViewDelegate>

@property (nonatomic, copy) NSArray<YYBAppItem *> *apps;

@end

@implementation YYBRankAppListCollectionView
- (instancetype)initWithAppItems:(NSArray<YYBAppItem *> *)appItems {
    self = [super initWithFrame:NSZeroRect];
    if (self) {
        NSCollectionViewFlowLayout *layout = [[NSCollectionViewFlowLayout alloc] init];
        layout.scrollDirection = NSCollectionViewScrollDirectionVertical;
        layout.itemSize = NSMakeSize(190, 36);
        layout.minimumLineSpacing = 2;
        self.collectionViewLayout = layout;

        self.dataSource = self;
        self.delegate = self;
        self.apps = appItems ?: @[];
        self.backgroundColors = @[[NSColor clearColor]];

        [self registerClass:[YYBRankAppCell class] forItemWithIdentifier:@"YYBRankAppCell"];
    }
    return self;
}

- (void)reloadWithAppItems:(NSArray<YYBAppItem *> *)appItems {
    self.apps = appItems ?: @[];
    [self reloadData];
}

// MARK: CollectionView DataSource
- (NSInteger)numberOfSectionsInCollectionView:(NSCollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(NSCollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.apps.count;
}

- (NSCollectionViewItem *)collectionView:(NSCollectionView *)collectionView itemForRepresentedObjectAtIndexPath:(NSIndexPath *)indexPath {
    YYBRankAppCell *cell = [collectionView makeItemWithIdentifier:@"YYBRankAppCell" forIndexPath:indexPath];
    [cell configureWithAppItem:self.apps[indexPath.item]];
    YYBMacLogInfo(kAppListLog, @"榜单应用 cell[%ld] app=%@", indexPath.item, self.apps[indexPath.item].name);
    return cell;
}

@end
