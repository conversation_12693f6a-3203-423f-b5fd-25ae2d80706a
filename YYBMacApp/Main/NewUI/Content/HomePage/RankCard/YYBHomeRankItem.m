//
//  YYBHomeRankItem.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBHomeRankItem.h"
#import "YYBHomeRankItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBRankComponentCardCollectionView.h"
#import "Masonry.h"
#import "YYBComponent.h"

static NSString *const kRankItemLogTag = @"YYBHomeRankItem";

@interface YYBHomeRankItem ()
@property (nonatomic, strong) YYBRankComponentCardCollectionView *rankView;
@end

@implementation YYBHomeRankItem

- (void)viewDidLoad {
    [super viewDidLoad];
    self.rankView = [[YYBRankComponentCardCollectionView alloc] initWithFrame:NSZeroRect];
    [self.view addSubview:self.rankView];
    [self.rankView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    YYBMacLogInfo(kRankItemLogTag, @"榜单区块Item初始化完成");
}

- (void)viewDidLayout {
    [super viewDidLayout];
    if (self.rankView.superview && self.currentComponentGroup.count > 0 && NSWidth(self.view.bounds) > 0) {
        [self.rankView reloadWithComponents:self.currentComponentGroup];
        YYBMacLogInfo(kRankItemLogTag, @"viewDidLayout中触发数据渲染，frame:%@", NSStringFromRect(self.rankView.frame));
    }
}

- (void)configureWithComponent:(YYBComponent *)component atRowIndex:(NSInteger)rowIdx atItemIndex:(NSInteger)itemIdx {
    [super configureWithComponent:component atRowIndex:rowIdx atItemIndex:itemIdx];
    [self configureWithComponentGroup:@[component] atRowIndex:itemIdx];
}

/// 榜单区业务渲染-组
- (void)configureWithComponentGroup:(NSArray<YYBComponent *> *)componentGroup atRowIndex:(NSInteger)rowIdx {
    [super configureWithComponentGroup:componentGroup atRowIndex:rowIdx];
    [self.rankView reloadWithComponents:componentGroup];
    
    YYBMacLogInfo(kRankItemLogTag, @"榜单区块渲染: rowIdx=%ld first-cardId=%@ last-cardId=%@",
                  rowIdx,
                  componentGroup.firstObject.cardId ?: @"",
                  componentGroup.lastObject.cardId ?: @"");
}

@end
