//
//  YYBRankAppCell.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBRankAppCell.h"
#import "Masonry.h"
#import "SDWebImage.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBAppItem.h"

static NSString *const kAppCellLog = @"YYBRankAppCell";

@interface YYBRankAppCell ()

@property (nonatomic, strong) NSImageView *iconView;
@property (nonatomic, strong) NSTextField *nameLabel;
@property (nonatomic, strong) NSTextField *cateLabel;

@end

@implementation YYBRankAppCell

- (void)loadView {
    self.view = [[NSView alloc] init];
}

// 应用单元格渲染
- (void)configureWithAppItem:(YYBAppItem *)appItem {
    // 移除旧view
    [self.view.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];

    // icon
    self.iconView = [[NSImageView alloc] init];
    self.iconView.wantsLayer = YES;
    self.iconView.layer.cornerRadius = 7;
    self.iconView.layer.masksToBounds = YES;
    self.iconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    [self.view addSubview:self.iconView];

    // 名称
    self.nameLabel = [[NSTextField alloc] init];
    self.nameLabel.editable = NO;
    self.nameLabel.bezeled = NO;
    self.nameLabel.backgroundColor = [NSColor clearColor];
    self.nameLabel.textColor = [NSColor whiteColor];
    self.nameLabel.font = [NSFont systemFontOfSize:13 weight:NSFontWeightMedium];
    self.nameLabel.stringValue = appItem.name ?: @"";
    [self.view addSubview:self.nameLabel];

    // 分类
    self.cateLabel = [[NSTextField alloc] init];
    self.cateLabel.editable = NO;
    self.cateLabel.bezeled = NO;
    self.cateLabel.backgroundColor = [NSColor clearColor];
    self.cateLabel.textColor = [NSColor colorWithWhite:1 alpha:0.60];
    self.cateLabel.font = [NSFont systemFontOfSize:11];
    self.cateLabel.stringValue = appItem.cateName ?: @"";
    [self.view addSubview:self.cateLabel];

    // Masonry布局
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.centerY.equalTo(self.view);
        make.size.mas_equalTo(NSMakeSize(26, 26));
    }];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconView.mas_right).offset(8);
        make.centerY.equalTo(self.iconView.mas_centerY);
        make.width.mas_lessThanOrEqualTo(80);
    }];
    [self.cateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel.mas_right).offset(10);
        make.centerY.equalTo(self.iconView.mas_centerY);
        make.right.lessThanOrEqualTo(self.view).offset(-8);
    }];

    // SDWebImage图片加载
    [self.iconView sd_setImageWithURL:[NSURL URLWithString:appItem.icon ?: @""] placeholderImage:nil options:SDWebImageRetryFailed context:nil];

    YYBMacLogInfo(kAppCellLog, @"应用cell渲染 name=%@, cate=%@", appItem.name, appItem.cateName);
}

@end
