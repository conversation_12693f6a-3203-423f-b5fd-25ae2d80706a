//
//  YYBRankItemsCardCell.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBRankItemsCardCell.h"
#import "YYBRankAppListCollectionView.h"
#import "YYBAppItem.h"
#import "SDWebImage.h"
#import "Masonry.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBMacBlurView.h"

static NSString *const kRankItemsCardTag = @"YYBRankItemsCardCell";

@interface YYBRankItemsCardCell ()

@property (nonatomic, strong) NSImageView *bannerView;
@property (nonatomic, strong) YYBMacBlurView *bannerBlurView;
@property (nonatomic, strong) NSTextField *titleLabel;
@property (nonatomic, strong) YYBRankAppListCollectionView *appListCV;
@property (nonatomic, strong) YYBBaseCardData *itemData;

@end

@implementation YYBRankItemsCardCell

- (void)loadView {
    self.view = [[NSView alloc] init];
}

// 卡片内容渲染（横竖两种）
- (void)configureWithData:(YYBBaseCardData *)data layoutType:(YYBRankItemsCardLayoutType)layoutType {
    self.itemData = data;
    YYBMacLogInfo(kRankItemsCardTag, @"渲染榜单卡Title=%@ layout=%lu", data.name, (unsigned long)layoutType);

    // 清除旧视图
    [self.view.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    self.view.wantsLayer = YES;
    self.view.layer.cornerRadius = 16;
    self.view.layer.backgroundColor = [[NSColor colorWithWhite:0.12 alpha:0.80] CGColor];

    // 横向大卡/竖向小卡不同
    if (layoutType == YYBRankItemsCardLayoutTypeHorizontalLarge) {
        self.view.layer.backgroundColor = [[NSColor redColor] CGColor];
//        // 1. Banner图
//        self.bannerView = [[NSImageView alloc] init];
//        self.bannerView.wantsLayer = YES;
//        self.bannerView.layer.cornerRadius = 14;
//        self.bannerView.layer.masksToBounds = YES;
//        self.bannerView.imageScaling = NSImageScaleAxesIndependently;
//        [self.view addSubview:self.bannerView];
//
//        // 2. 背景模糊（YYBMacBlurView: NSVisualEffectView包装）
//        self.bannerBlurView = [[YYBMacBlurView alloc] initWithFrame:NSZeroRect];
//        [self.view addSubview:self.bannerBlurView positioned:NSWindowBelow relativeTo:self.bannerView];
//
//        // 3. 标题
//        self.titleLabel = [[NSTextField alloc] init];
//        self.titleLabel.editable = NO;
//        self.titleLabel.bezeled = NO;
//        self.titleLabel.backgroundColor = [NSColor clearColor];
//        self.titleLabel.textColor = [NSColor whiteColor];
//        self.titleLabel.font = [NSFont boldSystemFontOfSize:19];
//        self.titleLabel.stringValue = data.name ?: @"";
//        [self.view addSubview:self.titleLabel];
//
//        // 4. 应用榜
//        self.appListCV = [[YYBRankAppListCollectionView alloc] initWithAppItems:data.itemData];
//        [self.view addSubview:self.appListCV];
//
//        // Masonry布局
//        [self.bannerView mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.top.left.right.equalTo(self.view).insets(NSEdgeInsetsMake(16, 16, 0, 16));
//            make.height.mas_equalTo(80);
//        }];
//        [self.bannerBlurView mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.right.top.equalTo(self.bannerView);
//            make.height.mas_equalTo(48); // 只模糊上半部分
//        }];
//        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.equalTo(self.bannerView).offset(16);
//            make.top.equalTo(self.bannerView.mas_top).offset(12);
//            make.right.lessThanOrEqualTo(self.bannerView).offset(-16);
//            make.height.mas_equalTo(24);
//        }];
//        [self.appListCV mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.right.equalTo(self.bannerView);
//            make.top.equalTo(self.bannerView.mas_bottom).offset(8);
//            make.bottom.equalTo(self.view).offset(-10);
//        }];
//
//        // Banner图片填充&同步到BlurView内容
//        NSString *bannerImgURL = data.itemData.firstObject.horizontalCover.firstObject ?: data.itemData.firstObject.icon;
//        [self.bannerView sd_setImageWithURL:[NSURL URLWithString:bannerImgURL] placeholderImage:nil options:SDWebImageRetryFailed completed:^(NSImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
//            if (image) {
//                // 通知BlurView刷新背景
//                [self.bannerBlurView setImage:image];
//                YYBMacLogInfo(kRankItemsCardTag, @"Banner及模糊渲染成功 [%@]", data.name);
//            } else {
//                YYBMacLogWarn(kRankItemsCardTag, @"Banner图加载失败 [%@] url=%@", data.name, bannerImgURL);
//            }
//        }];
//
//        // 同步刷新应用榜
//        [self.appListCV reloadWithAppItems:data.itemData];
    } else {
        self.view.layer.backgroundColor = [[NSColor yellowColor] CGColor];
//        // 竖小卡（无banner）
//        self.titleLabel = [[NSTextField alloc] init];
//        self.titleLabel.editable = NO;
//        self.titleLabel.bezeled = NO;
//        self.titleLabel.backgroundColor = [NSColor clearColor];
//        self.titleLabel.textColor = [NSColor whiteColor];
//        self.titleLabel.font = [NSFont boldSystemFontOfSize:15];
//        self.titleLabel.stringValue = data.name ?: @"";
//        [self.view addSubview:self.titleLabel];
//
//        self.appListCV = [[YYBRankAppListCollectionView alloc] initWithAppItems:data.itemData];
//        [self.view addSubview:self.appListCV];
//
//        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.top.equalTo(self.view).offset(18);
//            make.right.lessThanOrEqualTo(self.view).offset(-18);
//            make.height.mas_equalTo(20);
//        }];
//        [self.appListCV mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.right.equalTo(self.titleLabel);
//            make.top.equalTo(self.titleLabel.mas_bottom).offset(8);
//            make.bottom.equalTo(self.view).offset(-10);
//        }];
//        [self.appListCV reloadWithAppItems:data.itemData];
    }
}

@end
