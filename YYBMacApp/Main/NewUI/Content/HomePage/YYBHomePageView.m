//
//  YYBHomePageView.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/13.
//

#import "YYBHomePageView.h"
#import "Masonry.h"
#import "YYBComponent.h"
#import <YYBMacFusionSDK/YYBMacHttp.h>
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBHomeResponse.h"
#import "YYModel.h"
#import "YYBHomeRowCell.h"
#import "YYBHomeTopItem.h"
#import "YYBHomeRankItem.h"

static NSString *const kHomeLogTag = @"YYBHomePageView";

#define kHomePageViewMaxWidth 1236          // 容器最大宽度

@interface YYBHomePageView () <NSCollectionViewDataSource, NSCollectionViewDelegate, NSCollectionViewDelegateFlowLayout>

@property (nonatomic, strong) NSCollectionView *homeCollectionView;
@property (nonatomic, strong) NSArray<YYBComponent *> *componentsData;
// 业务自定义分组结果，二维数组: 每个元素为一行卡片
@property (nonatomic, strong) NSArray<NSArray<YYBComponent *> *> *rowComponents;
@property (nonatomic, strong) NSScrollView *scrollView;

@end

@implementation YYBHomePageView

#pragma mark - 初始化
- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        YYBMacLogInfo(kHomeLogTag, @"首页视图初始化...");
        [self setupUI];
        [self reloadHomePage];
    }
    return self;
}

#pragma mark - UI搭建
- (void)setupUI {
    self.wantsLayer = YES;

    NSCollectionViewFlowLayout *layout = [[NSCollectionViewFlowLayout alloc] init];
    layout.scrollDirection = NSCollectionViewScrollDirectionVertical;
    layout.minimumInteritemSpacing = 0;
    layout.minimumLineSpacing = 16;
    layout.sectionInset = NSEdgeInsetsMake(24, 20, 24, 20);
    layout.itemSize = NSMakeSize(100, 100); // 防止初始size为0导致crash

    NSCollectionView *collectionView = [[NSCollectionView alloc] initWithFrame:NSZeroRect];
    collectionView.collectionViewLayout = layout;
    collectionView.dataSource = self;
    collectionView.delegate = self;
    collectionView.backgroundColors = @[[NSColor clearColor]];

    // 注册行cell（每行Container）
    [collectionView registerClass:[YYBHomeRowCell class] forItemWithIdentifier:@"YYBHomeRowCell"];

    self.homeCollectionView = collectionView;

    NSScrollView *scrollView = [[NSScrollView alloc] initWithFrame:self.bounds];
    scrollView.documentView = collectionView;
    scrollView.hasVerticalScroller = YES;
    scrollView.backgroundColor = [NSColor clearColor];
    self.scrollView = scrollView;
    [self addSubview:scrollView];

    [scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];

    YYBMacLogInfo(kHomeLogTag, @"首页主列表UI搭建完毕。");
}

#pragma mark - 首页数据请求与刷新
- (void)reloadHomePage {
    YYBMacLogInfo(kHomeLogTag, @"首页刷新请求...");
    NSDictionary *bodyValue = @{
        @"bid": @"yybmac",
        @"preview": @NO,
        @"layout": @"",
        @"listS": @{
                @"region": @{@"repStr": @[@"CN"]},
                @"supplyId": @{@"repStr": @[@""]},
                @"wx_sdk_version": @{@"repStr": @[@""]},
                @"installed_list": @{@"repStr": @[]},
                @"architecture": @{@"repStr": @[@"Unknown"]},
                @"trace_id": @{@"repStr": @[@""]}
        },
        @"listI": @{
                @"international": @{@"repInt": @[@0]},
                @"client_type": @{@"repInt": @[@0]},
                @"oem_type": @{@"repInt": @[@0]},
                @"installed_appid_list": @{@"repInt": @[]},
                @"multi_oaid_switch": @{@"repInt": @[@2]}
        },
        @"offset": @0,
        @"size": @15,
        @"trace": @NO
    };
//    // 旧版协议
//    YYBMacJsonCmdRequest *request = [[YYBMacJsonCmdRequest alloc] initV2WithApi:@"dynamicard_pcyyb" scene:@"discovery" bodyValue:bodyValue];
    // 新版协议
    YYBMacJsonCmdRequest *request = [[YYBMacJsonCmdRequest alloc] initV2WithApi:@"dynamicard_pcyyb" scene:@"discovery_client" bodyValue:bodyValue];

    __weak typeof(self) weakSelf = self;
    [[YYBMacHttp sharedInstance] sendYYBJsonCmd:request success:^(YYBMacJsonCmdResponse * _Nonnull response) {
        YYBMacLogInfo(kHomeLogTag, @"首页数据请求成功，开始解析...");
        NSDictionary *respData = response.responseObject[@"data"];
        YYBHomeResponse *homeResp = [YYBHomeResponse yy_modelWithDictionary:respData];
        if (!homeResp) {
            YYBMacLogError(kHomeLogTag, @"首页数据解析失败：%@", response.responseObject);
            return;
        }
        YYBMacLogInfo(kHomeLogTag, @"首页数据解析成功，组件数=%ld", (long)homeResp.components.count);
        weakSelf.componentsData = homeResp.components;
        
        // 分组（业务自定义拼行策略）
        weakSelf.rowComponents = [weakSelf buildRowsFromComponents:homeResp.components];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf.homeCollectionView reloadData];
        });
    } failure:^(NSError * _Nonnull error) {
        YYBMacLogError(kHomeLogTag, @"首页数据请求失败: %@", error);
    }];
}

#pragma mark - 业务自定义分组算法, TODO: 窗口大小变化时也需要刷新适配，重新分组
/// 将一维components数组归并为二维rows，策略需业务review
- (NSArray<NSArray<YYBComponent *> *> *)buildRowsFromComponents:(NSArray<YYBComponent *> *)components {
    NSMutableArray *rows = [NSMutableArray array];
    NSMutableArray *curRow = [NSMutableArray array];
    
    for (YYBComponent *comp in components) {
        // ===【业务规则调整区】===
        // 1. Top/CardId等应单独占一整行
        if ([comp.cardId isEqualToString:@"RecomTopView"]) {
            // 强制开启新的一行
            if (curRow.count > 0) {
                // 占行前将前面的内容加入数组
                [rows addObject:[curRow copy]];
                [curRow removeAllObjects];
            }
            // 独占一行
            [rows addObject:@[comp]];
        }
        // 2. 今日推荐为一行
        else if ([comp.cardId isEqualToString:@"TodayActivity"]) {
            // 强制开启新的一行
            if (curRow.count > 0) {
                // 占行前将前面的内容加入数组
                [rows addObject:[curRow copy]];
                [curRow removeAllObjects];
            }
            [curRow addObject:comp];
        } else if ([comp.cardId hasPrefix:@"Today"]) {
            // 拼接在前面的行中
            [curRow addObject:comp];
        }
        // 2. 榜单4个卡为一行，rankleft为首行
        else if ([comp.cardId isEqualToString:@"RankLeft"]) {
            // 强制开启新的一行
            if (curRow.count > 0) {
                // 占行前将前面的内容加入数组
                [rows addObject:[curRow copy]];
                [curRow removeAllObjects];
            }
            [curRow addObject:comp];
        } else if ([comp.cardId hasPrefix:@"Rank"]) {
            // 拼接在前面的行中
            [curRow addObject:comp];
        }
        // 3. 其它，默认单占一行
        else {
            // 强制开启新的一行
            if (curRow.count > 0) {
                // 占行前将前面的内容加入数组
                [rows addObject:[curRow copy]];
                [curRow removeAllObjects];
            }
            [rows addObject:@[comp]];
        }
    }
    
    if (curRow.count > 0) [rows addObject:[curRow copy]];
    
    YYBMacLogInfo(kHomeLogTag, @"拼行算法分组，原%lu卡，得%lu行", (unsigned long)components.count, (unsigned long)rows.count);
    for (int i=0; i<rows.count; i++) {
        NSArray *row = rows[i];
        YYBMacLogInfo(kHomeLogTag, @"-->第%d行:%@", i, [[row valueForKey:@"cardId"] componentsJoinedByString:@","]);
    }
    return rows;
}


#pragma mark - NSCollectionViewDataSource
- (NSInteger)numberOfSectionsInCollectionView:(NSCollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(NSCollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.rowComponents.count;
}

- (NSCollectionViewItem *)collectionView:(NSCollectionView *)collectionView itemForRepresentedObjectAtIndexPath:(NSIndexPath *)indexPath {
    NSArray<YYBComponent *> *rowComps = self.rowComponents[indexPath.item];
    YYBHomeRowCell *rowCell = [collectionView makeItemWithIdentifier:@"YYBHomeRowCell" forIndexPath:indexPath];
    [rowCell reloadWithComponents:rowComps rowIndex:indexPath.item];
    
    NSString *rowCardIds = [[rowComps valueForKey:@"cardId"] componentsJoinedByString:@","];
    YYBMacLogInfo(kHomeLogTag, @"渲染行%ld: cardIds=%@", (long)indexPath.item, rowCardIds);
    
    return rowCell;
}

#pragma mark - NSCollectionViewDelegateFlowLayout
// 如需不同高度，可根据行或cardId做区分
- (NSSize)collectionView:(NSCollectionView *)collectionView layout:(NSCollectionViewLayout *)layout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    NSArray<YYBComponent *> *rowComps = self.rowComponents[indexPath.item];

    // 业务：可以按组件类型定高度
    if (rowComps.count >= 1) {
        YYBComponent *first = rowComps.firstObject;
        if ([first.cardId isEqualToString:@"RecomTopView"]) {
            return NSMakeSize(kHomePageViewMaxWidth, 244);
        }
        if ([first.cardId isEqualToString:@"RankLeft"]) {
            return NSMakeSize(kHomePageViewMaxWidth, 636);
        }
    }
    return NSMakeSize(kHomePageViewMaxWidth, 320);
}

@end
