//
//  YYBHomeBaseItem.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBHomeBaseItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBComponent.h"

static NSString *const kBaseItemLogTag = @"YYBHomeBaseItem";

@interface YYBHomeBaseItem ()

@property (nonatomic, strong) NSTextField *titleLabel; // 用于显示标题

@end

@implementation YYBHomeBaseItem

// 基类卡片view结构：随机色背景+标题
- (void)viewDidLoad {
    [super viewDidLoad];
    if (!self.useBaseUI) return;

    self.view.wantsLayer = YES;
    
    // 检查是否已加过
    if (!self.titleLabel) {
        NSTextField *label = [[NSTextField alloc] initWithFrame:NSZeroRect];
        label.editable = NO;
        label.bezeled = NO;
        label.bordered = NO;
        label.selectable = NO;
        label.drawsBackground = NO;
        label.alignment = NSTextAlignmentCenter;
        label.font = [NSFont boldSystemFontOfSize:18];
        label.textColor = [NSColor whiteColor];
        label.lineBreakMode = NSLineBreakByTruncatingTail;
        label.translatesAutoresizingMaskIntoConstraints = NO;
        label.backgroundColor = [NSColor clearColor];
        label.alphaValue = 1.0;    // 确保可见

        [self.view addSubview:label];
        [NSLayoutConstraint activateConstraints:@[
            [label.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
            [label.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor],
            [label.leadingAnchor constraintGreaterThanOrEqualToAnchor:self.view.leadingAnchor constant:8],
            [label.trailingAnchor constraintLessThanOrEqualToAnchor:self.view.trailingAnchor constant:-8]
        ]];
        self.titleLabel = label;
    }
}

/// 默认数据绑定，保存当前数据与index，并输出基础日志
- (void)configureWithComponent:(YYBComponent *)component
                    atRowIndex:(NSInteger)rowIdx
                   atItemIndex:(NSInteger)itemIdx {
    self.currentComponent = component;
    self.rowIndex = rowIdx;
    self.itemIndex = itemIdx;
    self.currentComponentGroup = @[component];
    NSString *title = component.data.name ?: [NSString stringWithFormat:@"分区-row(%ld)-item(%ld)", rowIdx, itemIdx];
    
    YYBMacLogInfo(kBaseItemLogTag, @"基类Item分发: rowIdx=%ld itemIdx=%ld cardId=%@ 标题:%@", (long)rowIdx, (long)itemIdx, component.cardId, title);
    if (!self.useBaseUI) {
        return;
    }
    
    // 生成彩色背景，方便调试不同卡
    self.view.wantsLayer = YES;
    self.view.layer.backgroundColor = [self backgroundColorForIndex:rowIdx].CGColor;
    if (self.titleLabel) {
        self.titleLabel.stringValue = title;
        self.titleLabel.hidden = NO;
        self.titleLabel.alphaValue = 1.0;
        [self.titleLabel display]; // 强制刷新一遍
    } else {
        YYBMacLogInfo(kBaseItemLogTag, @"titleLabel为nil，无法渲染文本！");
    }
}

/// 绑定（多个）数据入口（可被所有子类覆盖，基类做默认日志和数据保存）
- (void)configureWithComponentGroup:(NSArray<YYBComponent *> *)componentGroup atRowIndex:(NSInteger)rowIdx {
    self.currentComponent = componentGroup.firstObject;
    self.rowIndex = rowIdx;
    self.itemIndex = 0;
    self.currentComponentGroup = componentGroup;
    
    NSString *title = [NSString stringWithFormat:@"%@...%@-分区-rowIdx=%ld",
                       componentGroup.firstObject.cardId ?: @"",
                       componentGroup.lastObject.cardId ?: @"",
                       rowIdx];
    
    YYBMacLogInfo(kBaseItemLogTag, @"基类Item分发: cardId=%@...%@ 标题:%@, rowIdx=%ld",
                  componentGroup.firstObject.cardId ?: @"",
                  componentGroup.lastObject.cardId ?: @"",
                  title,
                  rowIdx);
    if (!self.useBaseUI) {
        return;
    }
    
    // 生成彩色背景，方便调试不同卡
    self.view.wantsLayer = YES;
    self.view.layer.backgroundColor = [self backgroundColorForIndex:rowIdx].CGColor;
    if (self.titleLabel) {
        self.titleLabel.stringValue = title;
        self.titleLabel.hidden = NO;
        self.titleLabel.alphaValue = 1.0;
        [self.titleLabel display]; // 强制刷新一遍
    } else {
        YYBMacLogInfo(kBaseItemLogTag, @"titleLabel为nil，无法渲染文本！");
    }
}

/// 根据index返回固定色，便于多卡调试、更美观
- (NSColor *)backgroundColorForIndex:(NSInteger)idx {
    // 可根据业务需求调整色值方案
    NSArray<NSColor *> *colors = @[
        [NSColor colorWithRed:0.46 green:0.56 blue:0.76 alpha:1],
        [NSColor colorWithRed:0.36 green:0.76 blue:0.66 alpha:1],
        [NSColor colorWithRed:0.96 green:0.56 blue:0.36 alpha:1],
        [NSColor colorWithRed:0.66 green:0.36 blue:0.96 alpha:1],
        [NSColor colorWithRed:0.86 green:0.36 blue:0.66 alpha:1],
        [NSColor colorWithRed:0.66 green:0.76 blue:0.36 alpha:1],
    ];
    return colors[idx % colors.count];
}

@end
