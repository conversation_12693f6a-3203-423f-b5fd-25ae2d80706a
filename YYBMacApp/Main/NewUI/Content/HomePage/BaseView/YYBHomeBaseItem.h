//
//  YYBHomeBaseItem.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import <Cocoa/Cocoa.h>
@class YYBComponent;

NS_ASSUME_NONNULL_BEGIN

/// 首页主CollectionView的所有分区模块统一基类
@interface YYBHomeBaseItem : NSCollectionViewItem

/// 是否显示base控件，默认为NO不显示
@property (nonatomic, assign) BOOL useBaseUI;

/// 绑定（单个）数据源入口（可被所有子类覆盖，基类做默认日志和数据保存）
- (void)configureWithComponent:(YYBComponent *)component atRowIndex:(NSInteger)rowIdx atItemIndex:(NSInteger)itemIdx;

/// 绑定（多个）数据源入口（可被所有子类覆盖，基类做默认日志和数据保存）
- (void)configureWithComponentGroup:(NSArray<YYBComponent *> *)componentGroup atRowIndex:(NSInteger)rowIdx;

/// 当前行单数据源component
@property (nonatomic, strong, nullable) YYBComponent *currentComponent;

/// 当前行多数据源(NSArray<YYBComponent *> *)
@property (nonatomic, strong, nullable) NSArray<YYBComponent *> *currentComponentGroup;

/// 当前item的iindex
@property (nonatomic, assign) NSInteger itemIndex;

/// 当前行的index
@property (nonatomic, assign) NSInteger rowIndex;

@end

NS_ASSUME_NONNULL_END
