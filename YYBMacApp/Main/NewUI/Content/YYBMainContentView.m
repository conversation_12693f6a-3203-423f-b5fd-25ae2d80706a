//
//  YYBMainContentView.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/13.
//

#import <QuartzCore/QuartzCore.h>
#import "YYBMainContentView.h"
#import "Masonry.h"
#import "YYBHomePageView.h"
#import "YYBWKWebView.h"
#import "YYBDefine.h"
#import "YYBMainNavigationView.h"
#import "MainUIDefine.h"

static NSString *const defaultMainWebViewURL = @"https://testmac.yyb.qq.com/";

@interface YYBMainContentView ()

@property (nonatomic, strong, readwrite) YYBHomePageView *homePageView;
@property (nonatomic, strong, readwrite) YYBWKWebView *webView;
@property (nonatomic, strong) NSView *currentContentView;
@property (nonatomic, strong) NSMutableArray<YYBMainNavigationItem *> *navigationItemList;
@property (nonatomic, strong, readwrite) YYBMainNavigationItem *currentNavigationItem;
@property (nonatomic, assign) NSInteger currentNavigationIndex;

@end

@implementation YYBMainContentView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        _navigationItemList = [NSMutableArray array];
        _currentNavigationIndex = -1;
        [self setupView];
        [self setupSubviews];
    }
    return self;
}

- (void)setupView {
    self.wantsLayer = YES;
    self.layer.backgroundColor = [NSColor colorNamed:@"Main/BGColor"].CGColor;
}

- (void)setupSubviews {
    // 创建首页视图
    self.homePageView = [[YYBHomePageView alloc] initWithFrame:NSZeroRect];
    [self addSubview:self.homePageView];
    [self.homePageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.bottom.equalTo(self);
    }];
    
    // 创建Web视图
    YYBWKWebViewConfig *webViewConfig = [[YYBWKWebViewConfig alloc] initWithProcess:kProcessAppStore windowId:nil];
    self.webView = [[YYBWKWebView alloc] initWithFrame:NSZeroRect config:webViewConfig];
    self.webView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    [self loadWebViewWithUrl:defaultMainWebViewURL];
    self.webView.hidden = YES;
    [self addSubview:self.webView];
    [self.webView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(kTopBarHeight));
        make.left.equalTo(@(kNavigationDefaultWidth));
        make.right.bottom.equalTo(self);
    }];
}

- (void)loadWebViewWithUrl:(NSString *)url {
    if (url.length == 0) {
        return;
    }
    
    NSURL *targetURL = [NSURL URLWithString:url];
    if (!targetURL) {
        return;
    }
    
    NSURLRequest *request = [NSURLRequest requestWithURL:targetURL];
    [self.webView loadRequest:request];
}

- (void)loadNavigationItem:(YYBMainNavigationItem *)item animated:(BOOL)animated {
    if (!item) {
        return;
    }
    
    // 如果已经是当前项，不做任何操作
    if (self.currentNavigationItem == item) {
        return;
    }
    
    // 如果当前导航索引不是最后一个，移除后面的所有导航项
    if (self.currentNavigationIndex >= 0 && self.currentNavigationIndex < self.navigationItemList.count - 1) {
        [self.navigationItemList removeObjectsInRange:NSMakeRange(self.currentNavigationIndex + 1, self.navigationItemList.count - self.currentNavigationIndex - 1)];
    }
    
    // 添加新的导航项到列表
    [self.navigationItemList addObject:item];
    self.currentNavigationIndex = self.navigationItemList.count - 1;
    [self switchToItem:item animated:animated];
}

- (void)switchToItem:(YYBMainNavigationItem *)item animated:(BOOL)animated {
    self.currentNavigationItem = item;
    NSView *targetView = item.isWebView ? (NSView *)self.webView : (NSView *)self.homePageView;
    [self switchFromView:self.currentContentView toView:targetView animated:animated];
    self.currentContentView = targetView;
    [self notifyDelegateContentSwitched];
}

- (BOOL)canGoBack {
    return (self.currentNavigationIndex > 0 && self.navigationItemList.count > 1);
}

- (BOOL)canGoForward {
    return !(self.currentNavigationIndex >= self.navigationItemList.count - 1);
}

- (void)goBack {
    // 检查是否可以返回
    if (self.currentNavigationIndex <= 0 || self.navigationItemList.count <= 1) {
        return;
    }
    
    // 更新导航索引
    self.currentNavigationIndex--;
    
    // 获取上一个导航项
    YYBMainNavigationItem *previousItem = self.navigationItemList[self.currentNavigationIndex];
    [self switchToItem:previousItem animated:NO];
}

- (void)goForward {
    // 检查是否可以前进
    if (self.currentNavigationIndex >= self.navigationItemList.count - 1) {
        return;
    }
    
    // 更新导航索引
    self.currentNavigationIndex++;
    
    // 获取下一个导航项
    YYBMainNavigationItem *nextItem = self.navigationItemList[self.currentNavigationIndex];
    [self switchToItem:nextItem animated:NO];
}

- (void)reload {
    if (self.currentContentView == self.webView) {
        [self.webView reload];
    } else {
        
    }
}

- (void)notifyDelegateContentSwitched {
    if ([self.delegate respondsToSelector:@selector(contentViewDidSwitchContent:)]) {
        [self.delegate contentViewDidSwitchContent:self];
    }
}

- (void)switchFromView:(NSView *)fromView toView:(NSView *)toView animated:(BOOL)animated {
    if (fromView == toView) {
        return;
    }
    
    // 确保目标视图在视图层次中
    if (![self.subviews containsObject:toView]) {
        [self addSubview:toView];
    }
    
    // 准备目标视图
    toView.hidden = NO;
    toView.alphaValue = 0.0;
    
    if (animated) {
        // 使用动画切换视图
        [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
            context.duration = 0.3;
            context.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
            
            [[fromView animator] setAlphaValue:0.0];
            [[toView animator] setAlphaValue:1.0];
            
        } completionHandler:^{
            fromView.hidden = YES;
            fromView.alphaValue = 1.0;
        }];
    } else {
        // 不使用动画，直接切换
        fromView.hidden = YES;
        toView.alphaValue = 1.0;
    }
}


@end
