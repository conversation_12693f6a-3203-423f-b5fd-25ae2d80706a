#import <Foundation/Foundation.h>
#import "YYBBaseModel.h"

NS_ASSUME_NONNULL_BEGIN
@interface YYBReportInfo : YYBBaseModel
@property (nonatomic, copy, nullable) NSString *adTagLabel;
@property (nonatomic, copy, nullable) NSString *ctype;
@property (nonatomic, assign) NSInteger enableAd;
@property (nonatomic, copy, nullable) NSString *expIds;
@property (nonatomic, copy, nullable) NSString *forceType;
@property (nonatomic, assign) NSInteger itemIndex;
@property (nonatomic, copy, nullable) NSString *quQuery;
@property (nonatomic, copy, nullable) NSString *recmdType;
@property (nonatomic, assign) NSInteger resPos;
@property (nonatomic, copy, nullable) NSString *rid;
@property (nonatomic, copy, nullable) NSString *sourceType;
@property (nonatomic, copy, nullable) NSString *traceId;
@end
NS_ASSUME_NONNULL_END

