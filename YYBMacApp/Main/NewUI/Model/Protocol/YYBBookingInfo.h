#import <Foundation/Foundation.h>
#import "YYBBaseModel.h"

NS_ASSUME_NONNULL_BEGIN
@interface YYBBookingInfo : YYBBaseModel
@property (nonatomic, copy, nullable) NSString *activeLink;
@property (nonatomic, strong, nullable) NSNumber *beginTime;
@property (nonatomic, strong, nullable) NSNumber *endTime;
@property (nonatomic, strong, nullable) NSNumber *expectPubTime;
@property (nonatomic, copy, nullable) NSString *horizontalCover;
@property (nonatomic, strong, nullable) NSNumber *needAutoBooking;
@property (nonatomic, strong, nullable) NSNumber *needSelectPlatform;
@property (nonatomic, strong, nullable) NSNumber *releaseTime;
@end
NS_ASSUME_NONNULL_END

