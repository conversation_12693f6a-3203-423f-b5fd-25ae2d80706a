#import <Foundation/Foundation.h>
#import "YYBBaseModel.h"

NS_ASSUME_NONNULL_BEGIN
@interface YYBBgColor : YYBBaseModel
@property (nonatomic, copy, nullable) NSString *adCardIconBgColor;
@property (nonatomic, copy, nullable) NSString *contentCardIconBgColor;
@property (nonatomic, copy, nullable) NSString *recommendCardFirstPicBgColor;
@property (nonatomic, copy, nullable) NSString *recommendCardHorizontalBgColor;
@property (nonatomic, copy, nullable) NSString *recommendCardIconBgColor;
@property (nonatomic, copy, nullable) NSString *horizontalCardBgColor;
@property (nonatomic, copy, nullable) NSString *verticalCardBgColor;
@end
NS_ASSUME_NONNULL_END

