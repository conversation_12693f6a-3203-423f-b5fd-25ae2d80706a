#import <Foundation/Foundation.h>
#import "YYBBaseModel.h"

NS_ASSUME_NONNULL_BEGIN
@interface YYBPcGameInfo : YYBBaseModel
@property (nonatomic, copy, nullable) NSString *clientNames;
@property (nonatomic, copy, nullable) NSString *dirRegKey;
@property (nonatomic, copy, nullable) NSString *disableEndTime;
@property (nonatomic, copy, nullable) NSString *disableMsg;
@property (nonatomic, copy, nullable) NSString *disableStartTime;
@property (nonatomic, copy, nullable) NSString *filesToDelete;
@property (nonatomic, copy, nullable) NSString *freeInstallation;
@property (nonatomic, strong, nullable) NSNumber *isCreateShortcut;
@property (nonatomic, strong, nullable) NSNumber *isLaunchAfterInstalled;
@property (nonatomic, copy, nullable) NSString *launcherDirRegKey;
@property (nonatomic, copy, nullable) NSString *launcherNames;
@property (nonatomic, copy, nullable) NSString *pcGameAuth;
@property (nonatomic, copy, nullable) NSString *runningProcessNames;
@property (nonatomic, copy, nullable) NSString *shortcutName;
@property (nonatomic, copy, nullable) NSString *startParam;
@property (nonatomic, copy, nullable) NSString *uninstallerName;
@property (nonatomic, copy, nullable) NSString *windowClassName;
@property (nonatomic, copy, nullable) NSString *windowName;
@end
NS_ASSUME_NONNULL_END

