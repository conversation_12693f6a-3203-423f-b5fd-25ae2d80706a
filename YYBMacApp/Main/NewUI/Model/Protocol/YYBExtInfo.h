#import <Foundation/Foundation.h>
#import "YYBBaseModel.h"

NS_ASSUME_NONNULL_BEGIN
@interface YYBExtInfo : YYBBaseModel
@property (nonatomic, strong, nullable) NSNumber *isShowKeymapSidebar;
@property (nonatomic, strong, nullable) NSNumber *skipMd5Check;
@property (nonatomic, strong, nullable) NSNumber *downMobile;
@property (nonatomic, copy, nullable) NSString *offerId;
@property (nonatomic, copy, nullable) NSString *downloadText;
@property (nonatomic, strong, nullable) NSNumber *relevancePcgameForceShow;
@property (nonatomic, copy, nullable) NSString *relevancePcgamePkgname;
@property (nonatomic, copy, nullable) NSString *relevancePcgamePkgnameDownloadText;
@property (nonatomic, copy, nullable) NSString *activityUrl;
@property (nonatomic, copy, nullable) NSString *msStoreDetailPage;
@property (nonatomic, strong, nullable) NSDictionary *shouldUpdateToPcgame;
@property (nonatomic, strong, nullable) NSArray *notSyncPlatform;
@property (nonatomic, strong, nullable) NSNumber *hybridYunGameLaunchId;
@property (nonatomic, copy, nullable) NSString *hybridYunGameSource;
@end
NS_ASSUME_NONNULL_END

