//
//  DownloadBoxPopver.h
//  YYBMacApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/21.
//

#ifndef DownloadBoxPopver_h
#define DownloadBoxPopver_h

#import <Cocoa/Cocoa.h>
#import "YYBApkInfoModel.h"

NS_ASSUME_NONNULL_BEGIN

@protocol PendingInstallCellDelegate <NSObject>
@optional
- (void)installDidCancelAtRow:(NSInteger)row;
@end

@interface PendingInstallItem : NSObject
@property (strong, nonatomic) YYBApkInfoModel *apkInfoModel;
@property (assign, nonatomic) BOOL isPaused;
@end

@interface DownloadBoxPopver : NSViewController <PendingInstallCellDelegate>
@property (strong, nonatomic) NSMutableArray<PendingInstallItem *> *installItems;

- (instancetype)init;
- (void)showInView:(NSView *)parentView atPoint:(CGPoint)position;
- (void)dismiss;
@end

NS_ASSUME_NONNULL_END

#endif /* DownloadBoxPopver_h */
