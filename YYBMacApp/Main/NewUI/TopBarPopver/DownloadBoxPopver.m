//
//  DownloadBoxPopver.m
//  YYBMacApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/21.
//

#import "DownloadBoxPopver.h"
#import "SDWebImage.h"

#define OVERLAY_WIDTH 350.0
#define ROW_HEIGHT 90.0
#define HEADER_HEIGHT 50.0
#define MAX_VISIBLE_ROWS 4

#pragma mark - PendingInstallItem Implementation
@implementation PendingInstallItem
@end

#pragma mark - Custom Table Cell
@interface PendingInstallCell : NSTableCellView
@property (strong) NSImageView *iconView;
@property (strong) NSTextField *nameLabel;
@property (strong) NSTextField *sizeLabel;
@property (strong) NSTextField *statusLabel;
@property (strong) NSButton *pauseButton;
@property (strong) NSButton *cancelButton;
@property (strong) NSImage *pauseIcon;
@property (strong) NSImage *playIcon;
@property (strong) NSProgressIndicator *progressIndicator;

@property (weak) id<PendingInstallCellDelegate> delegate;

@end

@implementation PendingInstallCell

- (instancetype)initWithFrame:(NSRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        // Icon View
        _iconView = [[NSImageView alloc] initWithFrame:NSMakeRect(10, 15, 69, 69)];
        _iconView.layer.cornerRadius = 5;
        _iconView.wantsLayer = YES;
        [_iconView sd_setImageWithURL:[NSURL URLWithString:@"https://pp.myapp.com/ma_icon/0/icon_10485956_1741244516/0"]];
        [self addSubview:_iconView];
        
        // Name Label
        _nameLabel = [NSTextField labelWithString:@""];
        _nameLabel.font = [NSFont systemFontOfSize:14];
        _nameLabel.frame = NSMakeRect(95, ROW_HEIGHT -35, 170, 20);
        [self addSubview:_nameLabel];
        
        
        // Progress Indicator
        _progressIndicator = [[NSProgressIndicator alloc] initWithFrame:NSMakeRect(95, ROW_HEIGHT - 50, 158, 10)];
        _progressIndicator.style = NSProgressIndicatorStyleBar;
        _progressIndicator.controlSize = NSControlSizeSmall;
        [_progressIndicator startAnimation:nil];
        [self addSubview:_progressIndicator];
        
        // Pause Button
        // 暂停图标
        _pauseIcon = [NSImage imageNamed:@"Main/DownloadBox/icon_pause"];
        // 播放图标（暂停状态下显示播放）
        _playIcon = [NSImage imageNamed:@"Main/DownloadBox/icon_play"];
        
        _pauseButton = [NSButton buttonWithImage:_pauseIcon target:self action:@selector(pauseClicked:)];
        _pauseButton.image = _pauseIcon;
        _pauseButton.identifier = @"pause";
        _pauseButton.imagePosition = NSImageOnly;  // 仅显示图标
        _pauseButton.bezelStyle = NSBezelStyleInline;
        _pauseButton.imageScaling = NSImageScaleProportionallyDown; // 图片自适应
        _pauseButton.wantsLayer = YES;
        _pauseButton.layer.backgroundColor = [NSColor clearColor].CGColor;
        _pauseButton.focusRingType = NSFocusRingTypeNone; // 禁用焦点高亮环
        _pauseButton.frame = NSMakeRect(263, ROW_HEIGHT - 50, 20, 20);
        [self addSubview:_pauseButton];
       
        
        
        // Cancel Button
        _cancelButton = [NSButton buttonWithImage:[NSImage imageNamed:@"Main/DownloadBox/icon_close"] target:self action:@selector(cancelClicked:)];
        _cancelButton.frame = NSMakeRect(294, ROW_HEIGHT - 50, 20, 20);
        _cancelButton.imagePosition = NSImageOnly;  // 仅显示图标
        _cancelButton.bezelStyle = NSBezelStyleInline;
        _cancelButton.imageScaling = NSImageScaleProportionallyDown; // 图片自适应
        _cancelButton.wantsLayer = YES;
        _cancelButton.layer.backgroundColor = [NSColor clearColor].CGColor;
        _cancelButton.focusRingType = NSFocusRingTypeNone; // 禁用焦点高亮环
        [self addSubview:_cancelButton];
        
        // Size Label
        _sizeLabel = [NSTextField labelWithString:@""];
        _sizeLabel.font = [NSFont systemFontOfSize:12];
        _sizeLabel.textColor = [NSColor secondaryLabelColor];
        _sizeLabel.frame = NSMakeRect(95, 16, 70, 16);
        [self addSubview:_sizeLabel];
        
        //statusLabel
        _statusLabel = [NSTextField labelWithString:@"暂停中"];
        _statusLabel.font = [NSFont systemFontOfSize:12];
        _statusLabel.textColor = [NSColor secondaryLabelColor];
        _statusLabel.frame = NSMakeRect(145, 16, 70, 16);
        [self addSubview:_statusLabel];
        
       
    }
    return self;
}

- (void)pauseClicked:(NSButton *)sender {
    if ([sender.identifier isEqualToString:@"pause"]) {
        sender.identifier = @"play";
        sender.image = _playIcon;
        [_progressIndicator stopAnimation:nil];
    } else {
        sender.identifier = @"pause";
        sender.image = _pauseIcon;
        [_progressIndicator startAnimation:nil];
    }
    
    // Notify delegate about state change
    //TODO
}

- (void)cancelClicked:(NSButton *)sender {
    // Notify delegate about cancel
    NSTableView *tableView = (NSTableView *)self.superview.superview;
    NSInteger row = [tableView rowForView:self];
    if (row != -1 && [self.delegate respondsToSelector:@selector(installDidCancelAtRow:)]) {
        [self.delegate installDidCancelAtRow:row];
    }
}

@end

#pragma mark - Main Overlay Implementation
@interface DownloadBoxPopver () <NSTableViewDelegate, NSTableViewDataSource>
@property (strong) NSTableView *tableView;
@property (strong) NSVisualEffectView *backgroundView;
@property (strong) NSTextField *titleLabel;
@property (assign) NSInteger currentItemsCount;
@property (strong, nonatomic) NSClickGestureRecognizer *outsideClickGesture;
@end

@implementation DownloadBoxPopver

#pragma mark - PendingInstallCellDelegate
- (void)installDidCancelAtRow:(NSInteger)row {
    if (row < 0 || row >= self.installItems.count) return;
    
    // Remove the item from the data source
    [self.installItems removeObjectAtIndex:row];
    
    // Update the UI with animation
    [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
        context.duration = 0.3;
        [self.tableView removeRowsAtIndexes:[NSIndexSet indexSetWithIndex:row] withAnimation:NSTableViewAnimationSlideUp];
        
        // Adjust the popover height if needed
        CGFloat visibleRows = MIN(MAX_VISIBLE_ROWS, self.installItems.count);
        CGFloat totalHeight = HEADER_HEIGHT + (visibleRows * ROW_HEIGHT);
        NSRect newFrame = self.view.frame;
        newFrame.size.height = totalHeight;
        self.view.animator.frame = newFrame;
    }];
}

- (instancetype)init{
    self = [super initWithNibName:nil bundle:nil];
    if (self) {
        
        //TODO Mock install items
        NSMutableArray<PendingInstallItem *> *items = [NSMutableArray array];
        for (int i = 0; i < 10; i++) {
            PendingInstallItem *item = [[PendingInstallItem alloc] init];
            item.apkInfoModel = [[YYBApkInfoModel alloc] init];
            item.apkInfoModel.name = [NSString stringWithFormat:@"应用 %d", i];
            item.apkInfoModel.icon = @"https://pp.myapp.com/ma_icon/0/icon_10485956_1741244516/0";
            [items addObject:item];
        }
        
        _installItems = items;
        _currentItemsCount = items.count;
    }
    return self;
}

- (void)loadView {
    // Calculate height based on visible rows
    CGFloat visibleRows = MIN(MAX_VISIBLE_ROWS, self.installItems.count);
    CGFloat totalHeight = HEADER_HEIGHT + (visibleRows * ROW_HEIGHT);
    
    // Background View (Visual Effect View)
    NSRect frame = NSMakeRect(0, 0, OVERLAY_WIDTH, totalHeight);
    _backgroundView = [[NSVisualEffectView alloc] initWithFrame:frame];
    _backgroundView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    _backgroundView.material = NSVisualEffectMaterialMenu;
    _backgroundView.state = NSVisualEffectStateActive;
    _backgroundView.wantsLayer = YES;
    _backgroundView.layer.cornerRadius = 5.0;
    _backgroundView.layer.masksToBounds = YES;
    
    // Title Label
    _titleLabel = [NSTextField labelWithString:@"正在安装"];
    _titleLabel.font = [NSFont boldSystemFontOfSize:16];
    _titleLabel.frame = NSMakeRect(15, totalHeight - HEADER_HEIGHT - 15, OVERLAY_WIDTH - 30, HEADER_HEIGHT);
    _titleLabel.autoresizingMask = NSViewMinYMargin;
    [_backgroundView addSubview:_titleLabel];
    
    // ScrollView Container
    CGFloat scrollHeight = visibleRows * ROW_HEIGHT;
    NSScrollView *scrollView = [[NSScrollView alloc] initWithFrame:NSMakeRect(
        0, 0, OVERLAY_WIDTH, scrollHeight
    )];
    scrollView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    scrollView.drawsBackground = NO;
    
    // TableView
    _tableView = [[NSTableView alloc] initWithFrame:scrollView.bounds];
    _tableView.autoresizingMask = NSViewWidthSizable;
    _tableView.backgroundColor = [NSColor clearColor];
    _tableView.headerView = nil;
    _tableView.rowHeight = ROW_HEIGHT;
    _tableView.selectionHighlightStyle = NSTableViewSelectionHighlightStyleNone;
    _tableView.dataSource = self;
    _tableView.delegate = self;
    
    // Single Column
    NSTableColumn *column = [[NSTableColumn alloc] initWithIdentifier:@"InstallColumn"];
    column.width = OVERLAY_WIDTH;
    [_tableView addTableColumn:column];
    
    scrollView.documentView = _tableView;
    [_backgroundView addSubview:scrollView];
    
    self.view = _backgroundView;
}

- (void)showInView:(NSView *)parentView atPoint:(CGPoint)position {
    if (!self.view.superview) {
        
        // 添加点击外部关闭的手势
           _outsideClickGesture = [[NSClickGestureRecognizer alloc] initWithTarget:self action:@selector(handleOutsideClick:)];
           _outsideClickGesture.numberOfClicksRequired = 1;
           _outsideClickGesture.delaysPrimaryMouseButtonEvents = NO; // 允许事件继续传递
           [parentView addGestureRecognizer:_outsideClickGesture];
        
        
        // Position the view
        NSRect frame = self.view.frame;
        frame.origin.x = position.x - frame.size.width -20;
        frame.origin.y = position.y - frame.size.height - 90;
        self.view.frame = frame;
        
        // Add to parent view
        [parentView addSubview:self.view];
        
        // Fade in animation
        self.view.alphaValue = 0;
        [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
            context.duration = 0.25;
            self.view.animator.alphaValue = 1.0;
        }];
        
    }
}


- (void)handleOutsideClick:(NSClickGestureRecognizer *)gesture {
    NSPoint location = [gesture locationInView:self.view.superview];
    if (!NSPointInRect(location, self.view.frame)) {
        [self dismiss];
        // 移除手势识别器
        if (self.outsideClickGesture) {
            [self.view.superview removeGestureRecognizer:self.outsideClickGesture];
            self.outsideClickGesture = nil; // 释放资源
        }
    }
    
}

- (void)dismiss {
    [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
        context.duration = 0.25;
        self.view.animator.alphaValue = 0;
    } completionHandler:^{
        [self.view removeFromSuperview];
    }];
}


#pragma mark - TableView DataSource
- (NSInteger)numberOfRowsInTableView:(NSTableView *)tableView {
    return self.installItems.count;
}

- (NSView *)tableView:(NSTableView *)tableView viewForTableColumn:(NSTableColumn *)tableColumn row:(NSInteger)row {
    PendingInstallCell *cell = [tableView makeViewWithIdentifier:@"InstallCell" owner:self];
    if (!cell) {
        cell = [[PendingInstallCell alloc] initWithFrame:NSMakeRect(0, 0, OVERLAY_WIDTH, ROW_HEIGHT)];
        cell.identifier = @"InstallCell";
    }
    
    PendingInstallItem *item = self.installItems[row];
    cell.nameLabel.stringValue = item.apkInfoModel.name;
    cell.sizeLabel.stringValue = @"1.2GB";
    cell.statusLabel.stringValue = item.apkInfoModel.apkState;
    cell.delegate = self;
    
//    cell.pauseButton.title = item.isPaused ? @"继续" : @"暂停";
//    item.isPaused ? [cell.progressIndicator stopAnimation:nil] : [cell.progressIndicator startAnimation:nil];
    
    return cell;
}

#pragma mark - Updates
- (void)updateItems:(NSMutableArray<PendingInstallItem *> *)newItems {
    self.installItems = newItems;
    
    // Animate changes if item count changed
    if (self.currentItemsCount != newItems.count) {
        [NSAnimationContext beginGrouping];
        [NSAnimationContext currentContext].duration = 0.3;
        
        [self.tableView beginUpdates];
        [self.tableView reloadData];
        [self.tableView endUpdates];
        
        // Resize overlay height
        CGFloat visibleRows = MIN(MAX_VISIBLE_ROWS, newItems.count);
        CGFloat totalHeight = HEADER_HEIGHT + (visibleRows * ROW_HEIGHT);
        
        NSRect newFrame = self.view.frame;
        newFrame.size.height = totalHeight;
        self.view.animator.frame = newFrame;
        
        [NSAnimationContext endGrouping];
        
        self.currentItemsCount = newItems.count;
    } else {
        [self.tableView reloadData];
    }
}

@end
