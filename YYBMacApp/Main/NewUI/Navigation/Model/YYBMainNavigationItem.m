//
//  YYBMainNavigationItem.m
//  YYBMacApp
//
//  Created by CodeBuddy on 2025/8/14.
//

#import "YYBMainNavigationItem.h"

@implementation YYBMainNavigationItem

- (instancetype)init {
    self = [super init];
    if (self) {
        _type = YYBNavigationItemTypeNormal;
        _isVisible = YES;
        _isSelected = NO;
    }
    return self;
}

+ (instancetype)itemWithIdentifier:(NSString *)identifier title:(NSString *)title icon:(NSImage *)icon isWebView:(BOOL)isWebView {
    YYBMainNavigationItem *item = [[YYBMainNavigationItem alloc] init];
    item.identifier = identifier;
    item.icon = icon;
    item.isWebView = isWebView;
    item.title = title;
    return item;
}

+ (instancetype)itemWithIdentifier:(NSString *)identifier title:(NSString *)title icon:(NSImage *)icon selectedIcon:(NSImage *)selectedIcon isWebView:(BOOL)isWebView {
    YYBMainNavigationItem *item = [self itemWithIdentifier:identifier title:title icon:icon isWebView:isWebView];
    item.selectedIcon = selectedIcon;
    return item;
}

@end
