//
//  YYBAppLaunchHistoryView.h
//  YYBMacApp
//
//  Created by lichenlin on 2025/8/20.
//

#import <Cocoa/Cocoa.h>

@class InstallApkInfo;

NS_ASSUME_NONNULL_BEGIN

/**
 * APP打开历史记录视图
 * 支持展开和收起两种状态，带有动画效果
 */
@interface YYBAppLaunchHistoryView : NSView

/**
 * 是否展开状态
 */
@property (nonatomic, assign, getter=isExpanded) BOOL expanded;

/**
 * 更新历史记录数据
 * @param recentApps 最近使用的APP数组，最多显示3个
 */
- (void)updateWithRecentApps:(NSArray<InstallApkInfo *> *)recentApps;

/**
 * 刷新历史记录显示
 */
- (void)refreshHistoryDisplay;

@end

NS_ASSUME_NONNULL_END
