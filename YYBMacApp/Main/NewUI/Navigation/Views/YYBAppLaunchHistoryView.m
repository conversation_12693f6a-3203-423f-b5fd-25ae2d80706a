//
//  YYBAppLaunchHistoryView.m
//  YYBMacApp
//
//  Created by lichenlin on 2025/8/20.
//

#import "YYBAppLaunchHistoryView.h"
#import "InstallApkInfo.h"
#import "SDWebImage.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "Masonry.h"
#import "YYBApkPackage.h"
#import "MainUIDefine.h"

static const CGFloat kItemHeight = 40.0;  // 每个展开的条目高度
static const CGFloat kItemSpacing = 8.0;  // 每个条目之间间距
static const CGFloat kIconSize = 28.0;  // 图标大小
static const CGFloat kFixedHeight = 164.0;  // view固定高度
static const CGFloat kTextLabelSpacing = 16.0;  // 文本标签与app item间隔

static NSString *const kTag = @"YYBAppLaunchHistoryView";

// 定义导航项视图状态枚举
typedef NS_ENUM(NSUInteger, YYBAppLaunchHistoryItemViewState) {
    YYBAppLaunchHistoryItemViewStateNormal,   // 正常状态
    YYBAppLaunchHistoryItemViewStateHover,    // 鼠标悬停状态
};

// 按钮毛玻璃效果
static NSVisualEffectMaterial const historyItemStateNormal = NSVisualEffectMaterialUnderPageBackground;
static NSVisualEffectMaterial const historyItemStateHover = NSVisualEffectMaterialMenu;

// 自定义的可点击 item view
@interface YYBAppLaunchHistoryItemView : NSView
@property (nonatomic, strong) InstallApkInfo *apkInfo;
@property (nonatomic, assign) BOOL expanded;
@property (nonatomic, strong) NSVisualEffectView *containerView;  // 容器视图（使用毛玻璃效果）
@property (nonatomic, strong) NSTrackingArea *trackingArea;  // 鼠标跟踪区域
@property (nonatomic, assign) YYBAppLaunchHistoryItemViewState viewState;  // 视图状态

@property (nonatomic, strong) NSView *nameLeftPaddingView;  // 名称左侧占位视图，用于动画
@property (nonatomic, strong) NSImageView *iconView;  // 图标视图
@property (nonatomic, strong) NSTextField *nameLabel;  // 名称标签

@end

@implementation YYBAppLaunchHistoryItemView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    // 设置初始状态
    self.viewState = YYBAppLaunchHistoryItemViewStateNormal;

    // 创建容器视图（使用毛玻璃效果）
    self.containerView = [[NSVisualEffectView alloc] init];
    self.containerView.wantsLayer = YES;
    self.containerView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    self.containerView.state = NSVisualEffectStateActive;
    self.containerView.layer.cornerRadius = 10.0;
    self.containerView.layer.masksToBounds = YES;
    self.containerView.material = historyItemStateNormal;
    [self addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];

    // 创建图标容器视图
    NSView *iconContainerView = [[NSView alloc] init];
    [self.containerView addSubview:iconContainerView];
    [iconContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.equalTo(self.containerView);
        make.width.mas_equalTo(iconContainerView.mas_height);
    }];

    // 创建图标视图
    self.iconView = [[NSImageView alloc] init];
    self.iconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    [iconContainerView addSubview:self.iconView];
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(iconContainerView);
        make.width.height.equalTo(@20);  // 图标大小20x20
    }];

    // 创建名称占位视图（仿照 YYBMainNavigationItemView）
    // 占位视图的宽度会根据containerView的宽度做线性变化，用于文字位移动画
    CGFloat radio = (46 - 40) / (kNavigationExpandWidth - kNavigationDefaultWidth);
    CGFloat offset = 40 - radio * kNavigationDefaultWidth;
    self.nameLeftPaddingView = [[NSView alloc] init];
    [self.containerView addSubview:self.nameLeftPaddingView];
    [self.nameLeftPaddingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.bottom.equalTo(self.containerView);
        make.width.equalTo(self.containerView.mas_width)
            .multipliedBy(radio)
            .offset(offset);
    }];

    // 创建名称标签
    self.nameLabel = [[NSTextField alloc] init];
    self.nameLabel.font = [NSFont systemFontOfSize:12];
    self.nameLabel.textColor = [NSColor whiteColor];
    [self.nameLabel setBezeled:NO];
    [self.nameLabel setDrawsBackground:NO];
    [self.nameLabel setEditable:NO];
    [self.nameLabel setSelectable:NO];
    self.nameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    self.nameLabel.alignment = NSTextAlignmentLeft;
    [self.nameLabel setAlphaValue:0];  // 初始透明度为0
    self.nameLabel.hidden = YES;  // 初始隐藏
    [self.containerView addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLeftPaddingView.mas_right);
        make.right.equalTo(self.containerView).offset(-8);
        make.centerY.equalTo(self.containerView);
    }];

    // 添加点击事件
    NSClickGestureRecognizer *clickGesture = [[NSClickGestureRecognizer alloc] initWithTarget:self action:@selector(handleClick:)];
    [self addGestureRecognizer:clickGesture];
}

- (void)setFrameSize:(NSSize)newSize {
    [super setFrameSize:newSize];

    // 计算展开比例（仿照 YYBMainNavigationItemView）
    self.nameLabel.alphaValue = (newSize.width - kNavigationIconSize) / (kNavigationExpandWidth - kNavigationDefaultWidth);
}

- (void)updateTrackingAreas {
    [super updateTrackingAreas];

    if (self.trackingArea) {
        [self removeTrackingArea:self.trackingArea];
    }

    NSTrackingAreaOptions options = NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingMouseMoved;
    self.trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                     options:options
                                                       owner:self
                                                    userInfo:nil];
    [self addTrackingArea:self.trackingArea];
}

// 更新视图状态
- (void)updateViewWithState:(YYBAppLaunchHistoryItemViewState)state {
    self.viewState = state;

    switch (state) {
        case YYBAppLaunchHistoryItemViewStateNormal:
            self.containerView.material = historyItemStateNormal;
            break;

        case YYBAppLaunchHistoryItemViewStateHover:
            self.containerView.material = historyItemStateHover;
            break;
    }
}

- (void)mouseEntered:(NSEvent *)event {
    [super mouseEntered:event];

    // 鼠标悬停效果
    [self updateViewWithState:YYBAppLaunchHistoryItemViewStateHover];
}

- (void)mouseExited:(NSEvent *)event {
    [super mouseExited:event];

    // 恢复正常效果
    [self updateViewWithState:YYBAppLaunchHistoryItemViewStateNormal];
}

- (void)handleClick:(NSClickGestureRecognizer *)gesture {
    // 点击效果
    if (self.apkInfo && self.apkInfo.pkgName) {
        YYBMacLogInfo(kTag, @"点击打开APP: %@", self.apkInfo.name);
        [[YYBApkPackage shared] openApp:self.apkInfo.pkgName];
    }
}

@end

@interface YYBAppLaunchHistoryView ()

@property (nonatomic, strong) NSTextField *collapsedTitleLabel;  // 合起状态标题标签（"启动"）
@property (nonatomic, strong) NSTextField *expandedTitleLabel;  // 展开状态标题标签（"快捷启动"）
@property (nonatomic, strong) NSMutableArray<YYBAppLaunchHistoryItemView *> *itemViews;
@property (nonatomic, strong) NSView *containerView;  // 容器视图
@property (nonatomic, strong) NSView *titleLeftPaddingView;  // 标题左侧占位视图，用于动画


@end


@implementation YYBAppLaunchHistoryView


- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        _expanded = NO; // 默认收起状态
        _itemViews = [NSMutableArray array];
        [self setupView];
        [self setupNotifications];
        [self loadInitialData];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}


- (void)setupView {
    self.wantsLayer = YES;
    self.layer.backgroundColor = [NSColor clearColor].CGColor;

    [self setupContainerView];
    [self setupTitleLabel];
    [self updateLayout];
}

- (void)setupContainerView {
    // 创建容器视图
    self.containerView = [[NSView alloc] init];
    [self addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];

    // 创建标题占位视图（仿照 YYBMainNavigationItemView）
    // 占位视图的宽度会根据containerView的宽度做线性变化，用于文字位移动画
    CGFloat radio = (46 - 40) / (kNavigationExpandWidth - kNavigationDefaultWidth);
    CGFloat offset = 40 - radio * kNavigationDefaultWidth;
    self.titleLeftPaddingView = [[NSView alloc] init];
    [self.containerView addSubview:self.titleLeftPaddingView];
    [self.titleLeftPaddingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.bottom.equalTo(self.containerView);
        make.width.equalTo(self.containerView.mas_width)
            .multipliedBy(radio)
            .offset(offset);
    }];
}

- (void)setupTitleLabel {
    // 创建合起状态标题标签（"启动"，居中）
    self.collapsedTitleLabel = [[NSTextField alloc] init];
    self.collapsedTitleLabel.stringValue = @"启动";
    self.collapsedTitleLabel.font = [NSFont systemFontOfSize:12 weight:NSFontWeightRegular];
    self.collapsedTitleLabel.textColor = [[NSColor whiteColor] colorWithAlphaComponent:0.45];
    [self.collapsedTitleLabel setBezeled:NO];
    [self.collapsedTitleLabel setDrawsBackground:NO];
    [self.collapsedTitleLabel setEditable:NO];
    [self.collapsedTitleLabel setSelectable:NO];
    self.collapsedTitleLabel.alignment = NSTextAlignmentCenter;
    [self.collapsedTitleLabel setAlphaValue:1.0];  // 初始显示
    self.collapsedTitleLabel.hidden = NO;  // 初始显示
    [self.containerView addSubview:self.collapsedTitleLabel];

    // 创建展开状态标题标签（"快捷启动"，跟随占位视图）
    self.expandedTitleLabel = [[NSTextField alloc] init];
    self.expandedTitleLabel.stringValue = @"快捷启动";
    self.expandedTitleLabel.font = [NSFont systemFontOfSize:12 weight:NSFontWeightRegular];
    self.expandedTitleLabel.textColor = [[NSColor whiteColor] colorWithAlphaComponent:0.45];
    [self.expandedTitleLabel setBezeled:NO];
    [self.expandedTitleLabel setDrawsBackground:NO];
    [self.expandedTitleLabel setEditable:NO];
    [self.expandedTitleLabel setSelectable:NO];
    self.expandedTitleLabel.alignment = NSTextAlignmentLeft;
    [self.expandedTitleLabel setAlphaValue:0];  // 初始隐藏
    self.expandedTitleLabel.hidden = YES;  // 初始隐藏
    [self.containerView addSubview:self.expandedTitleLabel];
}

- (void)setFrameSize:(NSSize)newSize {
    [super setFrameSize:newSize];

    // 计算展开比例
    CGFloat expandRatio = (newSize.width - kNavigationIconSize) / (kNavigationExpandWidth - kNavigationDefaultWidth);
    expandRatio = MAX(0, MIN(1, expandRatio));  // 限制在 0-1 之间

    // 根据展开比例调整两个标题标签的显示状态
    if (expandRatio > 0.5) {
        // 展开状态：显示"快捷启动"，隐藏"启动"
        self.collapsedTitleLabel.hidden = YES;
        [self.collapsedTitleLabel setAlphaValue:0];
        self.expandedTitleLabel.hidden = NO;
        [self.expandedTitleLabel setAlphaValue:1.0];
    } else {
        // 合起状态：显示"启动"，隐藏"快捷启动"
        self.collapsedTitleLabel.hidden = NO;
        [self.collapsedTitleLabel setAlphaValue:1.0];
        self.expandedTitleLabel.hidden = YES;
        [self.expandedTitleLabel setAlphaValue:0];
    }
}

// 切换展开和收起
- (void)setExpanded:(BOOL)expanded {
    if (_expanded != expanded) {
        _expanded = expanded;
    }
}

- (void)updateWithRecentApps:(NSArray<InstallApkInfo *> *)recentApps {
    // 清除旧的视图
    for (NSView *view in self.itemViews) {
        [view removeFromSuperview];
    }
    [self.itemViews removeAllObjects];

    // 最多显示3个
    NSInteger count = MIN(recentApps.count, 3);

    // 如果没有app item，隐藏整个view
    if (count == 0) {
        self.hidden = YES;
        [self updateLayout];
        return;
    } else {
        self.hidden = NO;
    }

    // 创建新的视图
    for (NSInteger i = 0; i < count; i++) {
        InstallApkInfo *apkInfo = recentApps[i];
        YYBMacLogInfo(kTag, @"刷新历史记录显示，获取到第 %ld 个最近使用的APP: %@", (long)(i+1), apkInfo.name);
        YYBAppLaunchHistoryItemView *itemView = [self createItemViewForApkInfo:apkInfo];

        [self.containerView addSubview:itemView];
        [self.itemViews addObject:itemView];
    }

    [self updateLayout];
}

#pragma mark - 通知和数据处理

- (void)setupNotifications {
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];

    // 监听APP打开通知
    [center addObserver:self
               selector:@selector(handleAppOpenedNotification:)
                   name:kYYBAppOpenedNotification
                 object:nil];

    // 监听APP卸载通知
    [center addObserver:self
               selector:@selector(handleAppUninstalledNotification:)
                   name:kYYBAppUninstalledNotification
                 object:nil];
}

- (void)loadInitialData {
    [self refreshHistoryDisplay];
}

- (void)refreshHistoryDisplay {
    // 获取最近的3个APP历史记录（从installedAppsMaps）
    NSArray<InstallApkInfo *> *recentApps = [[YYBApkPackage shared] getRecentAppsFromInstalledMaps:3];

    YYBMacLogInfo(kTag, @"刷新历史记录显示，获取到 %ld 个最近使用的APP", (long)recentApps.count);

    // 更新视图
    [self updateWithRecentApps:recentApps];
}

- (void)handleAppOpenedNotification:(NSNotification *)notification {
    InstallApkInfo *apkInfo = notification.userInfo[kYYBAppInfoKey];
    if (apkInfo) {
        YYBMacLogInfo(kTag, @"收到APP打开通知: %@", apkInfo.name);

        // 刷新历史记录显示
        dispatch_async(dispatch_get_main_queue(), ^{
            [self refreshHistoryDisplay];
        });
    }
}

- (void)handleAppUninstalledNotification:(NSNotification *)notification {
    InstallApkInfo *apkInfo = notification.userInfo[kYYBAppInfoKey];
    if (apkInfo) {
        YYBMacLogInfo(kTag, @"收到APP卸载通知: %@", apkInfo.name);

        // 刷新历史记录显示
        dispatch_async(dispatch_get_main_queue(), ^{
            [self refreshHistoryDisplay];
        });
    }
}


- (YYBAppLaunchHistoryItemView *)createItemViewForApkInfo:(InstallApkInfo *)apkInfo {
    YYBAppLaunchHistoryItemView *itemView = [[YYBAppLaunchHistoryItemView alloc] init];
    itemView.apkInfo = apkInfo;

    // 设置图标
    [itemView.iconView sd_setImageWithURL:[NSURL URLWithString:apkInfo.iconUrl]];

    // 设置名称
    itemView.nameLabel.stringValue = apkInfo.name ?: @"";

    return itemView;
}


- (void)updateLayout {

    if(self.itemViews.count == 0) {
        // view消失 - 只更新高度约束，不要清除其他约束。防止刷新向左偏移
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@0);
        }];
        return;
    }

    // 固定整个view的高度为164 - 只更新高度约束，不要清除其他约束。防止刷新向左偏移
    [self mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@(kFixedHeight));
    }];

    // 布局合起状态标题标签（居中）
    [self.collapsedTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.containerView);
        make.top.equalTo(self.containerView);
        make.height.equalTo(@20); // 标题标签高度
    }];

    // 布局展开状态标题标签（跟随占位视图）
    [self.expandedTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLeftPaddingView.mas_right);
        make.right.equalTo(self.containerView);
        make.top.equalTo(self.containerView);
        make.height.equalTo(@20); // 标题标签高度
    }];

    CGFloat itemHeight = self.expanded ? kItemHeight : kIconSize + 8;
    CGFloat startY = 20 + kTextLabelSpacing; // 标题高度 + 间隔

    // 布局app item，向上对齐，添加左右 Padding 为 8
    for (NSInteger i = 0; i < self.itemViews.count; i++) {
        YYBAppLaunchHistoryItemView *itemView = self.itemViews[i];
        [itemView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.containerView).offset(8);  // 左边距8
            make.right.equalTo(self.containerView).offset(-8);  // 右边距8
            make.height.equalTo(@(itemHeight));
            make.top.equalTo(self.containerView).offset(startY + i * (itemHeight + kItemSpacing));
        }];
    }

}




@end
