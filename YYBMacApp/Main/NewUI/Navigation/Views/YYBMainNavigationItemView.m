//
//  YYBMainNavigationItemView.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/14.
//

#import "YYBMainNavigationItemView.h"
#import "Masonry.h"
#import "NSFont+YYB.h"
#import "MainUIDefine.h"

// 定义导航项视图状态枚举
typedef NS_ENUM(NSUInteger, YYBNavigationItemViewState) {
    YYBNavigationItemViewStateNormal,   // 正常状态
    YYBNavigationItemViewStateHover,    // 鼠标悬停状态
    YYBNavigationItemViewStateSelected  // 选中状态
};

// 按钮毛玻璃效果
static NSVisualEffectMaterial const buttonStateNormal = NSVisualEffectMaterialUnderPageBackground;
static NSVisualEffectMaterial const buttonStateHover = NSVisualEffectMaterialMenu;
static NSVisualEffectMaterial const buttonStateSelected = NSVisualEffectMaterialHUDWindow;

@interface YYBMainNavigationItemView ()

@property (nonatomic, strong) NSImageView *iconImageView;
@property (nonatomic, strong) NSVisualEffectView *containerView;
@property (nonatomic, strong) NSTextField *label;
@property (nonatomic, strong) NSTrackingArea *trackingArea;
@property (nonatomic, assign) YYBNavigationItemViewState viewState;

@end

@implementation YYBMainNavigationItemView

- (instancetype)initWithItem:(YYBMainNavigationItem *)item {
    self = [self initWithFrame:NSZeroRect];
    if (self) {
        _item = item;
        [self setupViews];
        [self updateView];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (void)setFrameSize:(NSSize)newSize {
    [super setFrameSize:newSize];
    self.label.alphaValue = (newSize.width - kNavigationIconSize) / (kNavigationExpandWidth - kNavigationDefaultWidth);
}

- (void)setupViews {
    // 设置初始状态
    self.viewState = YYBNavigationItemViewStateNormal;
    
    // 创建容器视图
    self.containerView = [[NSVisualEffectView alloc] init];
    self.containerView.wantsLayer = YES;
    self.containerView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    self.containerView.state = NSVisualEffectStateActive;
    self.containerView.layer.cornerRadius = 10.0;
    self.containerView.layer.masksToBounds = YES;
    [self addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    // 创建图标视图
    NSView *iconView = [[NSView alloc] init];
    [self.containerView addSubview:iconView];
    [iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.equalTo(self);
        make.width.mas_equalTo(iconView.mas_height);
    }];
    self.iconImageView = [[NSImageView alloc] init];
    self.iconImageView.imageScaling = NSImageScaleProportionallyUpOrDown;
    [iconView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(iconView);
        make.width.height.mas_equalTo(20.0f);
    }];
    
    // 创建占位视图
    // 占位视图的宽度 = 文字的左边距，宽度会根据containerView的宽度做线性变化，用于文字位移动画
    CGFloat radio = (46 - 40) / (kNavigationExpandWidth - kNavigationDefaultWidth);
    CGFloat offset = 40 - radio * kNavigationDefaultWidth;
    NSView *labelLeftPaddingView = [[NSView alloc] init];
    [self.containerView addSubview:labelLeftPaddingView];
    [labelLeftPaddingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.bottom.equalTo(self.containerView);
        make.width.equalTo(self.containerView.mas_width)
            .multipliedBy(radio)
            .offset(offset);
    }];
    
    // 创建文字
    NSTextField *label = [[NSTextField alloc] initWithFrame:NSZeroRect];
    [label sizeToFit];
    label.stringValue = self.item.title;
    label.font = [NSFont PFMediumontWithsize:14];
    [label setBezeled:NO];
    [label setDrawsBackground:NO];
    [label setEditable:NO];
    [label setSelectable:NO];
    label.maximumNumberOfLines = 1;
    label.alignment = NSTextAlignmentCenter;
    [label setAlphaValue:0];
    self.label = label;
    [self.containerView addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(labelLeftPaddingView.mas_right);
        make.centerY.equalTo(self.containerView);
    }];
    
    // 添加点击事件
    NSClickGestureRecognizer *clickGesture = [[NSClickGestureRecognizer alloc] initWithTarget:self action:@selector(handleClick:)];
    [self addGestureRecognizer:clickGesture];
}

- (void)updateTrackingAreas {
    [super updateTrackingAreas];
    
    if (self.trackingArea) {
        [self removeTrackingArea:self.trackingArea];
    }
    
    NSTrackingAreaOptions options = NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingMouseMoved;
    self.trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                     options:options
                                                       owner:self
                                                    userInfo:nil];
    [self addTrackingArea:self.trackingArea];
}

#pragma mark - 属性设置方法

- (void)setItem:(YYBMainNavigationItem *)item {
    _item = item;
    [self updateView];
}

- (void)setSelected:(BOOL)selected {
    _selected = selected;
    
    // 根据选中状态更新视图状态
    YYBNavigationItemViewState newState = selected ? YYBNavigationItemViewStateSelected : YYBNavigationItemViewStateNormal;
    [self updateViewWithState:newState];
    
    // 更新图标
    [self updateIconForSelectedState];
}

// 更新视图状态
- (void)updateViewWithState:(YYBNavigationItemViewState)state {
    self.viewState = state;
    
    switch (state) {
        case YYBNavigationItemViewStateNormal:
            self.containerView.material = buttonStateNormal;
            break;
            
        case YYBNavigationItemViewStateHover:
            self.containerView.material = buttonStateHover;
            break;
            
        case YYBNavigationItemViewStateSelected:
            self.containerView.material = buttonStateSelected;
            break;
    }
}

#pragma mark - 视图更新

- (void)updateView {
    if (!self.item) {
        return;
    }
    
    self.selected = self.item.isSelected;
    YYBNavigationItemViewState newState = self.item.isSelected ?
        YYBNavigationItemViewStateSelected : YYBNavigationItemViewStateNormal;
    [self updateViewWithState:newState];
    [self updateIconForSelectedState];
    self.hidden = !self.item.isVisible;
}

- (void)updateIconForSelectedState {
    if (self.selected && self.item.selectedIcon) {
        self.iconImageView.image = self.item.selectedIcon;
    } else {
        self.iconImageView.image = self.item.icon;
    }
}

#pragma mark - 事件处理

- (void)handleClick:(NSClickGestureRecognizer *)gesture {
    if ([self.delegate respondsToSelector:@selector(navigationItemViewDidClick:)]) {
        [self.delegate navigationItemViewDidClick:self];
    }
}

- (void)mouseEntered:(NSEvent *)event {
    [super mouseEntered:event];
    
    // 鼠标悬停效果
    if (!self.selected) {
        [self updateViewWithState:YYBNavigationItemViewStateHover];
    }
}

- (void)mouseExited:(NSEvent *)event {
    [super mouseExited:event];
    
    // 恢复正常效果
    if (!self.selected) {
        [self updateViewWithState:YYBNavigationItemViewStateNormal];
    }
}

@end
