//
//  YYBMainTopbarView.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/13.
//

#import "YYBMainTopbarView.h"
#import "Masonry.h"
#import "YYBTopbarItemView.h"
#import "YYBTopbarBackButton.h"
#import "YYBTopbarSearchBar.h"

static CGFloat const buttonSize = 40.0;
static CGFloat const itemSpacing = 16.0;

@interface YYBMainTopbarView () <YYBTopbarItemViewDelegate, YYBTopbarBackButtonDelegate, YYBTopbarSearchBarDelegate>

@property (nonatomic, strong, readwrite) YYBTopbarBackButton *backButton;
@property (nonatomic, strong, readwrite) YYBTopbarSearchBar *searchBar;
@property (nonatomic, strong, readwrite) YYBTopbarModel *topbarModel;
@property (nonatomic, strong) NSMutableArray<YYBTopbarItemView *> *itemViews;

@end

@implementation YYBMainTopbarView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        _itemViews = [NSMutableArray array];
        [self setupView];
    }
    return self;
}

- (void)setDataSource:(id<YYBMainTopbarViewDataSource>)dataSource {
    _dataSource = dataSource;
    [self reloadData];
}

- (void)setupView {
    self.wantsLayer = YES;
    self.layer.backgroundColor = [NSColor clearColor].CGColor;
}

- (void)setupSubviews {
    // 返回按钮
    if (self.topbarModel.showBackButton) {
        [self setupBackButton];
    }
    
    // 搜索框
    if (self.topbarModel.showSearchField) {
        [self setupSearchField];
    }
    
    // 工具栏项
    [self setupToolbarItems];
}

- (void)setupBackButton {
    [self.backButton removeFromSuperview];
    
    self.backButton = [[YYBTopbarBackButton alloc] initWithFrame:NSZeroRect];
    self.backButton.delegate = self;
    NSImage *backImage = [NSImage imageNamed:@"Main/TopBar/BackIcon"];
    [self.backButton setIcon:backImage];
    self.backButton.wantsLayer = YES;
    self.backButton.layer.cornerRadius = buttonSize / 2;
    [self addSubview:self.backButton];
    
    [self.backButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(12);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(buttonSize, buttonSize));
    }];
}

- (void)setupSearchField {
    [self.searchBar removeFromSuperview];
    
    self.searchBar = [[YYBTopbarSearchBar alloc] initWithFrame:NSZeroRect];
    self.searchBar.delegate = self;
    [self.searchBar setPlaceholder:@"全局搜索"];
    [self addSubview:self.searchBar];
    
    [self.searchBar mas_makeConstraints:^(MASConstraintMaker *make) {
        if (self.backButton) {
            make.left.equalTo(self.backButton.mas_right).offset(10);
        } else {
            make.left.equalTo(self).offset(0);
        }
        make.centerY.equalTo(self);
        make.width.equalTo(@400);
        make.height.equalTo(@40);
    }];
}

- (void)setupToolbarItems {
    // 清除现有的工具栏项视图
    for (YYBTopbarItemView *itemView in self.itemViews) {
        [itemView removeFromSuperview];
    }
    [self.itemViews removeAllObjects];
    
    // 创建工具栏项视图
    NSMutableArray<YYBTopbarItemView *> *visibleItemViews = [NSMutableArray array];
    
    for (YYBTopbarItem *item in self.topbarModel.items) {
        if (item.visible) {
            YYBTopbarItemView *itemView = [[YYBTopbarItemView alloc] initWithItem:item];
            itemView.delegate = self;
            [itemView setCornerRadius:buttonSize / 2];
            [self addSubview:itemView];
            [self.itemViews addObject:itemView];
            [visibleItemViews addObject:itemView];
        }
    }
    
    // 布局工具栏项视图
    if (visibleItemViews.count > 0) {
        YYBTopbarItemView *lastItemView = visibleItemViews.lastObject;
        [lastItemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self).offset(-30);
            make.centerY.equalTo(self);
            make.size.mas_equalTo(CGSizeMake(buttonSize, buttonSize));
        }];
        
        for (NSInteger i = visibleItemViews.count - 2; i >= 0; i--) {
            YYBTopbarItemView *itemView = visibleItemViews[i];
            YYBTopbarItemView *nextItemView = visibleItemViews[i + 1];
            
            [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(nextItemView.mas_left).offset(-itemSpacing);
                make.centerY.equalTo(self);
                make.size.mas_equalTo(CGSizeMake(buttonSize, buttonSize));
            }];
        }
    }
}

#pragma mark - 公共方法

- (void)setBackButtonEnabled:(BOOL)enabled {
    [self.backButton setEnabled:enabled];
}

- (void)reloadData {
    if (self.dataSource && [self.dataSource respondsToSelector:@selector(topbarModelForTopbarView:)]) {
        self.topbarModel = [self.dataSource topbarModelForTopbarView:self];
    }
    [self setupSubviews];
}

#pragma mark - YYBTopbarBackButtonDelegate

- (void)topbarBackButtonDidClick:(YYBTopbarBackButton *)backButton {
    if ([self.delegate respondsToSelector:@selector(topbarViewDidClickBackButton:)]) {
        [self.delegate topbarViewDidClickBackButton:self];
    }
}

#pragma mark - YYBTopbarItemViewDelegate

- (void)topbarItemViewDidClick:(YYBTopbarItemView *)itemView {
    if ([self.delegate respondsToSelector:@selector(topbarView:didClickItem:)]) {
        [self.delegate topbarView:self didClickItem:itemView.item];
    }
    
    // 不再需要兼容旧的代理方法，所有点击事件都通过topbarView:didClickItem:方法处理
}

#pragma mark - YYBTopbarSearchBarDelegate

- (void)searchBar:(YYBTopbarSearchBar *)searchBar textDidChange:(NSString *)searchText {
    if ([self.delegate respondsToSelector:@selector(topbarView:searchTextDidChange:)]) {
        [self.delegate topbarView:self searchTextDidChange:searchText];
    }
}

- (void)searchBarSearchButtonClicked:(YYBTopbarSearchBar *)searchBar {
    if ([self.delegate respondsToSelector:@selector(topbarView:didSubmitSearchText:)]) {
        [self.delegate topbarView:self didSubmitSearchText:searchBar.searchText];
    }
}

@end
