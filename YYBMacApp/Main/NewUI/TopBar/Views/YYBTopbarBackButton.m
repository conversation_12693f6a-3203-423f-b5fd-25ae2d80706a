//
//  YYBTopbarBackButton.m
//  YYBMacApp
//
//  Created by CodeBuddy on 2025/8/19.
//

#import "YYBTopbarBackButton.h"
#import "Masonry.h"

typedef NS_ENUM(NSUInteger, YYBTopbarBackButtonState) {
    YYBTopbarBackButtonStateNormal = 1,
    YYBTopbarBackButtonStateHover,
    YYBTopbarBackButtonStateSelected,
    YYBTopbarBackButtonStateDisabled,
};

@interface YYBTopbarBackButton ()

@property (nonatomic, strong) NSVisualEffectView *backgroundView;
@property (nonatomic, strong) NSImageView *iconView;
@property (nonatomic, assign) YYBTopbarBackButtonState buttonState;
@property (nonatomic, assign) BOOL isEnabled;
@property (nonatomic, strong) NSTrackingArea *trackingArea;

@end

@implementation YYBTopbarBackButton

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupView];
        [self setupSubviews];
        [self setEnabled:NO];
    }
    return self;
}

- (void)setupView {
    self.wantsLayer = YES;
    self.layer.masksToBounds = YES;
}

- (void)setupSubviews {
    // 创建背景视图
    self.backgroundView = [[NSVisualEffectView alloc] initWithFrame:self.bounds];
    self.backgroundView.blendingMode = NSVisualEffectBlendingModeWithinWindow;
    self.backgroundView.state = NSVisualEffectStateActive;
    self.backgroundView.wantsLayer = YES;
    [self addSubview:self.backgroundView];
    [self.backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    // 创建图标视图
    self.iconView = [[NSImageView alloc] initWithFrame:NSZeroRect];
    self.iconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    [self addSubview:self.iconView];
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.width.height.equalTo(@16);
    }];
}


- (void)setCornerRadius:(CGFloat)cornerRadius {
    self.layer.cornerRadius = cornerRadius;
    self.backgroundView.layer.cornerRadius = cornerRadius;
}

- (void)updateState:(YYBTopbarBackButtonState)state {
    if (_buttonState == state) {
        return;
    }
    _buttonState = state;
    switch (state) {
        case YYBTopbarBackButtonStateNormal:
            self.backgroundView.material = NSVisualEffectMaterialSelection;
            self.backgroundView.emphasized = NO;
            self.alphaValue = 1;
            break;
            
        case YYBTopbarBackButtonStateHover:
            self.backgroundView.material = NSVisualEffectMaterialMenu;
            self.backgroundView.emphasized = NO;
            self.alphaValue = 1;
            break;
            
        case YYBTopbarBackButtonStateSelected:
            self.backgroundView.material = NSVisualEffectMaterialSelection;
            self.backgroundView.emphasized = YES;
            self.alphaValue = 1;
            break;
            
        case YYBTopbarBackButtonStateDisabled:
            self.backgroundView.material = NSVisualEffectMaterialSelection;
            self.backgroundView.emphasized = NO;
            self.alphaValue = 0.5;
            break;
    }
}

- (void)setIcon:(NSImage *)icon {
    self.iconView.image = icon;
}

- (void)setEnabled:(BOOL)enabled {
    _isEnabled = enabled;
    YYBTopbarBackButtonState buttonState = enabled ? YYBTopbarBackButtonStateNormal : YYBTopbarBackButtonStateDisabled;
    [self updateState:buttonState];
}

#pragma mark - 鼠标事件处理

- (void)updateTrackingAreas {
    [super updateTrackingAreas];
    
    if (self.trackingArea) {
        [self removeTrackingArea:self.trackingArea];
    }
    
    NSTrackingAreaOptions options = NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingMouseMoved;
    self.trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                     options:options
                                                       owner:self
                                                    userInfo:nil];
    [self addTrackingArea:self.trackingArea];
}

- (void)mouseEntered:(NSEvent *)event {
    [super mouseEntered:event];
    if (self.isEnabled) {
        [self updateState:YYBTopbarBackButtonStateHover];
    }
}

- (void)mouseExited:(NSEvent *)event {
    [super mouseExited:event];
    if (self.isEnabled) {
        [self updateState:YYBTopbarBackButtonStateNormal];
    }
}

- (void)mouseDown:(NSEvent *)event {
    [super mouseDown:event];
    if (self.isEnabled) {
        [self updateState:YYBTopbarBackButtonStateSelected];
    }
}

- (void)mouseUp:(NSEvent *)event {
    [super mouseUp:event];
    
    if (!self.isEnabled) {
        return;
    }
    
    NSPoint point = [self convertPoint:event.locationInWindow fromView:nil];
    if (NSPointInRect(point, self.bounds)) {
        [self updateState:YYBTopbarBackButtonStateHover];
        
        // 通知代理点击事件
        if ([self.delegate respondsToSelector:@selector(topbarBackButtonDidClick:)]) {
            [self.delegate topbarBackButtonDidClick:self];
        }
    } else {
        [self updateState:YYBTopbarBackButtonStateNormal];
    }
}

@end
