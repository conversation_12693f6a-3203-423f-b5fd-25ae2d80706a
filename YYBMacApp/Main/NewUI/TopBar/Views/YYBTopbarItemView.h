//
//  YYBTopbarItemView.h
//  YYBMacApp
//
//  Created by halehuang on 2025/8/18.
//

#import <Cocoa/Cocoa.h>
#import "YYBTopbarItem.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * 顶部工具栏项视图状态
 */
typedef NS_ENUM(NSInteger, YYBTopbarItemViewState) {
    YYBTopbarItemViewStateNormal = 1,    // 正常状态
    YYBTopbarItemViewStateHover,         // 悬停状态
    YYBTopbarItemViewStatePressed,       // 按下状态
};

@class YYBTopbarItemView;

/**
 * 顶部工具栏项视图代理协议
 */
@protocol YYBTopbarItemViewDelegate <NSObject>

@optional
/**
 * 点击工具栏项视图时调用
 */
- (void)topbarItemViewDidClick:(YYBTopbarItemView *)itemView;

@end

/**
 * 顶部工具栏项视图
 */
@interface YYBTopbarItemView : NSView

/**
 * 工具栏项
 */
@property (nonatomic, strong, readonly) YYBTopbarItem *item;

/**
 * 当前状态
 */
@property (nonatomic, assign) YYBTopbarItemViewState state;

/**
 * 代理对象
 */
@property (nonatomic, weak) id<YYBTopbarItemViewDelegate> delegate;

/**
 * 使用工具栏项初始化
 */
- (instancetype)initWithItem:(YYBTopbarItem *)item;

/**
 * 更新视图状态
 */
- (void)updateState:(YYBTopbarItemViewState)state;

/**
 * 设置圆角
 */
- (void)setCornerRadius:(CGFloat)cornerRadius;

@end

NS_ASSUME_NONNULL_END
