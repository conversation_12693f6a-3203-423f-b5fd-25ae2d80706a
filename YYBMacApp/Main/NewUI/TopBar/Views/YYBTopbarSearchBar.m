//
//  YYBTopbarSearchBar.m
//  YYBMacApp
//
//  Created by CodeBuddy on 2025/8/19.
//

#import "YYBTopbarSearchBar.h"
#import "Masonry.h"
#import "NSColor+Utils.h"

@interface YYBTopbarSearchBar () <NSTextFieldDelegate>

@property (nonatomic, strong) NSTextField *searchField;
@property (nonatomic, strong) NSImageView *searchIconView;
@property (nonatomic, copy) NSString *searchText;

@end

@implementation YYBTopbarSearchBar

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // 设置视觉效果视图属性
    self.material = NSVisualEffectMaterialSelection;
    self.blendingMode = NSVisualEffectBlendingModeWithinWindow;
    self.wantsLayer = YES;
    self.layer.cornerRadius = 20.0;
    
    // 创建搜索图标
    _searchIconView = [[NSImageView alloc] initWithFrame:NSZeroRect];
    _searchIconView.imageScaling = NSImageScaleProportionallyDown;
    NSImage *searchIcon = [NSImage imageNamed:@"Main/TopBar/SearchIcon"];
    if (searchIcon) {
        _searchIconView.image = searchIcon;
    }
    [self addSubview:_searchIconView];
    
    // 创建搜索文本框
    _searchField = [[NSTextField alloc] initWithFrame:NSZeroRect];
    _searchField.delegate = self;
    _searchField.bezeled = NO;
    _searchField.drawsBackground = NO;
    _searchField.editable = YES;
    _searchField.selectable = YES;
    _searchField.textColor = [NSColor whiteColor];
    _searchField.font = [NSFont systemFontOfSize:14.0];
    _searchField.placeholderString = @"搜索";
    
    // 设置文本框背景为透明
    [_searchField setBackgroundColor:[NSColor clearColor]];
    
    // 添加到视图
//    [self addSubview:_searchField];
    
    // 使用Masonry设置约束
    [_searchIconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-20);
        make.centerY.equalTo(self);
        make.width.height.equalTo(@16);
    }];
    
//    [_searchField mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.equalTo(_searchIconView.mas_right).offset(8);
//        make.right.equalTo(self).offset(-12);
//        make.centerY.equalTo(self);
//        make.height.equalTo(@24);
//    }];
}

#pragma mark - NSTextFieldDelegate

- (void)controlTextDidChange:(NSNotification *)notification {
    NSTextField *textField = [notification object];
    if (textField == self.searchField) {
        self.searchText = textField.stringValue;
        if ([self.delegate respondsToSelector:@selector(searchBar:textDidChange:)]) {
            [self.delegate searchBar:self textDidChange:self.searchText];
        }
    }
}

- (void)controlTextDidBeginEditing:(NSNotification *)notification {
    NSTextField *textField = [notification object];
    if (textField == self.searchField) {
        if ([self.delegate respondsToSelector:@selector(searchBarDidBeginEditing:)]) {
            [self.delegate searchBarDidBeginEditing:self];
        }
    }
}

- (void)controlTextDidEndEditing:(NSNotification *)notification {
    NSTextField *textField = [notification object];
    if (textField == self.searchField) {
        if ([self.delegate respondsToSelector:@selector(searchBarDidEndEditing:)]) {
            [self.delegate searchBarDidEndEditing:self];
        }
    }
}

- (BOOL)control:(NSControl *)control textView:(NSTextView *)textView doCommandBySelector:(SEL)commandSelector {
    if (commandSelector == @selector(insertNewline:)) {
        if ([self.delegate respondsToSelector:@selector(searchBarSearchButtonClicked:)]) {
            [self.delegate searchBarSearchButtonClicked:self];
        }
        return YES;
    }
    return NO;
}

#pragma mark - Public Methods

- (void)setPlaceholder:(NSString *)placeholder {
    self.searchField.placeholderString = placeholder;
}

- (void)clearSearchText {
    self.searchField.stringValue = @"";
    self.searchText = @"";
    if ([self.delegate respondsToSelector:@selector(searchBar:textDidChange:)]) {
        [self.delegate searchBar:self textDidChange:self.searchText];
    }
}

- (void)setEditable:(BOOL)editable {
    self.searchField.editable = editable;
}

@end
