//
//  YYBTVKVideo.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/20.
//

#import "YYBTVKVideo.h"


#import <TVKPlayerForMac/TVKMediaPlaybackDelegate.h>
#import <TVKPlayerForMac/TVKVideoView.h>
#import <TVKPlayerForMac/TVKMediaPlayer.h>
#import <TVKPlayerForMac/TVKMediaPlayInfo.h>
#import <TVKPlayerForMac/TVKNetVideoInfo.h>
#import <TVKPlayerForMac/TVKAudioFxProcessor.h>
#import <TVKPlayerForMac/TVKAudioFxFactory.h>
#import <TVKPlayerForMac/TVKAssetFactory.h>
#import <TVKPlayerForMac/ITVKAsset.h>
#import <TVKPlayerForMac/ITVKVideoFx.h>
#import <TVKPlayerForMac/ITVKColorBlindnessFx.h>
#import <TVKPlayerForMac/TVKVideoFxFactory.h>
#import <TVKPlayerForMac/ITVKVideoFxProcessor.h>
#import <TVKPlayerForMac/ITVKVRFx.h>
#import <TVKPlayerForMac/TVKReportEvent.h>
#import <TVKPlayerForMac/TVKPreloadManager.h>
@interface YYBTVKVideo ()<TVKMediaPlaybackDelegate, TVKReportEventDelegate>
@property (nonatomic, strong) YYBVideoModel* video;
@property (nonatomic, strong) NSView* playerView;

// player
@property (nonatomic, strong) TVKMediaInfo *mediaInfo;
@property (nonatomic, strong) TVKNetVideoInfo *netVideoInfo;
@property (nonatomic, strong) id<ITVKMediaPlayer> mediaPlayer;
@property (nonatomic, strong) TVKVideoView *videoView;
@property (nonatomic, strong) NSView *superView;
@end

@implementation YYBTVKVideo
- (instancetype)initWithVideo:(YYBVideoModel *)video withSuperView:(NSView *)superView {
    self = [super init];
    if (self) {
        self.video = video;
        self.superView = superView;
        _playerView.wantsLayer = YES;
        _playerView.layer = [CALayer layer];
        _playerView.layer.backgroundColor = [NSColor blackColor].CGColor;
        [self initPlayerUI];
    }
    return self;
}

- (void)initPlayerUI {
    self.videoView = [[TVKVideoView alloc] initWithFrame:_playerView.bounds];

    [_playerView addSubview:_videoView];
    [_playerView addSubview:_superView positioned:NSWindowBelow relativeTo:_videoView];
    
    // 调用播放器流程. 起播播放器有6个重要步骤，此处是起播设置的5个步骤，
    // 在播放回调中，播放状态为TVKMediaPlayerStatePrepared时，调用play，正片即可播放

    // 1.创建播放器
    self.mediaPlayer = [self createPlayer];

    // 2.设置播放view
    self.mediaPlayer.videoView = _videoView;
    self.mediaPlayer.backgroundPlayerView = self.superView;

    // 3.设置播放回调的代理
    self.mediaPlayer.playbackDelegate = self;

    // 4.创建播放的视频信息
    self.mediaInfo = [self createMeidaInfo];
    // 5.调用播放,用户信息参考TVKUserInfo.h 可不填
    TVKUserInfo *userInfo = [[TVKUserInfo alloc] init];
    userInfo.cookie = @"";
    [self.mediaPlayer addReportEventDelegate:self];
    [self.mediaPlayer openMediaPlayerWithMediaInfo:self.mediaInfo userInfo:userInfo];
//    TVKPreloadParam *preloadParam = [[TVKPreloadParam alloc] init];
//    preloadParam.preloadDurationMs = 30 * 1000;
//    int preloadId = [TVKPreloadManager preloadWithMediaInfo:self.mediaInfo userInfo:userInfo preloadParam:preloadParam preloadDelegate:self];
    [self.mediaPlayer informRealTimeInfoChangedWithInfoKey:TVKRealTimeInfoKeyEnableAutoPictureInPicture infoValue:@(1)]; //开启自动画中画功能
    [self SetSubtitleRenderParams];

}

- (void)updateProgressAction:(id)sender {
    NSSlider *progressSlider = (NSSlider *)sender;
    if (progressSlider.doubleValue < 0 || progressSlider.doubleValue > 100) {
        return;
    }
    NSTimeInterval targetTime = self.mediaPlayer.duration * progressSlider.doubleValue / 100;
    [self seekToTime:targetTime];
}

- (TVKMediaPlayer *)createPlayer {
    // 创建播放器
    TVKMediaPlayer *player = [TVKMediaPlayer mediaPlayer];  //使用类方法创建,不要使用alloc创建
    player.progressInterval = 1.0;
    [(id<ITVKVideoView>)player.videoView removeAllSubviews];
    return player;
}

#pragma mark - 播放创建和起播流程
- (TVKMediaInfo *)createMeidaInfo {
    NSURL *url = [NSURL URLWithString:@"https://cdn.yyb.gtimg.com/xy/ehe/5LCaoW4P.mp4"];
    id<ITVKAsset> asset = [TVKAssetFactory createUrlAssetWithUrl:url];
    TVKMediaInfo *videoInfo = [[TVKMediaInfo alloc] initWithAsset:asset];
//    videoInfo.playType = TVKPlayTypeExternalUrl;
    videoInfo.startPosition = 0;
    videoInfo.configMap = @{kTVKMediaInfoConfigMapKeyEnableOpenPictureInPictureSeamless: @"1"};
    return videoInfo;
}

- (void)SetSubtitleRenderParams {
    id<ITVKSubtitleRendererController> subtitleController = self.mediaPlayer.subtitleRenderController;
    TVKSubtitleRenderParams *renderParams = [[TVKSubtitleRenderParams alloc] init];
    renderParams.fontScale = 2.0;
    renderParams.paramFlags = renderParams.paramFlags | TVKSubtitleParamFlagFontScale;
    renderParams.paramPriorityFlags = renderParams.paramPriorityFlags | TVKSubtitleParamFlagFontScale;
    [subtitleController setRenderParam:renderParams];
}

- (void)seekToTime:(NSTimeInterval)time {
    [self.mediaPlayer seekTo:time];
}

// 播放状态回调，页面可以根据播放状态进行相应的调整。
// 注意，调用openMediaPlayer后，播放器状态在变为TVKMediaPlayerStatePrepared后调用start才能起播
- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer stateChanged:(TVKMediaPlayerState)state withError:(NSError *)error {
    // 重要!!! 处理UI切换到主线程
    dispatch_async(dispatch_get_main_queue(), ^{
        switch (state) {
            case TVKMediaPlayerStateUnknown:  // 初始状态
            {
                break;
            }
            case TVKMediaPlayerStatePreparing:  // 正片获取信息中
            {
                NSLog(@"loading stated:%u", state);
                break;
            }
            case TVKMediaPlayerStatePrepared:  // 重要!!!正片播放准备完毕
            {
                NSLog(@"prepared stated:%u", state);
                [self.mediaPlayer play];  // 6.调用播放，正式起播
                break;
            }
            case TVKMediaPlayerStatePlaying:  // 正片播放中
            {
                NSLog(@"playing stated:%u", state);
                break;
            }
            case TVKMediaPlayerStateUserPaused:  // 正片播放用户行为导致暂停
            {
                NSLog(@"paused stated:%u", state);
                break;
            }
            case TVKMediaPlayerStateStopped:  // 正片播放停止(用户主动调用停止后，进入此状态)
            {
                NSLog(@"stopped stated:%u", state);
                break;
            }
            case TVKMediaPlayerStateComplete:  // 正片播放完毕
            {
                NSLog(@"complete stated:%u", state);
                break;
            }
            case TVKMediaPlayerStateError:  // 正片播放失败
            {
                NSLog(@"error stated:%u", state);
                break;
            }
            default:
                break;
        }
    });
}

- (void)mediaPlayer:(TVKMediaPlayer *)mediaPlayer NetVideoInfo:(TVKNetVideoInfo *)netVideoInfo {
    NSLog(@"netVideoInfo: %@" , netVideoInfo);
}

- (void)mediaPlayer:(id<ITVKMediaPlayer>)mediaPlayer reportEvent:(TVKReportEvent)event withParams:(TVKReportEventParams *)params {
    NSLog(@"playerdemo report event: event:%@, params: %@" , @(event), params);
}

- (void)onPreloadDownloadProgressUpdateWithPreloadId:(int)preloadId downloadProgressInfo:(TVKDownloadProgressInfo *)downloadProgressInfo {
    NSLog(@"preload progress update, preloadId: %d progressInfo:%@", preloadId, downloadProgressInfo);
}

- (void)onPreloadErrorWithPreloadId:(int)preloadId error:(NSError *)error {
    NSLog(@"preload error, preloadId: %d, error: %@", preloadId,error);
}

- (void)onPreloadSuccessWithPreloadId:(int)preloadId {
    NSLog(@"preload suceess, preloadId: %d", preloadId);
}

@end
