//
//  YYBTopView.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/19.
//

#import "YYBTopView.h"
#import "YYBVideoManager.h"
#import "YYBTPVideo.h"
#import "Masonry.h"
#import "YYBComponent.h"

@interface YYBTopView ()

@property (nonatomic, strong) NSView *playerView;
@property (nonatomic, strong)YYBTPVideo * player;

@end

@implementation YYBTopView
- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        YYBVideoModel *video = [[YYBVideoModel alloc] init];
        video.url = @"https://cdn.yyb.gtimg.com/xy/ehe/5LCaoW4P.mp4";
        self.playerView = [[NSView alloc] initWithFrame:self.bounds];
        self.playerView.wantsLayer = YES;
        [self addSubview:self.playerView];
#if TVK_USE
        
#else
        self.player = [[YYBTPVideo alloc] initWithVideo:video withSuperView:self.playerView];
#endif
        [self.playerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self);
        }];
    }
    return self;
}

- (void)configureWithComponent:(YYBComponent *)component {
    
}

@end
