//
//  YYBGlassEffectView.h
//  LiquidGrass
//
//  Created by halehuang on 2025/8/19.
//

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

#if defined(MAC_OS_VERSION_26_0)

@interface NSGlassEffectView (YYB)

/**
 * 设置玻璃效果视图的变体
 * @param variant 变体值，范围通常为0-19
 */
- (void)yyb_setVariant:(NSInteger)variant;

/**
 * 设置玻璃效果视图的纱幕状态
 * @param scrimState YES表示启用纱幕效果，NO表示禁用
 */
- (void)yyb_setScrimState:(BOOL)scrimState;

/**
 * 设置玻璃效果视图的柔和状态
 * @param subduedState YES表示启用柔和效果，NO表示禁用
 */
- (void)yyb_setSubduedState:(BOOL)subduedState;

/**
 * 设置玻璃效果视图的交互状态
 * @param interactionState YES表示启用交互效果，NO表示禁用
 */
- (void)yyb_setInteractionState:(BOOL)interactionState;

/**
 * 设置玻璃效果视图的内容透镜效果
 * @param lensing 透镜效果值，范围通常为0.0-1.0
 */
- (void)yyb_setContentLensing:(float)lensing;

/**
 * 设置玻璃效果视图的自定义路径
 * @param path 自定义路径，传入NULL可还原默认路径
 */
- (void)yyb_setPath:(CGPathRef)path;

- (void)yyb_setSubvariant:(NSString *)subvariant;

@end

#endif

NS_ASSUME_NONNULL_END

