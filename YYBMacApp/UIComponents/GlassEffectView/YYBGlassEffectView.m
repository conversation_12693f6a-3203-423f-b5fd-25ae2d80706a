//
//  YYBGlassEffectView.m
//  LiquidGrass
//
//  Created by halehuang on 2025/8/19.
//

#import "YYBGlassEffectView.h"
#import <objc/runtime.h>
#import <objc/message.h>

#if defined(MAC_OS_VERSION_26_0)

@implementation NSGlassEffectView (YYB)

- (void)yyb_setVariant:(NSInteger)variant {
    SEL selector = NSSelectorFromString(@"set_variant:");
    if ([self respondsToSelector:selector]) {
        Method method = class_getInstanceMethod([self class], selector);
        typedef void (*VariantSetterIMP)(id, SEL, NSInteger);
        VariantSetterIMP imp = (VariantSetterIMP)method_getImplementation(method);
        imp(self, selector, variant);
    } else {
        NSLog(@"警告: set_variant: 方法不可用");
    }
}

- (void)yyb_setScrimState:(BOOL)scrimState {
    SEL selector = NSSelectorFromString(@"set_scrimState:");
    if ([self respondsToSelector:selector]) {
        Method method = class_getInstanceMethod([self class], selector);
        typedef void (*ScrimSetterIMP)(id, SEL, BOOL);
        ScrimSetterIMP imp = (ScrimSetterIMP)method_getImplementation(method);
        imp(self, selector, scrimState);
    } else {
        NSLog(@"警告: set_scrimState: 方法不可用");
    }
}

- (void)yyb_setSubduedState:(BOOL)subduedState {
    SEL selector = NSSelectorFromString(@"set_subduedState:");
    if ([self respondsToSelector:selector]) {
        Method method = class_getInstanceMethod([self class], selector);
        typedef void (*SubduedSetterIMP)(id, SEL, BOOL);
        SubduedSetterIMP imp = (SubduedSetterIMP)method_getImplementation(method);
        imp(self, selector, subduedState);
    } else {
        NSLog(@"警告: set_subduedState: 方法不可用");
    }
}

- (void)yyb_setInteractionState:(BOOL)interactionState {
    SEL selector = NSSelectorFromString(@"set_interactionState:");
    if ([self respondsToSelector:selector]) {
        Method method = class_getInstanceMethod([self class], selector);
        typedef void (*InteractionSetterIMP)(id, SEL, BOOL);
        InteractionSetterIMP imp = (InteractionSetterIMP)method_getImplementation(method);
        imp(self, selector, interactionState);
    } else {
        NSLog(@"警告: set_interactionState: 方法不可用");
    }
}

- (void)yyb_setContentLensing:(float)lensing {
    SEL selector = NSSelectorFromString(@"set_contentLensing:");
    if ([self respondsToSelector:selector]) {
        Method method = class_getInstanceMethod([self class], selector);
        typedef void (*LensingSetterIMP)(id, SEL, float);
        LensingSetterIMP imp = (LensingSetterIMP)method_getImplementation(method);
        imp(self, selector, lensing);
    } else {
        NSLog(@"警告: set_contentLensing: 方法不可用");
    }
}

- (void)yyb_setSubvariant:(NSString *)subvariant {
    SEL selector = NSSelectorFromString(@"set_subvariant:");
    if ([self respondsToSelector:selector]) {
        Method method = class_getInstanceMethod([self class], selector);
        typedef void (*SubvariantSetterIMP)(id, SEL, NSString *);
        SubvariantSetterIMP imp = (SubvariantSetterIMP)method_getImplementation(method);
        imp(self, selector, subvariant);
    } else {
        NSLog(@"警告: set_subvariant: 方法不可用");
    }
}

- (void)yyb_setPath:(CGPathRef)path {
    SEL selector = NSSelectorFromString(@"_setPath:");
    if ([self respondsToSelector:selector]) {
        Method method = class_getInstanceMethod([self class], selector);
        typedef void (*PathSetterIMP)(id, SEL, CGPathRef);
        PathSetterIMP imp = (PathSetterIMP)method_getImplementation(method);
        imp(self, selector, path);
    } else {
        NSLog(@"警告: _setPath: 方法不可用");
    }
}

@end

#endif
