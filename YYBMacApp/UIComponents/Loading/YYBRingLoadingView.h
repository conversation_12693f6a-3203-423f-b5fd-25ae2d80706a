//
//  YYBRingLoadingView.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/20.
//

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

@interface YYBRingLoadingView : NSView

/// 设置loading圆圈尺寸，代码/布局自适应。动画自动保持中间位置
@property (nonatomic, assign) CGFloat ringSize; // 默认24

/// 开始动画（自动show）
- (void)startAnimating;
/// 停止动画（自动hide）
- (void)stopAnimating;
/// 判断当前loading是否在动画
- (BOOL)isAnimating;

@end

NS_ASSUME_NONNULL_END
