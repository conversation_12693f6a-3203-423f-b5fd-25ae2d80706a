//
//  YYBRingLoadingView.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/20.
//

#import "YYBRingLoadingView.h"
#import <QuartzCore/QuartzCore.h>
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kLogTag = @"YYBRingLoadingView";

@interface YYBRingLoadingView ()

@property (nonatomic, strong) CALayer *arcLayer;
@property (nonatomic, assign) BOOL animating;
@property (nonatomic, assign) BOOL clockwise;  // 是否顺时针

@end

@implementation YYBRingLoadingView

#pragma mark - Init

- (instancetype)initWithFrame:(NSRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.wantsLayer = YES;
        _ringSize = 24;
        self.clockwise = NO; // 默认逆针
        
        _arcLayer = [CALayer layer];
        _arcLayer.contentsGravity = kCAGravityResizeAspect;
        [self.layer addSublayer:_arcLayer];
        
        self.animating = NO;
        [self setHidden:YES];
    }
    return self;
}

- (void)dealloc {
    YYBMacLogInfo(kLogTag, @"[dealloc] YYBRingLoadingView released.");
}

#pragma mark - Size & Layout

- (void)setRingSize:(CGFloat)ringSize {
    if (_ringSize != ringSize && ringSize > 6.0) {
        _ringSize = ringSize;
        [self setNeedsLayout:YES];
    }
}
- (BOOL)isAnimating { return _animating; }

- (void)layout {
    [super layout];
    
    CGFloat useSize = (_ringSize > 0) ? _ringSize : MIN(self.bounds.size.width, self.bounds.size.height);
    useSize = MIN(useSize, MIN(self.bounds.size.width, self.bounds.size.height));
    if (useSize < 10) useSize = 16;
    
    CGRect circleFrame = CGRectMake((self.bounds.size.width  - useSize) / 2.0,
                                    (self.bounds.size.height - useSize) / 2.0,
                                    useSize, useSize);
    self.arcLayer.frame = circleFrame;
    
    CGFloat lineW = MAX(useSize / 10.0, 3.0);
    
    // 用缓存的图片提升性能
    self.arcLayer.contents = (__bridge id)[self.class cachedRingArcCGImageWithSize:circleFrame.size thickness:lineW clockwise:self.clockwise];
    
    YYBMacLogInfo(kLogTag, @"[layout] frame=%@ ringSize=%.0f lineW=%.1f", NSStringFromRect(circleFrame), useSize, lineW);
}

#pragma mark - Animation

- (void)startAnimating
{
    if (_animating) return;
    _animating = YES;
    self.hidden = NO;
    
    [self.arcLayer removeAnimationForKey:@"rotate"];
    CABasicAnimation *rotate = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotate.fromValue = @(0);
    // 动画方向由 clockwise 决定
    rotate.toValue = self.clockwise ?  @(-2 * M_PI): @(2 * M_PI) ;
    rotate.duration = 1.1;
    rotate.repeatCount = HUGE_VALF;
    rotate.removedOnCompletion = NO;
    rotate.fillMode = kCAFillModeForwards;
    [self.arcLayer addAnimation:rotate forKey:@"rotate"];
    
    YYBMacLogInfo(kLogTag, @"[startAnimating] ring loading started, ringSize=%.0f, clockwise=%d", _ringSize, self.clockwise);
}

- (void)stopAnimating
{
    if (!_animating) return;
    _animating = NO;
    self.hidden = YES;
    [self.arcLayer removeAnimationForKey:@"rotate"];
    
    YYBMacLogInfo(kLogTag, @"[stopAnimating] ring loading stopped.");
}

#pragma mark - 圆环图片生成与缓存

/// 带缓存功能的工厂，key由尺寸+线宽+方向组成
+ (CGImageRef)cachedRingArcCGImageWithSize:(CGSize)size thickness:(CGFloat)lineW clockwise:(BOOL)clockwise
{
    NSString *key = [NSString stringWithFormat:@"%.1fx%.1f-%.1f-%d", size.width, size.height, lineW, clockwise];
    static NSCache *ringArcCache = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        ringArcCache = [[NSCache alloc] init];
        // 可根据需要限制缓存条数
        ringArcCache.countLimit = 32;
    });
    
    CGImageRef cachedImg = (__bridge CGImageRef)[ringArcCache objectForKey:key];
    if (cachedImg) {
        return cachedImg;
    }
    CGImageRef img = [self generateRingArcCGImageWithSize:size thickness:lineW clockwise:clockwise];
    if (img) {
        [ringArcCache setObject:(__bridge id)img forKey:key];
    }
    return img;
}

/// 生成Loader圆环图片
/// clockwise: YES顺时针（动画与渐变头部方向一致），NO逆时针（动画与渐变头部相反，但亮头仍在前端）
+ (CGImageRef)generateRingArcCGImageWithSize:(CGSize)size thickness:(CGFloat)lineW clockwise:(BOOL)clockwise
{
    // ---参数可调区---
    CGFloat arcRate = 0.85;     // 有效圆环比例: 85%。可调，控制拖尾长度
    CGFloat kExponent = 2.8;    // 越大亮头越突出。Win10质感推荐 2.5~3
    CGFloat alphaHead = 1.0;    // 亮头透明度
    CGFloat alphaTail = 0.10;   // 拖尾透明度
    int sections = 256;         // 越大越平滑
    // -------------------
    
    // 圆环几何
    CGFloat radius = (size.width - lineW) / 2.0;
    CGPoint center = CGPointMake(size.width/2, size.height/2);
    
    // 亮头在正上方（-M_PI_2），实际运动方向与动画一致
    CGFloat startAngle = -M_PI_2;
    CGFloat arcLen = 2 * M_PI * arcRate;
    if (!clockwise) arcLen = -arcLen; // 逆时针则逆画
    
    size_t pixels = (size_t)size.width;
    size_t bytesPerRow = pixels * 4;
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    CGContextRef ctx = CGBitmapContextCreate(NULL, pixels, pixels, 8, bytesPerRow,
                                             colorSpace, kCGBitmapByteOrderDefault | kCGImageAlphaPremultipliedFirst);
    
    CGContextClearRect(ctx, CGRectMake(0, 0, size.width, size.height));
    
    // 保证亮头始终在前，无论动画方向
    for (int i = 0; i < sections; ++i) {
        // pct=0：亮头，pct=1：拖尾
        CGFloat pct = (CGFloat)i / (sections-1);
        CGFloat angle = startAngle + arcLen * pct;
        
        // 渐变公式：pct=0亮头，pct=1拖尾
        CGFloat alpha = alphaTail + (alphaHead - alphaTail) * pow(1-pct, kExponent);
        
        NSColor *color = [NSColor colorWithWhite:1 alpha:alpha];
        
        CGPoint p0 = CGPointMake(center.x + radius * cos(angle),
                                 center.y + radius * sin(angle));
        CGFloat dotR = lineW / 2.0;
        CGRect dotRect = CGRectMake(p0.x - dotR, p0.y - dotR, lineW, lineW);
        
        // macOS平台用 kCGBlendModeNormal
        CGContextSetBlendMode(ctx, kCGBlendModeNormal);
        CGContextSetFillColorWithColor(ctx, color.CGColor);
        CGContextFillEllipseInRect(ctx, dotRect);
    }
    
    CGImageRef resultImg = CGBitmapContextCreateImage(ctx);
    CGContextRelease(ctx);
    CGColorSpaceRelease(colorSpace);
    return resultImg;
}

@end
