//
//  YYBApkInfoFetcher.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/20.
//

#import "YYBApkInfoFetcher.h"
#import "YYBApkInfoModel.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <YYBMacFusionSDK/YYBMacHttp.h>

@interface YYBApkInfoFetcher ()

@end

@implementation YYBApkInfoFetcher

// 这里只是模拟请求
- (void)fetchAppInfoForPkgName:(NSString *)pkgName completion:(YYBApkInfoFetcherResult)completion {
    // 域名后跟v2，表示v2版本的请求格式
    // v2版本url格式示例：https://yybadaccess.sparta.html5.qq.com/v2/dynamicard_pcyyb?scene=discovery
    
    // bodyValue，作为请求体中的body的值
    NSDictionary *bodyValue = @{
        @"bid": @"yybmac",
        @"preview": @NO,
        @"layout": @"AppInfo", //  ← 接口名
        @"listS": @{
            @"region": @{ @"repStr": @[@"CN"] },
            @"supplyId": @{ @"repStr": @[@""] },
            @"installed_list": @{ @"repStr": @[] },
            @"architecture": @{ @"repStr": @[@"Unknown"] },
            @"trace_id": @{ @"repStr": @[@""] },
            @"pkgname": @{ @"repStr": @[pkgName] },  // 包名
            @"channel_id": @{ @"repStr": @[@""] },
            @"media_type": @{ @"repStr": @[@""] }
        },
        @"listI": @{
            @"international": @{ @"repInt": @[@0] },
            @"client_type": @{ @"repInt": @[@0] },
            @"oem_type": @{ @"repInt": @[@0] },
            @"installed_appid_list": @{ @"repInt": @[] },
            @"multi_oaid_switch": @{ @"repInt": @[@2] },
            @"download_from_ad": @{ @"repInt": @[@0] },
            @"download_type": @{ @"repInt": @[@0] }
        },
        @"offset": @0,
        @"size": @15,
        @"trace": @NO
    };
    
    // 创建一个请求对象，对于v2版本使用initV2WithApi初始化，填入api、scene、bodyValue等参数
    YYBMacJsonCmdRequest *request = [[YYBMacJsonCmdRequest alloc] initV2WithApi:@"dynamicard_pcyyb" scene:@"datasource" bodyValue:bodyValue];
    
    // 发送请求
    [[YYBMacHttp sharedInstance] sendYYBJsonCmd:request success:^(YYBMacJsonCmdResponse * _Nonnull response) {
        NSDictionary *respObj = response.responseObject;
        YYBApkInfoModel *model = nil;
        
        // 默认解析失败的错误
        void (^callCompletionFail)(NSString *) = ^(NSString *msg) {
            if (completion) {
                NSError *e = [NSError errorWithDomain:@"YYBApkInfoFetcher"
                                                 code:-1
                                             userInfo:@{NSLocalizedDescriptionKey: msg ?: @"数据格式错误"}];
                completion(nil, e);
            }
        };
        
        // 开始安全解析
        NSDictionary *data = respObj[@"data"];
        NSArray *components = data[@"components"];
        if ([components isKindOfClass:NSArray.class] && components.count > 0) {
            NSDictionary *component0 = components[0];
            NSDictionary *cdata = component0[@"data"];
            NSArray *itemData = cdata[@"itemData"];
            if ([itemData isKindOfClass:NSArray.class] && itemData.count > 0) {
                NSDictionary *item = itemData[0];
                model = [[YYBApkInfoModel alloc] init];
                // 包名先优选 "pkg_name" 其次 "cid"
                model.pkgName = item[@"pkg_name"] ?: item[@"cid"] ?: pkgName;
                model.name = item[@"name"] ?: @"";
                model.versionName = item[@"version_name"] ?: @"";
                NSString *versionCode = nil;
                // version_code 可能是int或string，转字符串
                id vcodeObj = item[@"version_code"];
                if ([vcodeObj isKindOfClass:NSString.class]) {
                    versionCode = vcodeObj;
                } else if ([vcodeObj isKindOfClass:NSNumber.class]) {
                    versionCode = [(NSNumber*)vcodeObj stringValue];
                }
                model.versionCode = versionCode ?: @"";
                
                model.md5 = item[@"md5"] ?: @"";
                model.apkUrl = item[@"apk_url"] ?: @"";
                model.icon = item[@"icon"] ?: @"";
                
                // 其它字段可扩展填充
                if (completion) completion(model, nil);
                return;
            } else {
                callCompletionFail(@"itemData字段为空");
            }
        } else {
            callCompletionFail(@"components字段为空");
        }
    } failure:^(NSError * _Nonnull error) {
        if (completion) completion(nil, error);
    }];
}


@end
