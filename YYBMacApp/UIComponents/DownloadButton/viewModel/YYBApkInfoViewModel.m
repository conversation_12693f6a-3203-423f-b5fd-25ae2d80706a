//
//  YYBApkInfoViewModel.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import "YYBApkInfoViewModel.h"
#import "YYBApkInfoModel.h"
#import "InstallApkInfo.h"
#import "YYBAria2DownloadManager.h"
#import "YYBApkPackage.h"
#import "YYBApkInfoFilter.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBApkInfoFetcher.h"
#import "MacroUtils.h"

static NSString *const kLogTag = @"YYBApkInfoViewModel";

@interface YYBApkInfoViewModel ()

/// apk下载状态信息
@property (nonatomic, strong, readwrite) YYBAria2Task *downloadApkInfo;
/// apk安装状态信息
@property (nonatomic, strong, readwrite) InstallApkInfo *installApkInfo;

@property (nonatomic, strong) YYBApkInfoFilter *filter;
@property (nonatomic, strong) dispatch_queue_t delegateQueue;
@property (nonatomic, strong) NSHashTable<id<YYBApkInfoViewModelDelegate>> *delegates;
@property (nonatomic, assign) BOOL isFetchingAppInfo;
@property (nonatomic, strong) YYBApkInfoFetcher *fetcher;

@end

@implementation YYBApkInfoViewModel

#pragma mark - 初始化
- (instancetype)initWithModel:(YYBApkInfoModel *)model {
    if (self = [super init]) {
        _model = model;
        _filter = [[YYBApkInfoFilter alloc] init];
        _delegates = [NSHashTable weakObjectsHashTable];
        _delegateQueue = dispatch_queue_create("com.yyb.apkinfovm.delegate", DISPATCH_QUEUE_SERIAL);
        _fetcher = [[YYBApkInfoFetcher alloc] init];
        [self initApkInfo];
        [self addToListener];
    }
    return self;
}

/// 初始化apkinfo信息
- (void)initApkInfo {
    //优先更新安装信息
    InstallApkInfo *installApkInfo = [[YYBApkPackage shared] getInstallApkInfoForListener:self];
    [self updateInstallApkInfo:installApkInfo];
    
    YYBAria2Task *downloadApkInfo = [[YYBAria2DownloadManager sharedManager] getAppDownloadTaskInfo:self];
    [self updateDownloadApkInfo:downloadApkInfo error:nil];
    
    if (![self.model.apkUrl length] || ![self.model.name length]) {
        // 还没有拉取到完整AppInfo：请求
        YYBMacLogInfo(kLogTag, @"[initApkInfo] 需拉取AppInfo, pkg=%@", self.model.pkgName);
        [self fetchFullAppInfoIfNeeded];
        // UI保持loading（继续显示YYBApkStateWaiting）
        return;
    }
}

// 注册监听
- (void)addToListener {
    //优先更新安装信息
    [[YYBApkPackage shared] addInstallUninstallListener:self];
    [[YYBAria2DownloadManager sharedManager] addDownloadListener:self];
}

- (void)dealloc {
    // 安全去注册，避免野指针与重复事件分发
    @try {
        [[YYBApkPackage shared] removeInstallUninstallListener:self];
    } @catch (__unused NSException *e) {
        
    }
    @try {
        [[YYBAria2DownloadManager sharedManager] removeDownloadListener:self];
    } @catch (__unused NSException *e) {
        
    }
    YYBMacLogInfo(kLogTag, @"[dealloc] VM released, pkg=%@", self.model.pkgName);
}

#pragma mark - 状态
- (BOOL)haveCache {
    return self.downloadApkInfo != nil;
}

#pragma mark - 拉取apkinfo信息
- (BOOL)fetchFullAppInfoIfNeeded {
    // 只在 pkgName 有但其它为nil、尚未请求过, 且为autostart(用户点击过)时才拉取
    if (!self.model.pkgName.length || self.isFetchingAppInfo || !self.autoStart) {
        YYBMacLogInfo(kLogTag, @"[fetchFullAppInfoIfNeeded] return;  pkg=%@, isFetchingAppInfo = %@, autoStart = %@",
                      self.model.pkgName, @(self.isFetchingAppInfo), @(self.autoStart));
        return NO;
    }
    
    self.isFetchingAppInfo = YES;
    [self.model updateApkState:YYBApkStateFetchingApkInfo];
    [self dispatchChangeWithError:nil];
    YYBMacLogInfo(kLogTag, @"[fetchFullAppInfoIfNeeded] 请求apkInfo pkg=%@", self.model.pkgName);
    __weak typeof(self) weakSelf = self;
    [self.fetcher fetchAppInfoForPkgName:self.model.pkgName completion:^(YYBApkInfoModel * _Nullable retModel, NSError * _Nullable error) {
        __strong typeof(weakSelf) self = weakSelf;
        self.isFetchingAppInfo = NO;
        if (retModel) {
            // 合并 retModel 到 self.model
            YYBMacLogInfo(kLogTag, @"[fetchFullAppInfoIfNeeded] 请求成功 pkg=%@", retModel.pkgName);
            [self.model copyFromModel:retModel];
            [self.model updateApkState:YYBApkStateFetchComplete];
            [self dispatchChangeWithError:nil];
        } else {
            YYBMacLogError(kLogTag, @"[fetchFullAppInfoIfNeeded] 请求失败 pkg=%@ error=%@", self.model.pkgName, error);
            [self.model updateApkState:YYBApkStateError];
            [self dispatchChangeWithError:error];
        }
    }];
    return YES;
}

#pragma mark - 状态更新
- (void)updateDownloadApkInfo:(YYBAria2Task *)downloadApkInfo error:(NSError * _Nullable)error  {
    if (![self.filter shouldAcceptDownloadInfo:downloadApkInfo]) {
        YYBMacLogInfo(kLogTag, @"[updateDownloadApkInfo], downloadApkInfo 已过滤，跳过UI状态更新， %@", downloadApkInfo);
        return;
    }
    
    self.downloadApkInfo = downloadApkInfo;
    
    if ([self.model.apkState isEqualToString:YYBApkStateInstalled] ||
        [self.model.apkState isEqualToString:YYBApkStateInstalling]) {
        // 安装中或已经安装完成的情况 下，无需再更新downloadApkInfo
        return;
    }
    
    if (!downloadApkInfo) {
        // 如果下载信息为空，且有安装信息，则同步安装信息
        if ([self inInstall:self.installApkInfo]) {
            [self updateInstallApkInfo:self.installApkInfo];
        } else {
            self.model.downloadPercent = 0;
            [self dispatchChangeWithError:error];
        }
        return;
    }
    
    // 更新进度
    self.model.downloadPercent = MAX(0, MIN(1.0, downloadApkInfo.progress));
    
    if (!self.downloadApkInfo.isStarted &&
        self.downloadApkInfo.status != YYBAria2TaskStatusComplete) {
        // 非完成情况下，且用户还没启动过任务，则直接标记为暂停（需要用户点击 继续来启动）
        [self.model updateApkState:YYBApkStatePaused];
        [self dispatchChangeWithError:error];
        return;
    }
    
    // 更新状态
    switch (downloadApkInfo.status) {
        case YYBAria2TaskStatusPending:
            [self.model updateApkState:YYBApkStateINIT];
            break;
        case YYBAria2TaskStatusWaitingForNetwork:
        case YYBAria2TaskStatusWaitingForService:
        case YYBAria2TaskStatusRetrying:
            [self.model updateApkState:YYBApkStateWaiting];
            break;
            
        // wait是active的过渡态，提前让进度显示出来
        case YYBAria2TaskStatusWaiting:
        case YYBAria2TaskStatusActive:
            self.autoStart = YES;
            [self.model updateApkState:YYBApkStateActive];
            break;
        case YYBAria2TaskStatusPaused:
            if (downloadApkInfo.scheduleStatus == YYBAria2TaskScheduleStatusPaused ||
                downloadApkInfo.scheduleStatus == YYBAria2TaskScheduleStatusWaiting) {
                // 如果是被并发个数拦截的pause，则返回waiting
                [self.model updateApkState:YYBApkStateWaiting];
            } else {
                [self.model updateApkState:YYBApkStatePaused];
            }
            break;
        case YYBAria2TaskStatusComplete:
            [self.model updateApkState:YYBApkStateComplete];
            break;
        case YYBAria2TaskStatusErrorNetwork:
            [self.model updateApkState:YYBApkStateError];
            break;
        case YYBAria2TaskStatusErrorFatal:
            [self.model updateApkState:YYBApkStateError];
            break;
        case YYBAria2TaskStatusRemoved:
            [self.model updateApkState:YYBApkStateRemoved];
            break;
    }
    
    [self dispatchChangeWithError:error];
}

- (void)updateInstallApkInfo:(InstallApkInfo *)installApkInfo {
    if (![self.filter shouldAcceptInstallInfo:installApkInfo]) {
        YYBMacLogInfo(kLogTag, @"[updateInstallApkInfo], installApkInfo 已过滤，跳过UI状态更新， %@", installApkInfo);
        return;
    }
    self.installApkInfo = installApkInfo;
    // 安装只处理明确的状态，其他状态不处理（兼容本地安装，以及初始化时相关状态），安装状态优先级理论上大于下载状态
    switch (installApkInfo.installStatus) {
        case InstallApkStatusStartInstalling:
            // 安装-开始
            [self.model updateApkState:YYBApkStateBeginInstall];
            break;
        case InstallApkStatusInstalling:
            // 安装-中
            [self.model updateApkState:YYBApkStateInstalling];
            break;
        case InstallApkStatusInstallCompleted:
            // 安装-完成
            [self.model updateApkState:YYBApkStateInstalled];
            break;
        default: {
            if (self.downloadApkInfo) {
                // 其他状态，还原为下载时的状态
                [self updateDownloadApkInfo:self.downloadApkInfo error:nil];
            } else {
                [self.model updateApkState:YYBApkStateINIT];
            }
        }
    }
    
    [self dispatchChangeWithError:nil];
}

/// 是否正在安装中
- (BOOL)inInstall:(InstallApkInfo *)installApkInfo {
    if (!installApkInfo) {
        return NO;
    }
    
    return installApkInfo.installStatus == InstallApkStatusStartInstalling ||
    installApkInfo.installStatus == InstallApkStatusInstalling ||
    installApkInfo.installStatus == InstallApkStatusInstallCompleted;
}

#pragma mark - 下载listener代理
+ (nonnull NSString *)downloadKeyForTask:(nonnull YYBAria2Task *)task {
    return task.pkgName ?: @"";
}

- (nonnull NSString *)listenerDownloadKey { 
    return self.model.pkgName ?: @"";
}

- (void)onDownloadProgress:(nonnull YYBAria2Task *)task progress:(double)progress {
    [self updateDownloadApkInfo:task error:nil];
}

- (void)onDownloadStatusChanged:(nonnull YYBAria2Task *)task status:(YYBAria2TaskStatus)status error:(NSError * _Nullable)error {
    [self updateDownloadApkInfo:task error:nil];
}

#pragma mark - 安装listener代理
- (nonnull NSString *)listenerPkgName {
    return self.model.pkgName ?: @"";
}

- (void)onInstallStatusChanged:(nonnull InstallApkInfo *)apkInfo {
    [self updateInstallApkInfo:apkInfo];
}

#pragma mark - 添加/移除delegate
- (void)addDelegate:(id<YYBApkInfoViewModelDelegate>)delegate {
    if (!delegate) {
        return;
    }
    dispatch_sync(_delegateQueue, ^{
        [self.delegates addObject:delegate];
    });
}

- (void)removeDelegate:(id<YYBApkInfoViewModelDelegate>)delegate {
    if (!delegate) {
        return;
    }
    dispatch_sync(_delegateQueue, ^{
        [self.delegates removeObject:delegate];
    });
}

#pragma mark - delegate分发
// 通用事件分发，仅回调多代理，主线程
- (void)dispatchChangeWithError:(NSError *)error {
    YYBApkInfoModel *modelSnapshot = self.model;
    DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
        for (id<YYBApkInfoViewModelDelegate> d in self.delegates) {
            if (d && [d respondsToSelector:@selector(apkInfoViewModel:didUpdateModel:error:)]) {
                [d apkInfoViewModel:self didUpdateModel:modelSnapshot error:error];
            }
        }
    });
}

- (void)notifyCurrentStateToUI {
    [self dispatchChangeWithError:nil];
}


@end
