//
//  YYBApkInfoViewModelPool.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//  VM 池，按 pkgName 作为 key，复用 YYBApkInfoViewModel

#import <Foundation/Foundation.h>

@class YYBApkInfoModel;
@class YYBApkInfoViewModel;

NS_ASSUME_NONNULL_BEGIN

@interface YYBApkInfoViewModelPool : NSObject

+ (instancetype)sharedPool;

/// 依据 model.pkgName  取复用 VM，不存在则创建并缓存（弱引用）
/// 注意：外部需强引用返回的VM，否则会被回收
- (YYBApkInfoViewModel *)viewModelForModel:(YYBApkInfoModel *)model;

// 基于pkgName取复用VM
- (YYBApkInfoViewModel *)viewModelForPkgName:(NSString *)pkgName;

@end

NS_ASSUME_NONNULL_END
