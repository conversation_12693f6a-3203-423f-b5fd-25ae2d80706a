//
//  YYBApkInfoViewModel.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import <Foundation/Foundation.h>
#import "YYBDownloadListener.h"
#import "YYBInstallUninstallListener.h"
#import "YYBApkInfoViewModelDelegate.h"

@class YYBApkInfoModel;
@class YYBAria2Task;
@class InstallApkInfo;

NS_ASSUME_NONNULL_BEGIN

@interface YYBApkInfoViewModel : NSObject <YYBDownloadListener, YYBInstallUninstallListener>

/// apk完整状态信息
@property (nonatomic, strong, readonly) YYBApkInfoModel *model;
/// apk下载状态信息
@property (nonatomic, strong, readonly) YYBAria2Task *downloadApkInfo;
/// apk安装状态信息
@property (nonatomic, strong, readonly) InstallApkInfo *installApkInfo;

/// 自动启动（下载完后是否自动安装，安装完后是否自动打开, 需要用户有明确操作后才开启）
@property (nonatomic, assign) BOOL autoStart;

/// 是否有缓存（有下载任务代表已有缓存）
@property (nonatomic, assign, readonly) BOOL haveCache;

- (instancetype)initWithModel:(YYBApkInfoModel *)model;

/// 添加/移除 代理
- (void)addDelegate:(id<YYBApkInfoViewModelDelegate>)delegate;
- (void)removeDelegate:(id<YYBApkInfoViewModelDelegate>)delegate;

/// 拉取apkinfo信息
- (BOOL)fetchFullAppInfoIfNeeded;

@end

NS_ASSUME_NONNULL_END
