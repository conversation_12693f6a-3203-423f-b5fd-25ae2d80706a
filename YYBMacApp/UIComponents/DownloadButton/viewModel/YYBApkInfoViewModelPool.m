//
//  YYBApkInfoViewModelPool.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import "YYBApkInfoViewModelPool.h"
#import "YYBApkInfoViewModel.h"
#import "YYBApkInfoModel.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kVMPoolLogTag = @"YYBApkInfoViewModelPool";

@interface YYBApkInfoViewModelPool ()

@property (nonatomic, strong) NSMapTable<NSString *, YYBApkInfoViewModel *> *pool; // weak values
@property (nonatomic, strong) dispatch_queue_t isolationQueue;

@end

@implementation YYBApkInfoViewModelPool

+ (instancetype)sharedPool {
    static YYBApkInfoViewModelPool *vmPool = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        vmPool = [[YYBApkInfoViewModelPool alloc] initPrivate];
    });
    return vmPool;
}

- (instancetype)initPrivate {
    if (self = [super init]) {
        _pool = [NSMapTable strongToWeakObjectsMapTable];
        _isolationQueue = dispatch_queue_create("com.yyb.apkinfo.vm.pool", DISPATCH_QUEUE_SERIAL);
    }
    return self;
}

// 基于pkgName取复用VM
- (YYBApkInfoViewModel *)viewModelForPkgName:(NSString *)pkgName {
    if (!pkgName.length) return nil;
    // 用pkgName创建一个空model（只有pkgName）
    YYBApkInfoModel *stubModel = [[YYBApkInfoModel alloc] init];
    stubModel.pkgName = pkgName;
    return [self viewModelForModel:stubModel];
}

- (YYBApkInfoViewModel *)viewModelForModel:(YYBApkInfoModel *)model {
    NSString *modelKey = [self keyForApkInfoModel:model];
    if (!modelKey.length) {
        YYBMacLogInfo(kVMPoolLogTag, @"[viewModelForModel] invalid model");
        return [[YYBApkInfoViewModel alloc] initWithModel:model];
    }
    
    __block YYBApkInfoViewModel *vm = nil;
    dispatch_sync(self.isolationQueue, ^{
        vm = [self.pool objectForKey:modelKey];
        if (!vm) {
            vm = [[YYBApkInfoViewModel alloc] initWithModel:model];
            [self.pool setObject:vm forKey:modelKey];
            YYBMacLogInfo(kVMPoolLogTag, @"[create] pkg=%@ -> new VM:%p", modelKey, vm);
        } else {
            [model updateApkState:vm.model.apkState];
            YYBMacLogInfo(kVMPoolLogTag, @"[reuse] pkg=%@ -> reuse VM:%p", modelKey, vm);
        }
    });
    return vm;
}

- (nullable NSString *)keyForApkInfoModel:(YYBApkInfoModel *)model {
    if (!model || !model.pkgName.length) {
        return nil;
    }
    
    // 用pkgName进行复用（部分业务或下载按钮未点击也需要复用已点击下载开始的apk）
    return model.pkgName;
}

@end
