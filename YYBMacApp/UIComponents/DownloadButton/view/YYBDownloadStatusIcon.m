//
//  YYBDownloadStatusIcon.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import "YYBDownloadStatusIcon.h"
#import "YYBApkInfoModel.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBRingLoadingView.h"

static NSString *const kLogTag = @"YYBDownloadStatusIcon";

@interface YYBDownloadStatusIcon()

@property (nonatomic, strong) NSProgressIndicator *systemSpinner;
@property (nonatomic, strong) YYBRingLoadingView  *ringLoading;
@property (nonatomic, assign) BOOL isLoading; // 当前loading标志
@property (nonatomic, assign) CGFloat expectedLoadingSize; // 期望的loading尺寸

@end

@implementation YYBDownloadStatusIcon

- (instancetype)initWithFrame:(NSRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.wantsLayer = YES;
        _loadingStyle = YYBDownloadStatusIconStyleRing; // 默认圈圈
        
        // 圈圈loading
        _ringLoading = [[YYBRingLoadingView alloc] initWithFrame:self.bounds];
        _ringLoading.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
        _ringLoading.hidden = YES;
        [self addSubview:_ringLoading];
        
        // 系统loading
        _systemSpinner = [[NSProgressIndicator alloc] initWithFrame:self.bounds];
        _systemSpinner.style = NSProgressIndicatorStyleSpinning;
        _systemSpinner.displayedWhenStopped = NO;
        _systemSpinner.hidden = YES;
        _systemSpinner.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
        [self addSubview:_systemSpinner];
        
        _isLoading = NO;
        _expectedLoadingSize = 0;
    }
    return self;
}

#pragma mark - 样式切换动态响应
- (void)setLoadingStyle:(YYBDownloadStatusIconStyle)loadingStyle
{
    if (_loadingStyle == loadingStyle) return;
    _loadingStyle = loadingStyle;
    
    // 运行时切换样式
    if (_isLoading) {
        [self showLoadingWithSize:_expectedLoadingSize];
    } else {
        [self stopLoading];
    }
    YYBMacLogInfo(kLogTag, @"[setLoadingStyle] style=%lu", (unsigned long)loadingStyle);
}

/// 统一响应外部apk状态
- (void)setApkState:(NSString *)apkState
          autoStart:(BOOL)autoStart
              error:(NSError * _Nullable)error
        loadingSize:(CGFloat)loadingSize {
    BOOL shouldShowLoading = NO;
    if ([apkState isEqualToString:YYBApkStateBeginInstall] ||
        [apkState isEqualToString:YYBApkStateInstalling] ||
        [apkState isEqualToString:YYBApkStateBeginUninstall] ||
        [apkState isEqualToString:YYBApkStateUnInstalling] ||
        [apkState isEqualToString:YYBApkStateComplete] ||
        [apkState isEqualToString:YYBApkStateWaiting] ||
        [apkState isEqualToString:YYBApkStateBeginLaunch] ||
        [apkState isEqualToString:YYBApkStateFetchingApkInfo] ||
        [apkState isEqualToString:YYBApkStateFetchComplete]) {
        shouldShowLoading = YES;
    }
    
    if (shouldShowLoading) {
        [self showLoadingWithSize:loadingSize];
        YYBMacLogInfo(kLogTag, @"[setApkState] showLoading for state=%@", apkState);
    } else {
        [self stopLoading];
        YYBMacLogInfo(kLogTag, @"[setApkState] stopLoading for state=%@", apkState);
    }
}

#pragma mark - loading动画（样式自适应）

- (void)showLoadingWithSize:(CGFloat)size
{
    _isLoading = YES;
    _expectedLoadingSize = size;
    // 先都隐藏
    _ringLoading.hidden = YES; [_ringLoading stopAnimating];
    _systemSpinner.hidden = YES; [_systemSpinner stopAnimation:self];
    
    switch (_loadingStyle) {
        case YYBDownloadStatusIconStyleSystem:
            // 系统loading
            _systemSpinner.frame = [self loadingFrameWithSize:size];
            _systemSpinner.hidden = NO;
            [_systemSpinner startAnimation:self];
            YYBMacLogInfo(kLogTag, @"[showLoadingWithSize] system spinner ON, size=%.0f", size);
            break;
        case YYBDownloadStatusIconStyleRing:
        default:
            _ringLoading.frame = [self loadingFrameWithSize:size];
            _ringLoading.ringSize = size;
            _ringLoading.hidden   = NO;
            [_ringLoading startAnimating];
            YYBMacLogInfo(kLogTag, @"[showLoadingWithSize] ring slider loading ON, size=%.0f", size);
            break;
    }
    
    [self setNeedsLayout:YES];
    [self setNeedsDisplay:YES];
}

- (void)stopLoading {
    _isLoading = NO;
    _expectedLoadingSize = 0;
    
    if (!_ringLoading.hidden) [_ringLoading stopAnimating];
    if (!_systemSpinner.hidden) [_systemSpinner stopAnimation:self];
    _ringLoading.hidden = YES;
    _systemSpinner.hidden = YES;
    
    YYBMacLogInfo(kLogTag, @"[stopLoading] all loading hidden.");
}

#pragma mark - 子view布局自适应
- (void)layout {
    [super layout];
    CGFloat useSize = (_expectedLoadingSize > 0) ? _expectedLoadingSize : MIN(self.bounds.size.width, self.bounds.size.height) * 0.7;
    NSRect loadingFrame = [self loadingFrameWithSize:useSize];
    
    _ringLoading.frame = loadingFrame;
    _systemSpinner.frame = loadingFrame;
    
    // 日志定位加载框具体尺寸/位置
    YYBMacLogInfo(kLogTag, @"[layout] frame=%@ useSize=%.0f", NSStringFromRect(loadingFrame), useSize);
}

/// 居中frame
- (NSRect)loadingFrameWithSize:(CGFloat)size {
    CGFloat w = MIN(size, self.bounds.size.width);
    CGFloat h = MIN(size, self.bounds.size.height);
    if (w < 10) w = 16; if (h < 10) h = 16;
    return NSMakeRect((self.bounds.size.width - w) / 2.0, (self.bounds.size.height - h) / 2.0, w, h);
}

@end
