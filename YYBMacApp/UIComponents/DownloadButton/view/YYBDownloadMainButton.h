//
//  YYBDownloadMainButton.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, YYBDownloadMainButtonStyle) {
    YYBDownloadMainButtonStyleDefault,     // 蓝色主按钮
    YYBDownloadMainButtonStyleInProgress,  // 进度变化中
};

@interface YYBDownloadMainButton : NSButton

@property (nonatomic, assign) YYBDownloadMainButtonStyle btnStyle;
@property (nonatomic, assign) double progress; // 0.0 ~ 1.0
@property (nonatomic, strong) NSColor *progressColor;

// 填充进度 0~1
- (void)setProgress:(double)progress animated:(BOOL)animated;
- (void)setProgress:(double)progress fillColor:(nullable NSColor *)color animated:(BOOL)animated;

#pragma mark - 统一刷新接口
/// 响应整体apk状态、进度等（统一接口）
- (void)setApkState:(NSString *)apkState
          autoStart:(BOOL)autoStart
          haveCache:(BOOL)haveCache
           progress:(double)progress
              error:(NSError * _Nullable)error
         isHovering:(BOOL)isHovering;

@end

NS_ASSUME_NONNULL_END
