//
//  YYBDownloadStatusIcon.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, YYBDownloadStatusIconStyle) {
    YYBDownloadStatusIconStyleRing,      // 渐变圈圈，推荐&默认
    YYBDownloadStatusIconStyleSystem,    // 系统NSProgressIndicator样式
};

/// 功能图标，如“加载中”等
@interface YYBDownloadStatusIcon : NSImageView

/// 当前loading样式
@property (nonatomic, assign) YYBDownloadStatusIconStyle loadingStyle;

/// 显示/隐藏loading动画
- (void)showLoadingWithSize:(CGFloat)size;
- (void)stopLoading;

/// 响应下载主状态
- (void)setApkState:(NSString *)apkState
          autoStart:(BOOL)autoStart
              error:(NSError * _Nullable)error
        loadingSize:(CGFloat)loadingSize;

@end

NS_ASSUME_NONNULL_END
