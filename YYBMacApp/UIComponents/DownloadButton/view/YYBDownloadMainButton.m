//
//  YYBDownloadMainButton.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//


#import "YYBDownloadMainButton.h"
#import <QuartzCore/QuartzCore.h>
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBApkInfoModel.h"   // 用于状态常量

static NSString *const kLogTag = @"YYBDownloadMainButton";

@interface YYBDownloadMainButton ()

@property (nonatomic, strong) CALayer *progressLayer;
@property (nonatomic, strong) CAShapeLayer *progressMaskLayer;
@property (nonatomic, assign) double internalProgress;
@property (nonatomic, strong) NSColor *internalProgressColor;

// 默认主色调和反色
@property (nonatomic, strong) NSColor *defaultMainColor;
@property (nonatomic, strong) NSColor *defaultInverseColor;

@end

@implementation YYBDownloadMainButton

#pragma mark - 初始化

- (instancetype)initWithFrame:(NSRect)frameRect {
    if (self = [super initWithFrame:frameRect]) {
        self.wantsLayer = YES;
        self.bordered = NO;
        self.defaultMainColor = [NSColor colorWithCalibratedRed:0.10 green:0.55 blue:1.0 alpha:1.0]; // 主蓝
        self.defaultInverseColor = [NSColor colorWithCalibratedRed:0.45 green:0.74 blue:1.0 alpha:1.0]; // 浅蓝
        self.layer.backgroundColor = self.defaultMainColor.CGColor;
        self.layer.cornerRadius = frameRect.size.height / 2.0;
        self.layer.masksToBounds = YES;
        self.font = [NSFont systemFontOfSize:16 weight:NSFontWeightSemibold];
        
        // 进度条底层
        self.progressLayer = [CALayer layer];
        self.progressLayer.masksToBounds = YES;
        self.progressLayer.backgroundColor = self.defaultMainColor.CGColor;
        [self.layer addSublayer:self.progressLayer];
        
        // 进度 mask 层
        self.progressMaskLayer = [CAShapeLayer layer];
        self.progressLayer.mask = self.progressMaskLayer;
        
        self.internalProgressColor = self.defaultMainColor;
        self.progress = 0;
        [self refreshTextLayout];
    }
    return self;
}

#pragma mark - 风格样式

- (void)setBtnStyle:(YYBDownloadMainButtonStyle)btnStyle {
    _btnStyle = btnStyle;
    [self applyCurrentStyle];
}

- (void)applyCurrentStyle {
    NSColor *bg = self.defaultMainColor;
    self.progressLayer.hidden = YES;
    switch (self.btnStyle) {
        case YYBDownloadMainButtonStyleDefault:
            bg = self.defaultMainColor;
            self.progressLayer.hidden = YES;
            break;
        case YYBDownloadMainButtonStyleInProgress:
            bg = self.defaultInverseColor;
            self.progressLayer.hidden = NO;
            break;
    }
    self.layer.backgroundColor = bg.CGColor;
}

#pragma mark - 状态综合控制接口（建议外部只用本接口驱动UI）

- (void)setApkState:(NSString *)apkState
          autoStart:(BOOL)autoStart
          haveCache:(BOOL)haveCache
           progress:(double)progress
              error:(NSError * _Nullable)error
         isHovering:(BOOL)isHovering {
    // 默认
    BOOL mainButtonEnabled = YES;
    double buttonProgress = 0;
    NSString *title = @"安装";
    YYBDownloadMainButtonStyle btnStyle = YYBDownloadMainButtonStyleDefault;
    
    if (!autoStart) {
        // 各种状态(非自动打开)
        if ([apkState isEqualToString:YYBApkStateInstalled]) {
            title = @"打开";
        } else if ([apkState isEqualToString:YYBApkStateFetchingApkInfo] ||
                   [apkState isEqualToString:YYBApkStateInstalling]) {
            // 请求信息中或安装中，展示loading, 且不可再点击
            title = @"";
            mainButtonEnabled = NO;
        } else if (haveCache) {
            title = @"继续";
            buttonProgress = progress;
            btnStyle = YYBDownloadMainButtonStyleInProgress;
        } else {
            title = @"安装";
#ifdef DEBUG
            if (error.code) {
                title = [NSString stringWithFormat:@"安装(%@)", @(error.code)];
            }
#endif
            buttonProgress = progress;
        }
        self.enabled = mainButtonEnabled;
        self.btnStyle = btnStyle;
        self.title = title;
        [self setProgress:buttonProgress fillColor:nil animated:YES];
        YYBMacLogInfo(kLogTag, @"[setApkState] state=%@ title=%@ progress=%.2f%% enabled=%d", apkState, title, progress*100, mainButtonEnabled);
        return;
    }

    // 各种状态(可自动打开)
    // 默认不可交互（处于loading态）
    mainButtonEnabled = NO;
    buttonProgress = 0;
    title = @"";
    if ([apkState isEqualToString:YYBApkStateInstalled]) {
        title = @"打开";
        mainButtonEnabled = YES;
        buttonProgress = 0;
    } else if ([apkState isEqualToString:YYBApkStateActive]) {
        mainButtonEnabled = YES;
        buttonProgress = progress;
        if (isHovering) {
            title = @"暂停";
        } else {
            title = [NSString stringWithFormat:@"%.2f%%", progress * 100];
        }
        btnStyle = YYBDownloadMainButtonStyleInProgress;
    } else if ([apkState isEqualToString:YYBApkStatePaused]) {
        title = @"继续";
        buttonProgress = progress;
        mainButtonEnabled = YES;
        btnStyle = YYBDownloadMainButtonStyleInProgress;
    } else if ([apkState isEqualToString:YYBApkStateNeedUpdate]) {
        title = @"更新";
        mainButtonEnabled = YES;
        buttonProgress = 0;
    } else if ([apkState isEqualToString:YYBApkStateError]) {
        title = @"重试";
        mainButtonEnabled = YES;
        buttonProgress = 0;
    } else if ([apkState isEqualToString:YYBApkStateINIT]) {
        title = @"安装";
        mainButtonEnabled = YES;
        buttonProgress = 0;
    }
    
    // 应用
    self.enabled = mainButtonEnabled;
    self.btnStyle = btnStyle;
    self.title = title;
    [self setProgress:buttonProgress fillColor:nil animated:YES];
    YYBMacLogInfo(kLogTag, @"[setApkState] state=%@ title=%@ progress=%.2f%% enabled=%d", apkState, title, progress*100, mainButtonEnabled);
}

#pragma mark - Progress

- (void)setProgress:(double)progress {
    [self setProgress:progress fillColor:nil animated:NO];
}

- (void)setProgress:(double)progress animated:(BOOL)animated {
    [self setProgress:progress fillColor:nil animated:animated];
}

/// 设置进度并可自定义颜色，带自然动画
- (void)setProgress:(double)progress fillColor:(NSColor *)color animated:(BOOL)animated {
    progress = MAX(0, MIN(progress, 1.0));
    NSColor *fill = color ?: self.internalProgressColor;
    self.internalProgress = progress;
    self.progressLayer.backgroundColor = fill.CGColor;
    
    CGFloat fullW = self.bounds.size.width;
    CGFloat h = self.bounds.size.height;
    CGFloat progressW = fullW * progress;
    progressW = MAX(MIN(progressW, fullW), 0); // 保证不超出
    CGRect targetFrame = CGRectMake(0, 0, progressW, h);
    if (animated) {
        // 使用CoreAnimation平滑动画
        [CATransaction begin];
        [CATransaction setAnimationDuration:0.22];
        self.progressLayer.frame = targetFrame;
        [CATransaction commit];
    } else {
        self.progressLayer.frame = targetFrame;
    }
    self.progressLayer.hidden = progress <= 0.0;
    // mask 进度条左圆角、右直角（单一风格）
    CGPathRef maskPath = YYBMakeLeftSideMask(self.progressLayer.bounds, h/2.0);
    self.progressMaskLayer.frame = self.progressLayer.bounds;
    self.progressMaskLayer.path = maskPath;
    CGPathRelease(maskPath);
}

/// mask，只留左圆角、右直角，进度条越来越短时也贴合左边
static CGPathRef YYBMakeLeftSideMask(CGRect rect, CGFloat radius) {
    CGFloat width = CGRectGetWidth(rect);
    CGFloat height = CGRectGetHeight(rect);
    CGMutablePathRef path = CGPathCreateMutable();
    if (width <= 0 || height <= 0) {
        CGPathCloseSubpath(path);
        return path;
    }
    
    CGFloat minX = CGRectGetMinX(rect), minY = CGRectGetMinY(rect);
    CGFloat maxX = minX + width, maxY = minY + height;
    CGPathMoveToPoint(path, NULL, minX, minY);
    CGPathAddLineToPoint(path, NULL, maxX, minY);
    CGPathAddLineToPoint(path, NULL, maxX, maxY);
    CGPathAddLineToPoint(path, NULL, minX, maxY);
    CGPathCloseSubpath(path);
    return path;
}

- (void)setProgressColor:(NSColor *)progressColor {
    _progressColor = progressColor;
    if (progressColor) {
        self.internalProgressColor = progressColor;
        [self setProgress:self.internalProgress animated:NO];
    }
}

#pragma mark - Layout

- (void)layout {
    [super layout];
    self.layer.cornerRadius = self.bounds.size.height / 2.0;
    // 自动适配按钮尺寸变化
    [self setProgress:self.internalProgress animated:NO];
}

// setFrame 兼容代码/AutoLayout多场景，确保进度条和圆角始终匹配
- (void)setFrame:(NSRect)frame {
    [super setFrame:frame];
    self.layer.cornerRadius = frame.size.height / 2.0;
    [self setProgress:self.internalProgress animated:NO];
}

#pragma mark - Title样式

- (void)setTitle:(NSString *)title {
    [super setTitle:title];
    [self refreshTextLayout];
}

// 刷新按钮主标题
- (void)refreshTextLayout {
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.alignment = NSTextAlignmentCenter;
    style.maximumLineHeight = self.frame.size.height;
    NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:self.title ?: @""];
    NSColor *fg = [NSColor whiteColor];
    [attr addAttribute:NSForegroundColorAttributeName value:fg range:NSMakeRange(0, attr.length)];
    [attr addAttribute:NSParagraphStyleAttributeName value:style range:NSMakeRange(0, attr.length)];
    self.attributedTitle = attr;
}

@end
