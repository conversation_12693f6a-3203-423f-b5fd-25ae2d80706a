//
//  InstallApkInfo.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import "InstallApkInfo.h"
#import "YYBApkInfoModel.h"
#import "YYBAria2Task.h"


@implementation InstallApkInfo

#pragma mark - 时间戳工具方法

/// 获取当前北京时间的时间戳
+ (NSTimeInterval)currentBeijingTimestamp {
    NSTimeZone *beijingTimeZone = [NSTimeZone timeZoneWithName:@"Asia/Shanghai"];
    NSDate *now = [NSDate date];
    NSTimeInterval beijingOffset = [beijingTimeZone secondsFromGMTForDate:now];
    return [now timeIntervalSince1970] + beijingOffset;
}

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"pkgName": @"pkg_name",
        @"filePath": @"file_path",
        @"iconUrl": @"icon",
        @"versionName": @"version_name",
        @"isInstallInEngine": @"is_install_in_engine",
        @"hasShotcut": @"has_shotcut",
        @"extendInfo": @"json_data",
        @"installStatus": @"install_status",
        @"lastErrorCode": @"last_error_code",
        @"lastErrorMessage": @"last_error_message",
        @"silentUpdate": @"silentUpdate",
        @"installTimestamp": @"install_timestamp",
        @"lastOpenTimestamp": @"last_open_timestamp"
    };
}

#pragma mark - 枚举转换
/// 安装状态转字符串，便于UI显示、日志打印等
- (NSString *)statusString {
    switch (self.installStatus) {
        case InstallApkStatusIdle:
            return @"空闲未安装";
        case InstallApkStatusStartInstalling:
            return @"开始安装";
        case InstallApkStatusInstalling:
            return @"安装中";
        case InstallApkStatusInstallCompleted:
            return @"已安装/完成";
        case InstallApkStatusInstallError:
            return @"安装失败";
        case InstallApkStatusStartUninstalling:
            return @"开始卸载";
        case InstallApkStatusUninstalling:
            return @"卸载中";
        case InstallApkStatusUninstallCompleted:
            return @"已卸载/完成";
        case InstallApkStatusUninstallError:
            return @"卸载失败";
        case InstallApkStatusUninstallCancelled:
            return @"卸载取消";
    }
}

#pragma mark - description
/// 完整对象描述，便于日志排查和调试
- (NSString *)description {
    NSMutableString *desc = [NSMutableString stringWithFormat:
        @"<InstallApkInfo: %p, pkgName=%@, name=%@, version=%@, status=%@(%ld), silentUpdate=%@, isInstallInEngine=%@, hasShortcut=%@",
        self,
        self.pkgName ?: @"", self.name ?: @"",
        self.versionName ?: @"",
        [self statusString], (long)self.installStatus,
        self.silentUpdate ? @"YES" : @"NO",
        self.isInstallInEngine ? @"YES" : @"NO",
        self.hasShotcut ? @"YES" : @"NO"
    ];

    // 添加时间戳信息
    if (self.installTimestamp > 0) {
        NSDate *installDate = [NSDate dateWithTimeIntervalSince1970:self.installTimestamp];
        [desc appendFormat:@", installTime=%@", installDate];
    }
    if (self.lastOpenTimestamp > 0) {
        NSDate *lastOpenDate = [NSDate dateWithTimeIntervalSince1970:self.lastOpenTimestamp];
        [desc appendFormat:@", lastOpenTime=%@", lastOpenDate];
    }

    if (self.lastErrorCode != 0 || self.lastErrorMessage.length > 0) {
        [desc appendFormat:@", lastErrorCode=%ld, lastErrorMessage=%@", (long)self.lastErrorCode, self.lastErrorMessage ?: @""];
    }
    if (self.filePath.length > 0) {
        [desc appendFormat:@", filePath=%@", self.filePath];
    }
    if (self.iconPath.length > 0) {
        [desc appendFormat:@", iconPath=%@", self.iconPath];
    }
    if (self.md5.length > 0) {
        [desc appendFormat:@", md5=%@", self.md5];
    }
    [desc appendString:@">"];
    return desc;
}

/// 基于apkinfo信息和下载信息 创建当前的安装信息对象
+ (InstallApkInfo *)createInstallApkInfoFromApkInfo:(YYBApkInfoModel *)apkInfoModel
                                    downloadApkInfo:(YYBAria2Task *)downloadApkInfo {
    InstallApkInfo *installApkInfo = [[InstallApkInfo alloc] init];
    installApkInfo.pkgName = apkInfoModel.pkgName;
    installApkInfo.name = apkInfoModel.name;
    installApkInfo.iconUrl = apkInfoModel.icon;
    installApkInfo.filePath = downloadApkInfo.finalFilePath;
    installApkInfo.versionName = apkInfoModel.versionName;
    installApkInfo.md5 = apkInfoModel.md5;
    
    return installApkInfo;
}

@end
