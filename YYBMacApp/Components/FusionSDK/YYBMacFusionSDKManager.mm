//
//  YYBMacFusionSDKManager.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/6/19.
//

#import "YYBMacFusionSDKManager.h"
#import <YYBMacFusionSDK/YYBMacFusion.h>
#import <YYBMacFusionSDK/YYBMacFusionSDK.h>
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <YYBMacFusionSDK/YYBMacFusionSDKConfig.h>
#import "YYBAppConfig.h"
#import <YYBMacFusionSDK/YYBMacResHub.h>
#import "YYBDefine.h"
#import "ResHubDownloader.h"
//#import "JsonCmdTest.h"


////// c++对应以下头文件
//#import <YYBMacFusionSDK/YYBMacFusionSDKC.h>
//#import <YYBMacFusionSDK/YYBMacLogC.h>
//#import <YYBMacFusionSDK/YYBMacFusionSDKConfigCpp.h>
//#import <YYBMacFusionSDK/YYBMacMMKVC.h>


static NSString *const kLogTag = @"YYBMacFusionSDKManager";
static BOOL isSDKInitialied = NO;

@implementation YYBMacFusionSDKManager

/// 统一初始化（日志）
+ (void)setupFustionSDK:(void(^_Nullable)(BOOL success, NSError * _Nullable error))completion {
    // bugly: https://bugly.woa.com/v2/setting/product?productId=e084796a57&pid=4  产品名称：YYB-MAC-APP
    YYBMacFusionSDKConfig *config = [[YYBMacFusionSDKConfig alloc] init];
    config.identifier = @"main";
    config.buglyAppId = @"e084796a57";
    config.buglyAppKey = @"73f003ac-a161-461f-8705-110f23020a82";
    
    // 大同轻量版：https://datong.woa.com/#/lite/BUSI303/list?inDTFrame=true&appId=dt_demoapp  产品名称：MAC-主端
    config.dataReportAppKey = @"0MAC06I37GRAL16X";
    
    // shiply相关：https://shiply.tds.woa.com/project/setting?projectId=693&appId=1060&envId=101685&scope=&tab=normal  项目名称: 应用宝-MAC-主端 产品名称：主App
    config.shiplyAppId = @"d74ec3f5fe";
    config.shiplyAppKey = @"141fa24e-fd08-4344-970a-88a8c3312bb8";
    
    config.isReleaseApp = [YYBAppConfig isDebug] ? NO : YES;
    config.isReleaseEnvironment = YES;
    config.userId = @"";
    config.channel = @"";
    config.reshubResourceStoragePath = [self getResHubResourcePath];
    config.updateMode = RD_UPDATE_MODE_START_C;
    config.downloadImpl = [ResHubDownloader sharedInstance];
    [[YYBMacFusionSDK sharedInstance] setupWithConfig:config completion:^(BOOL success, NSError * _Nullable error) {
        YYBMacLogInfo(kLogTag, @"setupFustionSDK succes %@, error = %@", @(success), error);
        [[YYBMacFusionSDK sharedInstance] setupAppStoreWithConfig:config];
        isSDKInitialied = YES;
        completion(success, error);
    }];
    
    // 测试json命令字请求
//    [[[JsonCmdTest alloc] init] testJsonCmdV2];
//    [[[JsonCmdTest alloc] init] testJsonCmdV3];
}

+ (NSString *)getResHubResourcePath {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES);
    NSString *appSupportPath = [paths firstObject];
    return [appSupportPath stringByAppendingPathComponent:YYBMainBundleID];
}

+ (BOOL)isInitialied {
    return isSDKInitialied;
}

// c++调用示例
//- (void)setupFustionSDKWithPhone {
//    // bugly: https://bugly.woa.com/v2/setting/product?productId=cb24ab76d2&pid=4  产品名称：YYB-MAC-PHONE
//    YYBMacFusionSDKConfigCpp config;
//    config.identifier = "phone";
//    config.buglyAppId = "cb24ab76d2";
//    config.buglyAppKey = "c898b09c-d505-47f8-b2be-72bf0eb7c014";
//    
//    // 大同轻量版：https://datong.woa.com/#/lite/BUSI303/list?inDTFrame=true&appId=dt_demoapp  产品名称：MAC-PHONE
//    config.dataReportAppKey = "0MAC06I37I14FGSX";
//    
//    // shiply相关：https://shiply.tds.woa.com/project/setting?projectId=693&appId=1060&envId=101685&scope=&tab=normal  项目名称: 应用宝-MAC-主端 产品名称：Phone
//    config.shiplyAppId = "536c926549";
//    config.shiplyAppKey = "1caa6026-6584-46bf-9c0f-3027d05de51f";
//    
//    config.isReleaseApp = false;
//    config.isReleaseEnvironment = false;
//    config.userId = "";
//    config.channel = "";
//
//    // C风格回调
//    void (*callback)(int, const char *) = [](int success, const char *errorMsg) {
//        if (success) {
//            YYBMacLogI("YYBMacFusionSDKManager", "success");
//        } else {
//            YYBMacLogE("YYBMacFusionSDKManager failed", errorMsg);
//        }
//    };
//
//    YYBMacFusionSDK_SetupWithCppConfig(&config, callback);
//}

/// 切换用户
+ (void)switchUser:(nullable NSString *)userId {
    [[YYBMacFusionSDK sharedInstance] switchUser:userId];
}

/// 切换后台环境
+ (void)switchEnvironment:(BOOL)isRelease {
    [[YYBMacFusionSDK sharedInstance] switchEnvironment:isRelease];
}

@end
