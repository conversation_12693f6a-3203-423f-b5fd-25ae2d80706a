//
//  HttpTest.m
//  YYBMacFusionSDK
//
//  Created by g<PERSON><PERSON><PERSON> on 2025/8/15.
//  Copyright © 2025 CocoaPods. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "YYModel.h"
#import "YYBHomeResponse.h"
#import "JsonCmdTest.h"

@implementation JsonCmdTest

-(void)testJsonCmdV2 {
    // 域名后跟v2，表示v2版本的请求格式
    // v2版本url格式示例：https://yybadaccess.sparta.html5.qq.com/v2/dynamicard_pcyyb?scene=discovery

    // bodyValue，作为请求体中的body的值
    NSDictionary * bodyValue = @{
        @"bid": @"yybmac",
        @"preview": @NO,
        @"layout": @"",
        @"listS": @{
            @"region": @{
                @"repStr": @[@"CN"]
            },
            @"supplyId": @{
                @"repStr": @[@""]
            },
            @"wx_sdk_version": @{
                @"repStr": @[@""]
            },
            @"installed_list": @{
                @"repStr": @[]
            },
            @"architecture": @{
                @"repStr": @[@"Unknown"]
            },
            @"trace_id": @{
                @"repStr": @[@""]
            }
        },
        @"listI": @{
            @"international": @{
                @"repInt": @[@0]
            },
            @"client_type": @{
                @"repInt": @[@0]
            },
            @"oem_type": @{
                @"repInt": @[@0]
            },
            @"installed_appid_list": @{
                @"repInt": @[]
            },
            @"multi_oaid_switch": @{
                @"repInt": @[@2]
            }
        },
        @"offset": @0,
        @"size": @15,
        @"trace": @NO
    };
    
    // 创建一个请求对象，对于v2版本使用initV2WithApi初始化，填入api、scene、bodyValue等参数
    YYBMacJsonCmdRequest *request = [[YYBMacJsonCmdRequest alloc] initV2WithApi:@"dynamicard_pcyyb" scene:@"discovery" bodyValue:bodyValue];
    
    // 发送请求
    [[YYBMacHttp sharedInstance] sendYYBJsonCmd:request success:^(YYBMacJsonCmdResponse * _Nonnull response) {
        // 成功响应
        // 响应体：response.responseObject，是整个响应的httpBody部分
        NSLog(@"JsonCmdTest v2 onJsonCmdResponse:%@", response.responseObject);
        // 响应头：response.responseHeaders，是整个响应的httpHeader部分
        NSLog(@"JsonCmdTest v2 onJsonCmdResponse headers:%@, content-type:%@", response.responseHeaders, response.responseHeaders[@"Content-Type"]);
        YYBHomeResponse *home = [YYBHomeResponse yy_modelWithDictionary:response.responseObject[@"data"]];
        NSLog(@"%@", home);
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"JsonCmdTest v2 onJsonCmdFail: errCode:%ld, error:%@", error.code, error);
    }];
}

-(void)testJsonCmdV3 {
    
    // 域名后跟v3，表示v3版本的请求格式
    // v3版本url格式示例：https://yybadaccess.sparta.html5.qq.com/v3/pcyyb_get_rainbow_conf
    
    NSDictionary *envInfo = @{
        @"abox_version": @"3.0.9970.531",
        @"androws_version": @"5.10.1100.2691",
        @"architecture": @"x64",
        @"client_type": @0,
        @"cpu_core_number": @20,
        @"cpu_model": @"12th Gen Intel(R) Core(TM) i7-12700",
        @"cpu_vendor": @"Intel",
        @"graphics_model": @"Intel(R) UHD Graphics 770",
        @"graphics_vendor": @"Intel",
        @"guid": @"ericxluo",
        @"hyperv_state": @0,
        @"image_version": @"5.10.1100.1781",
        @"is_support_hyperv": @1,
        @"locale": @"zh-CN",
        @"login_openid": @"",
        @"login_type": @"",
        @"main_version": @"5.10.11.13",
        @"max_disk_size": @50965417984,
        @"media_channel": @"2700200113",
        @"oem_type": @0,
        @"physical_memory": @32,
        @"qimei": @"6a7f7a0d915bd07c63860e8e300018718212",
        @"region": @"CN",
        @"system_version": @"10.0.22621",
        @"vt_state": @1
    };
    NSDictionary * bodyValue = @{
        @"android": @"Android13",
        @"env_info": envInfo,
        @"groups": @[@"com.qqgame.happymj111"]
    };

    // 创建一个请求对象，对于v3版本使用initV3WithApi初始化，填入api、bodyValue等参数
    YYBMacJsonCmdRequest *request = [[YYBMacJsonCmdRequest alloc] initV3WithApi:@"pcyyb_get_rainbow_conf" bodyValue:bodyValue];
    
    // 发送请求
    [[YYBMacHttp sharedInstance] sendYYBJsonCmd:request success:^(YYBMacJsonCmdResponse * _Nonnull response) {
        // 成功响应
        // 响应体：response.responseObject，是整个响应的httpBody部分
        NSLog(@"JsonCmdTest v3 onJsonCmdResponse:%@", response.responseObject);
        // 响应头：response.responseHeaders，是整个响应的httpHeader部分
        NSLog(@"JsonCmdTest v3 onJsonCmdResponse headers:%@, content-type:%@", response.responseHeaders, response.responseHeaders[@"Content-Type"]);
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"JsonCmdTest v3 onJsonCmdFail: errCode:%ld, error:%@", error.code, error);
    }];
}

@end
