//
//  TVKLogger.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/20.
//

#import "TVKLogger.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

@implementation TVKLogger

- (void)logWithLevel:(TVKLogLevel)logLevel
                 tag:(NSString *)tag
                file:(const char *)file
            function:(const char *)function
                line:(NSUInteger)line
              format:(NSString *)format
                args:(va_list)args {
    [[YYBMacLog sharedInstance] log:(YYBMacLogLevel)logLevel tag:tag file:file func:function line:(int)line format:format, args];
}

- (void)logWithLevel:(TVKLogLevel)logLevel
                 tag:(NSString *)tag
                file:(NSString *)file
            function:(NSString *)function
                line:(NSUInteger)line
             content:(NSString *)content {
    
}

@end
