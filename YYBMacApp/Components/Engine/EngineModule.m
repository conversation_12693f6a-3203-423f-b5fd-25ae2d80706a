//
//  EngineModule.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/1.
//

#import <YYBMacFusionSDK/YYBMacLog.h>
#import <AppKit/AppKit.h>
#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import <YYBMacFusionSDK/YYBMacResHub.h>
#import <YYBMacFusionSDK/ResHubModel.h>
#import "EngineModule.h"
#import "FileUtils.h"
#import "BussinessMessageCenter.h"
#import "IdentifierConstants.h"
#import "YYBSocketDataSender.h"
#import "AsyncTaskManager.h"
#import "EngineUpdateTask.h"
#import "VMSUpdateTask.h"
#import "EngineDownloadProxy.h"
#import "YYBDefine.h"
#import "EngineInfoCenter.h"
#import "ResHubDownloader.h"


NS_ASSUME_NONNULL_BEGIN

static NSString* const kTag = @"EngineModule";

NSString* const kEngineBundleId = @"com.tencent.yybmac.engine";
NSString* const kStartEngineResult = @"startEngineResult";

@interface EngineModule()


@end

@implementation EngineModule

+ (nonnull instancetype)sharedInstance {
    static EngineModule* module = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        module = [[EngineModule alloc] init];
    });
    return module;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self registerCallbacks];
    }
    return self;
}

- (void)dealloc {
    [self unregisterCallbacks];
}

- (void)realStartEngine:(StartEngineCallback _Nullable)callback {
    YYBMacLogInfo(kTag, @"realStartEngine");
    NSURL *appURL = [NSURL fileURLWithPath:[[EngineInfoCenter shareInstance]getEnginePath]];
    NSWorkspaceOpenConfiguration *config = [NSWorkspaceOpenConfiguration configuration];
    [[NSWorkspace sharedWorkspace] openApplicationAtURL:appURL
                                          configuration:config
                                      completionHandler:^(NSRunningApplication * _Nullable app, NSError * _Nullable error) {
        if (app) {
            [self handleCallback:callback withResult:ENGINE_LAUNCH_SUCCESS andMsg:@""];
            [self sendStartEngineMsg:0];
            
            YYBMacLogInfo(kTag, @"引擎启动成功: %@", app);
        } else {
            [self handleCallback:callback withResult:ENGINE_LAUNCH_FAILED andMsg:@"engine launch failed."];
            [self sendStartEngineMsg:-1];
            
            YYBMacLogError(kTag, @"引擎启动失败: %@ 错误: %@",app, error);
        }
    }];
}

- (void)startEngine:(nullable StartEngineCallback)callback {
    __weak typeof(self) weakSelf = self;
    dispatch_sync(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        if (!weakSelf) {
            return;
        }
        if (![[ResHubDownloader sharedInstance] isReshubConfigPullFinished]) {
            // reshub配置请求未返回。等待初始化成功后，自然会触发启动引擎
            YYBMacLogInfo(kTag, @"startEngine.sdk未初始化完成");
            [weakSelf handleCallback:callback withResult:SDK_NOT_INITIALIED andMsg:@"SDK is not Initialied."];
            [YYBSocketDataSender sendMessage:@"engineCanNotBeUsed" with:nil to:kProcessBoastcast];
            return;
        }
        YYBMacLogInfo(kTag, @"startEngine isReshubConfigPullFinished is YES");
        
        // 检查是否引擎已启动
        NSArray<NSRunningApplication *> *apps = [NSRunningApplication runningApplicationsWithBundleIdentifier:kEngineBundleId];
        if (apps.count > 0 && [weakSelf enableUseLastEngine]) {
            YYBMacLogInfo(kTag, @"engine is running. return. count:%d", (int)apps.count);
            [weakSelf handleCallback:callback withResult:ENGINE_IS_RUNNING andMsg:@"engine is running."];
            [weakSelf sendStartEngineMsg:0];
            return;
        }
        
        
        // 1.检查引擎相关文件是否存在
        if (![weakSelf enableUseLastEngine]) {
            [weakSelf handleEngineFileNotExist:callback];
            return;
        }
        // 2. 额外检查，略
        // 3. 拉起引擎
        [weakSelf realStartEngine:callback];
    });
    
    
}

- (void)handleEngineFileNotExist:(nullable StartEngineCallback)callback {
    YYBMacLogInfo(kTag, @"handleEngineFileNotExist");
    [self handleCallback:callback withResult:FILE_NOT_FOUND andMsg:@"engine or vms not exists."];
    [self notifyDownloadIfNecessary];
    [YYBSocketDataSender sendMessage:@"engineCanNotBeUsed" with:nil to:kProcessBoastcast];
}

- (void)notifyDownloadIfNecessary {
    NSMutableDictionary* tasks = [[AsyncTaskManager sharedInstance] getTasks];
    BOOL needDownloadEngine = YES;
    BOOL needDownloadVms = YES;
    for (id key in tasks) {
        BaseAsyncInitTask* task = (BaseAsyncInitTask*)tasks[key];
        if (([task getTaskType] == ENGINE_UPDATE_TASK && task.isExecuting) || [[EngineInfoCenter shareInstance] enableUseEngine]) {
            YYBMacLogInfo(kTag, @"notifyDownloadIfNecessary. no need to execute engine update task.taskInfo:%@", task);
            needDownloadEngine = NO;
        }
        if (([task getTaskType] == VMS_UPDATE_TASK && task.isExecuting) || [[EngineInfoCenter shareInstance] enableUseVms]) {
            YYBMacLogInfo(kTag, @"notifyDownloadIfNecessary. no need to execute vms update task.taskInfo:%@", task);
            needDownloadVms = NO;
        }
        
    }
    if (needDownloadEngine) {
        EngineUpdateTask* engineTask =[[EngineUpdateTask alloc] initWithIdentifier:[[NSUUID UUID]UUIDString]];
        [[AsyncTaskManager sharedInstance] addTask:engineTask];
        [[AsyncTaskManager sharedInstance] startTaskWithIdentifier:engineTask.taskIdentifier];
    }
    if (needDownloadVms) {
        VMSUpdateTask* vmsTask = [[VMSUpdateTask alloc] initWithIdentifier:[[NSUUID UUID]UUIDString]];
        [[AsyncTaskManager sharedInstance]addTask:vmsTask];
        [[AsyncTaskManager sharedInstance] startTaskWithIdentifier:vmsTask.taskIdentifier];
    }
}

- (void)registerCallbacks {
    __weak typeof(self) weakSelf = self;
    [[BussinessMessageCenter sharedCenter] registerObserver:self forIdentifier:kEngineDownloadResult withHandler:^(BussinessMessage * _Nonnull message) {
        if (!weakSelf) {
            return;
        }
        if (message) {
            NSNumber *sucess = (NSNumber*)message.payload;
            if (![sucess isEqualToNumber:@1]) {
                YYBMacLogError(kTag, @"引擎下载失败");
                [weakSelf sendStartEngineMsg:-2];
                return;
            }
            if ([[EngineInfoCenter shareInstance] enableUseVms]) {
                YYBMacLogInfo(kTag, @"engine download success. try to start engine.");
                [weakSelf startEngine:nil];
            }
        }
        
    }];
    
    [[BussinessMessageCenter sharedCenter] registerObserver:self forIdentifier:kVMSDownloadResult withHandler:^(BussinessMessage * _Nonnull message) {
        if (!weakSelf) {
            return;
        }
        if (message) {
            NSNumber *sucess = (NSNumber*)message.payload;
            if (![sucess isEqualToNumber:@1]) {
                YYBMacLogError(kTag, @"vms下载失败");
                [weakSelf sendStartEngineMsg:-3];
                return;
            }
            if ([[EngineInfoCenter shareInstance] enableUseEngine]) {
                YYBMacLogInfo(kTag, @"vms download success. try to start engine.");
                [weakSelf startEngine:nil];
            }
        }
    }];
}

- (void)unregisterCallbacks {
    [[BussinessMessageCenter sharedCenter] unregisterObserver:self];
}

- (void)sendStartEngineMsg:(int)ret {
    [YYBSocketDataSender sendMessage:kStartEngineResult with:[[NSMutableDictionary alloc] initWithDictionary:@{@"ret": @(ret)}] to:kProcessBoastcast];
}


- (void)handleCallback:(nullable StartEngineCallback)callback withResult:(StartEngineResult)res andMsg:(NSString*)msg  {
    if (callback) {
        callback(res, msg);
    }
}


// 临时逻辑。如果url有更新，即使当前引擎已启动，需要直接下载；如果当前下载路径不存在，也先下载
- (BOOL)enableUseLastEngine {
    ResHubModel* model = [[YYBMacResHub sharedInstance] resourceWithId:kVmsResId preferLocal:NO needValidate:NO];
    YYBMacLogInfo(kTag, @"enableUseLastEngine.model:%@", model);
    BOOL enableEngine = [[EngineInfoCenter shareInstance] enableUseEngine];
    BOOL enableVms = [[EngineInfoCenter shareInstance] enableUseVms];
    YYBMacLogInfo(kTag, @"enableEngine:%d enableVms:%d", enableEngine, enableVms);
    return enableEngine && enableVms;
}



@end

NS_ASSUME_NONNULL_END
