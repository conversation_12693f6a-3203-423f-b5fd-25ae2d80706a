CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/../dev_pod/TVKPlayerForMac" "${PODS_ROOT}/Beacon/BeaconMacSDK" "${PODS_ROOT}/TPDownloadProxyMac" "${PODS_ROOT}/ThumbPlayerMacOSX" "${PODS_XCFRAMEWORKS_BUILD_DIR}/TVKPlayerForMac" "${PODS_XCFRAMEWORKS_BUILD_DIR}/ThumbPlayerMacOSX"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/JLRoutes" "${PODS_ROOT}/Headers/Public/Lottie" "${PODS_ROOT}/Headers/Public/Reachability" "${PODS_ROOT}/Headers/Public/SSZipArchive"
LD_RUNPATH_SEARCH_PATHS = $(inherited) '@executable_path/../Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/JLRoutes" "${PODS_CONFIGURATION_BUILD_DIR}/Lottie" "${PODS_CONFIGURATION_BUILD_DIR}/Reachability" "${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive"
OTHER_CFLAGS = $(inherited) -fmodule-map-file="${PODS_ROOT}/Headers/Public/SSZipArchive/SSZipArchive.modulemap" -iframework "${PODS_ROOT}/TPDownloadProxyMac" -iframework "${PODS_ROOT}/../dev_pod/TVKPlayerForMac" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/TVKPlayerForMac" -iframework "${PODS_ROOT}/ThumbPlayerMacOSX" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/ThumbPlayerMacOSX"
OTHER_LDFLAGS = $(inherited) -ObjC -l"JLRoutes" -l"Lottie" -l"Reachability" -l"SSZipArchive" -l"c++" -l"iconv" -l"resolv" -l"xml2" -l"z" -framework "AVFoundation" -framework "AppKit" -framework "BeaconAPI_Base" -framework "CoreMedia" -framework "CoreVideo" -framework "DownloadProxyFramework" -framework "Foundation" -framework "Security" -framework "SystemConfiguration" -framework "TVKPlayerForMac" -framework "ThumbPlayer" -framework "VideoToolbox"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/Beacon" "-F${PODS_CONFIGURATION_BUILD_DIR}/JLRoutes" "-F${PODS_CONFIGURATION_BUILD_DIR}/Lottie" "-F${PODS_CONFIGURATION_BUILD_DIR}/Reachability" "-F${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive" "-F${PODS_CONFIGURATION_BUILD_DIR}/TPDownloadProxyMac" "-F${PODS_CONFIGURATION_BUILD_DIR}/TVKPlayerForMac" "-F${PODS_CONFIGURATION_BUILD_DIR}/ThumbPlayerMacOSX"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/SSZipArchive/SSZipArchive.modulemap"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
