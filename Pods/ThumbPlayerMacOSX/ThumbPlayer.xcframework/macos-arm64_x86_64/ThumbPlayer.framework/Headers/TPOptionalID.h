/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file    TPOptionalID.h
 * @brief   optional param key定义
 *          optional key的声明格式如下: OPTIONAL_ID+设置时机（BEFORE/GLOBAL）+ 数据类型 (BOOL/LONG(int64_t)/FLOAT/STRING/QUEUE_INT/QUEUE_STRING/OBJECT)+ 名称。
 *          注意：LONG类型对应oc和c语言中的int64_t类型
 *          举例说明：
 *              设置时机 BEFORE：表示这个参数必须在prepareAsync之前设置，否则设置无效
 *              设置时机 GLOBAL：表示这个参数在播放过程中都可以设置（包含BEFORE时机），设置之后就立即生效
 *              OPTIONAL_ID_BEFORE_LONG_START_PLAYING_TIME_MS表示此option id是用于设置开始播放的时间点，
 *              它的value的数据类型是long(int64_t)类型， 需要在prepareAsync之前设置。
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/7
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// TPOptionalId的类型别名
typedef NSString *TPOptionalID NS_STRING_ENUM;

#pragma mark - 播放器通用相关的配置

/// 播放器调度线程优先级
/// @see TPThreadPriority
/// 默认值：TP_THREAD_PRIORITY_DEFAULT(承继调用方线程优先级)
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntPlayerSchedulingThreadPriority;

/// 设置下载组件开关配置，特殊场景不能使用代理，比如直播答题
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolUseDownloadProxy;

/// 是否允许截屏,如果是使用系统播放器，在播放杜比Vision、HDR10、Fairplay等源时，必须手动关闭
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableSnapShot;

/// AVPlayer的preferredForwardBufferDuration设置,单位为毫秒(Ms)
/// 默认值：系统AVPlayer默认值
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongAVPlayerBufferDurationMs;

/// 画中画起播延迟时长设置，单位为毫秒(Ms)
/// 默认值：500ms
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongPictureInPictureStartDelayMs;

/// 设置画中画是否需要快进快退按钮
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnablePictureInPictureSeekButton;

/// 在AirPlay场景下,播放状态如果发生了缓冲,再AVPlayer的load state变更为buffer full时,调用暂停再启播,兼容无法一直无法启播的问题
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAutoPlayIfBufferFullInAirPlayBufferingWorkaround;

/// 设置是否开启本地代理http加密校验
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolUseProxyAuthentication;

/// TPPictureInPictureController的一个crash修复开关。控制TPSystemPlayer对象dealloc时， 是否在释放pipController前加10ms延时
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableDelayReleasePIPController;

/// 是否开启复用播放器。开启后，播放器在被调用reset后，下次播放时不会重新创建播放器。否则，每次reset后，下次播放都会创建新的播放器。
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableReusePlayer;

/// TODO(ahailiu, 2024/9/5) 版本稳定性配置开关，计划ThumbPlayer 3.05删除
/// 是否使能AVPlayer报解码器暂时不可用告警时报错
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAvplayerDecoderUnavailableError;

#pragma mark - demuxer相关的配置

/// 开始播放的时间点
/// 默认值0
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongStartPlayingTimeMs;

/// 跳转到开始播放的时间点时，是否使用精确seek
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolStartPlayingTimeAccurateSeek;

/// demuxer(网络读包+协议解析)线程优先级
/// @see TPThreadPriority
/// 默认值：TP_THREAD_PRIORITY_DEFAULT(承继调用方线程优先级)
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntDemuxerThreadPriority;

/// 数据缓冲区的大小，用总时长数量表示，单位毫秒
/// 默认值：6000ms
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongBufferPacketTotalDurationMs;

/// 首次播放加载数据大小，用于控制缓存数据量来通知Prepared，用总时长表示，单位毫秒
/// 默认值：200ms
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongPreparePacketTotalDurationMs;

/// 缓冲时（缓冲数据不够引起的二次缓冲，或seek引起的拖动缓冲）最少要缓存多长时间的数据才能结束缓冲，回抛bufferingEnd，单位ms
/// 默认值：4000ms
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongBufferingForPlaybackMs;

/// 缓冲超时时间，即最大允许的缓冲时间，超过这个时间，则播放器将报错
/// 默认值：60000ms
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongBufferingTimeoutMs;

/// 是否启用严格的缓冲策略.
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableStrictBufferingStrategy;

// TODO(amytwang 2024/11/4):为了方便做实验，后续稳定后删除配置
/// 是否启用seek时先处理外挂轨的flush操作.
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableFirstFlushExternalTrackWhenSeeking;

// TODO(amytwang 2025/1/7):为了方便做实验，后续稳定后删除配置
/// 是否在start时创建二缓observer.
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableCreateBufferingObserverWhenStart;

/// 是否要保持原始流的PTS值。如果为YES，则输出到render的PTS会保持原始流的值，可能做时间单位的变换，但不会做任何其他形式的变换（比如对PTS跳变进行重新对齐）
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolKeepOriginalPts;

/// HLS格式的回调Tag, 例如设置：["#EXT-QQHLS-AD,#EXT-X-PROGRAM-DATE-TIME"]
/// 默认值: nil
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeQueueStringHlsTagCallback;

/// prepare超时时间，超过这个时间，则直接报错，单位ms
/// 默认值：60000ms
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongPrepareTimeoutMs;

/// 设置起播时的流分析最大时长（当前仅对ffmpeg有效，较小的值有利于降低起播耗时，但会增大流分析失败的机率)，单位ms
/// 默认值：0，表示使用FFmpeg内置的默认大小(5000ms)
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongMaxAnalyzeDurationMs;

/// 选择播放器demuxer策略, standalone,ffmpeg demuxer
/// @see TPDemuxerType
/// 默认值:内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeQueueIntDemuxerType;

/// 设置缓冲机制
/// @see TPBufferStrategy
/// 默认值：auto，自动模式，由内部自己决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntBufferStrategy;

/// 设置当直播出现高延迟时的需要采取的措施
/// @see TPReduceLiveLatencyStrategy
/// 默认值：不采取任何措施 TP_REDUCE_LIVE_LATENCY_STRATEGY_NONE
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntReduceLiveLatencyAction;

/// 当TPOptionalIDBeforeIntReduceLatencyAction设置成TP_REDUCE_LIVE_LATENCY_STRATEGY_UP时，需要加速播放的倍速值，必须大于1.0
/// @see TPReduceLiveLatencyStrategy
/// 默认值：1.0
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeFloatReduceLatencySpeedUpRate;

/// 当TPOptionalIDBeforeIntReduceLatencyAction不为TP_REDUCE_LIVE_LATENCY_STRATEGY_NONE时，
/// 缓冲区缓存数据量到达一定阈值时，开始降低延时，单位：ms
/// 默认值：0
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongBeginReduceLatencyBufferPacketDurationMs;

/// JitterBuffer参数
/// @see TPJitterBufferParams
/// 默认值：nil， 内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeObjectJitterBufferParams;

/// 配置业务想接收的定制化SEI类型，取值为NSNumber类型（原始类型为int）的NSArray数组
/// 默认值：nil
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeQueueIntSpecialSeiTypesCallback;

/// 设置是否开启精确duration计算，主要是为了aac文件。 因为aac文件是音频裸码流，没有duration信息，FFMPEG获取到的duration是根据比特率计算的，可能不准确。
/// 如果需要获取精确的duration信息，需要打开该设置开关。对于时长比较长的文件，不建议打开该设置，因为对首缓可能有影响
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAccurateDuration;

/// 设置是否允许忽略音频文件中的视频流。主要作用于"mp3","wav","wma","aac","flac","ogg","ape"等常见的音频格式文件
/// 默认值：YES，忽略常见音频格式文件中的视频流。
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableIgnoreVideoStreamInCommonAudioFormats;

/// 在数据源切换新策略中使用default/keep no buffer,模式下保留包队列中最小包队列时长(从从包队列头部偏移该时长找关键帧,找到则移除该帧以及后续所有数据)，单位ms.
/// 默认值：2000ms
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongMinLeftPacketQueueTotalDurationMsForSwitchDataSource;

/// 如果支持avs3 audio音频解码，需要传入对应解码模型绝对路径，否则无法解码；解封装时FFMPEG尝试打开解码器，所以此模型配置放置于demuxer相关项
/// 默认值：""
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeStringAvs3AudioDecoderModelPath;

/// avs3 audio音频解码多线程解码配置。可配置多线程的数量
/// 默认值：0 不对解码器进行外部多线程数量配置，由内部自行判断
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntAvs3AudioDecoderThreadCount;

/// 设置打开音频解码器sample format向上输出模式
/// true: bitdepth向上靠近，最大32bit.尽量保留最大动态范围 8/16bit ->S16, S24/32/64/DBL/FLT -> S32
/// false: 保持老flow，统一S16输出. 兼容性强
/// 默认值 false
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAudioDecoderSampleFormatUpwardOutputMode;

/// 设置打开音频解码器sample format向上输出模式的codec类型列表{api/common/TPAudioCodecType.h}
/// 默认值：空，内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeQueueIntAudioSampleFormatUpwardOutputCodecs;

/// 设置是否使能新的FLV的特殊配置解析
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableNewFlvSpecialConfigParse;

/// 设置是否使能FFmpeg HLS直播拉新m3u8首片不连续修复逻辑
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableFFmpegLiveHlsFirstSegmentDiscontinuityFix;

/// 设置是否使能片内seek修复逻辑
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableSegmentInternalSeekFix;

#pragma mark - decoder相关的配置

/// Decoder能容忍的最大错误次数，超过这个次数就按配置的或者默认的策略切换至下一个解码
/// 默认值：100
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntDecoderMaxToleratedErrorCount;

/// 解码参数发生改变后，是否需要reset decoder， 默认YES， 但是在一些定制平台上，如企鹅盒子，如果发生编码参数变化（如清晰度变化），MediaCodec内部能自己检测解码参数并reset，
/// 如果我们调用它们的MediaCodec接口重置的话，会有很长的耗时，导致清晰度切换过程出现卡顿。
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolResetDecoderOnParameterChange;

/// 音频解码策略，int数组类型
/// @see TPDecoderType
/// 默认值：内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeQueueIntAudioDecoderType;

/// 视频解码策略，int数组类型
/// @see TPDecoderType
/// 默认值：内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeQueueIntVideoDecoderType;

/// 视频解码线程优先级
/// @see TPThreadPriority
/// 默认值：TP_THREAD_PRIORITY_DEFAULT(承继调用方线程优先级)
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntVideoDecoderThreadPriority;

/// 音频解码线程优先级
/// @see TPThreadPriority
/// 默认值：TP_THREAD_PRIORITY_DEFAULT(承继调用方线程优先级)
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntAudioDecoderThreadPriority;

/// 仅针对iOS VTDecompressionSession有效，是否开启IO-Surface-Backed
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolVideoEnableIOSurfacePropertiesKey;

/// 在播放30帧及以下的源，解码前丢帧策略下的最大帧率。
/// 默认值：std::numeric_limits<float>::max()
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeFloatMaxFramerateForDecoderFrameDropStrategy30fpsOrLowerSource;

/// TODO(amytwang 2025/2/8)：临时开关，后续稳定后删除
/// 是否使能新的additional info信息匹配逻辑。
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableNewAdditionalInfoMatcher;

/// TODO(ahailiu 2025/4/14)：临时开关，后续稳定后删除
/// 是否使能video toolbox生命周期管理。
/// 默认值: NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableVideoToolboxLifeCycleManagement;

/// TODO(ahailiu 2025/4/14)：临时开关，后续稳定后删除
/// 是否使能video toolbox回调OSStatus。
/// 默认值: NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableVideoToolboxCallbackOSStatus;

/// demuxer触发的video param changed是否只处理一次
/// 默认值: NO
/// TODO(hemanli, 2025.7.10): 临时配置，过几个版本删掉
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolDemuxerVideoParamChangedProcessOnlyOnce;

#pragma mark - 音频后处理相关的配置

/// 设置通过 IOnAudioFrameOutputListener 回抛的音频格式
/// @see TPSampleFormat
/// 默认值：TP_SAMPLE_FMT_NONE， 依视频源，内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntOutAudioSampleFormat;

/// 设置通过 IOnAudioFrameOutputListener 回抛的音频声道布局
/// @see TPChannelLayout
/// 默认值：依视频源，内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongOutAudioChannelLayout;

/// 设置通过 IOnAudioFrameOutputListener 回抛的音频声道布局
/// @see TPChannelLayout
/// 默认值：依视频源，内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntOutAudioSampleRateHz;

/// 设置通过 IOnAudioFrameOutputListener 回抛的音频帧个数
/// 默认值：0，依视频源，内部决策 如设置则不低于256
/// samples = duration(ms) * sample_rate(Hz) / 1000
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntOutAudioNbSamplesPerChannel;

/// 设置avs3 audio（audio vivid）双耳渲染引擎的HOA阶数，最高7阶
/// 默认值：3
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntAvs3AudioRenderHoaOrder;


/// 设置打开音频后处理Converter默认S16输出模式
/// TODO(eriktang, 2025-05-02):临时开关，后续删除
/// true: 打开 false: 关闭
/// 默认值 true
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAudioFormatConverterFixedSampleFormatS16Out;

#pragma mark - 视频后处理相关的配置

/// 设置通过 IOnVideoFrameOutListener 回抛的视频格式
/// @see TPPixelFormat
/// @see IOnVideoFrameOutListener
/// 默认值：TPPixelFormatUnknown，依视频源，内部决策。
/// 注意：使用系统播放器时，只支持TPPixelFormatCVPixelBuffer和TPPixelFormatBGRA，其他格式会默认当做TPPixelFormatCVPixelBuffer处理
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntOutVideoPixelFormat;

/// 设置通过 IOnVideoFrameOutListener 回抛的视频宽度
/// @see IOnVideoFrameOutListener
/// 默认值：0，依视频源，内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntOutVideoWidth;

/// 设置通过 IOnVideoFrameOutListener 回抛的视频高度
/// @see IOnVideoFrameOutListener
/// 默认值：0，依视频源，内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntOutVideoHeight;

/// 设置通过 IOnVideoFrameOutListener 回抛的视频旋转角度
/// @see IOnVideoFrameOutListener
/// 默认值：0，依视频源，内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntOutVideoRotation;

#pragma mark - 同步机相关的配置
/// 使能同步机setup之后才deliver 视频帧
/// TODO(ahailiu 2025.8.8) 临时过渡配置开关，稳定后移除
/// 默认值: NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableDeliveringVideoFramePostSetup;
#pragma mark - render相关的配置

/// 是否使能新的pts scaler
/// 默认值: NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableNewPtsScaler;

#pragma mark - render相关的配置
/// 是否开启音视频同步。对于一些特殊应用场景，需要关闭音视频同步，比如，输出预览用的一组图像， 把播放器当作转码器的数据源提供方，等等
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAvSync;

/// 在处理音视频同步的时候，是否把Audio Render底层的latency计算进来。特别针对部分高延迟的蓝牙耳机
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAudioRendererLatencyCompensation;

/// 音频渲染类型列表
/// @see TPAudioRendererType
/// 默认值：空，内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeQueueIntAudioRendererType;

/// 视频渲染类型列表
/// @see TPVideoRendererType
/// 默认值：空，内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeQueueIntVideoRendererType;

/// 是否开启根据当前屏幕刷新率进行丢帧处理逻辑
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableDropFrameByRefreshRate;

/// 是否开启颜色管理功能，渲染器将会依据帧所遵循的标准进行正确的yuv->rgb转换
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const  TPOptionalIDBeforeBoolEnableColorManagement;

/// 当调用音频渲染器的flush函数时，是否重置渲染器，仅对部分的音频渲染器生效
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolResetAudioRendererWhenFlush;

/// 是否响应UIView的contentMode,如果配置需要响应UIView的contentMode，则setVideoGravity接口不再生效
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableViewContentMode;

/// 是否使能适配帧类型的audio queue
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAdaptiveAudioQueue;

/// 视频渲染监测周期，单位ms
/// 默认值：0ms, 即不进行监测
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongVideoRenderMonitorPeriodMs;

/// 若在TPOptionalIDBeforeLongVideoRenderMonitorPeriodMs时间内视频丢帧率高于该阈值 则通过oninfo中的TP_INFO_VIDEO_HIGH_FRAME_DROP_RATE消息进行通知
/// 取值范围[0.0f, 1.0f]
/// 默认值：1.0f，即不进行通知
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeFloatVideoHighFrameDropRateThreshold;

/// 若在TPOptionalIDBeforeLongVideoRenderMonitorPeriodMs时间内视频帧率低于该阈值 则通过oninfo中的TP_PLAYER_INFO_LONG2_VIDEO_LOW_FRAMERATE进行通知
/// 取值范围[0.0f, FLOAT_MAX]
/// 默认值：0.0f，即不进行通知
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeFloatVideoLowFrameRateThreshold;

/// 若前后渲染的两帧时间间隔超过TPOptionalIDBeforeLongVideoLargeRenderIntervalThresholdMs
/// 则通过oninfo中的TPOnInfoIDLong1VideoLargeRenderIntervalMs进行通知
/// 默认值：0，即不进行通知
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongVideoLargeRenderIntervalThresholdMs;

/// 是否启用后处理调节音量
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAudioVolumeByPostprocess;

/// 是否开启自适应帧率
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableVideoAdaptiveFrameRate;

/// 外挂字幕的输出方式
/// @see TPSubtitleDataType
/// 默认值：TPSubtitleDataTypeText，字符串输出
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntOutSubtitleDataType;

/// 指定字幕渲染器类型
/// @see TPSubtitleRendererType
/// 默认值：空，内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeQueueIntSubtitleRendererType;

/// 音轨选择失败是否报错
/// 默认值: NO
/// TODO(hemanli, 2024.3.31): 临时配置，过几个版本删掉
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolTreatSelectAudioTrackFailureAsFatalError;

/// 是否回调字幕数据
/// 默认值：NO 不回调
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableSubtitleDataCallback;

/// 是否使能音频渲染在写入渲染器所有数据后，需等待所有数据被真正渲染完成的能力
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAudioRenderWaitUntilCompleted;

/// 是否使能audio queue新latency计算
/// TODO(jahnliang, 2023/6/12): ios音画同步优化的临时开关，没问题后删除
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAudioQueueNewLatency;

/// 仅针对mac端，同步时是否计算homepod/蓝牙耳机等硬件的音频延迟
/// TODO(ultramli, 2024/11/14): mac端验证硬件音频延迟计算功能是否正常的临时开关，待版本稳定后删除，ios端暂时还未实现硬件音频延迟计算
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableOSXDeviceAudioLatency;


/// 控制displayerlayer首帧渲染后， 后续渲染多少帧后需要做微调view触发layout，用于修复iOS画中画渲染画面出现在右下角的问题
/// 默认值为7，即首帧渲染后 后续渲染的7帧每帧后做一下FineTunningBounds
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeIntDisplayLayerFineTunningBoundsNum;

/// 是否开启音频倍数强制处理
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAudioSpeedForceProcess;

/// 是否使能metal渲染器同步渲染
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableMetalRenderSync;

/// 是否使能渲染器无限重试
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableRendererUnlimitedRetry;

/// 是否使能音频渲染器通道布局转换
/// 默认值：YES
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolEnableAudioRendererChannelLayoutConversion;

/// 本次播放使能的HDR MAPPING类型
/// 默认值：空集合，内部决策
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeQueueIntHdrMappingType;

/// seek过程中，收到buffering end，是否start
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeBoolAllowStartAfterBufferingEndWhenSeeking;

/// 缓冲过滤阈值，即小于该阈值的缓冲会被过滤掉
// 默认100ms
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeLongBufferingFilterThresholdMs;

/// 是否启用在opengl每次渲染一帧时绑定渲染目标, 默认值:NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeEnableOpenglBindRenderTargetOnRenderFrame;

/// 是否打开webvtt重置playlist状态的逻辑
/// TOOD(hemanli, 2025.5.22): 临时开关，稳定后删除
/// 默认为NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDBeforeEnableEnableWebVTTResetPlaylistState;

#pragma mark - Global参数

/// 设置杜比音频解码的输出reference level
/// 默认值：-17
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalLongDolbyOutputReferenceLevel;

/// 是否打开从audio render发出的数据回抛到上层做后处理
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableAudioPostprocessCallback;

/// 是否打开从video render发出的数据回抛到上层做后处理
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableVideoPostprocessCallback;

/// 设置跳过片尾的时间（区别于position，例如需要跳过最后一分钟的片尾，当前传输应该是 60 * 1000)，单位ms
/// 默认值：0，不跳过片尾，播放完整个视频
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalLongSkipEndTimeMS;

/// 是否允许app处于后台时后进行画面渲染（自研播放器画中画必须开启）
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableVideoRenderInBackground;

/// 字幕渲染参数
/// @see TPSubtitleRenderParams
/// 默认值：参考TPSubtitleRenderParams中的定义
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalObjectSubtitleRenderParams;

/// 是否打开自适应清晰度切换开关 thumbPlayer内部进行清晰度自适应切换，目前仅支持标准嵌套m3u8资源
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableAdaptiveSwitchDef;

/// 自适应清晰度切换设置可支持的bitrate的range范围下限, 默认不设下限, 单位bps.
/// 默认值：0，不设下限
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalLongAdaptiveLimitBitrateRangeMin;

/// 自适应清晰度切换设置可支持的bitrate的range范围上限, 默认不设上限, 单位bps.
/// 默认值：0，不设上限
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalLongAdaptiveLimitBitrateRangeMax;

/// 是否开启当播放器类型为系统播放器，退后台可以自动进入画中画播放
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableSystemPlayerAutoPIPInBackground;

/// 是否开启视频解码帧回调
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableVideoFrameCallback;

/// 开启视频解码帧回调时，是否在暂停状态下立刻回调一帧视频帧
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID
const TPOptionalIDGlobalBoolCallbackOneFrameWhenEnableVideoFrameCallbackOnPaused;

/// 是否开启音频解码帧回调
/// 默认值：NO
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableAudioFrameCallback;

/// ffmpeg的filter grash参数描述字符串，例如："stereotools=mode=lr>ms[ms];[ms]stereotools=delay=-20[out];[out]volume=volume=6dB"
/// 设置为空字符串则为关闭音效
/// 默认值：""
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalStringFFmpegAudioFilterDescription;

/// AVPlayerItem的allowedAudioSpatializationFormats设置，具体类型为，iOS14支持
/// @see AVAudioSpatializationFormats
/// 默认值：AVAudioSpatializationFormatNone
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalIntAVPlayerSpatialAudioFormats;

/// 是否打开下载组件建议码率回调开关, 如果不设置, 默认为关闭 回调建议码率 (单位为bps)
/// 默认值：NO,关闭
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableSuggestedBitrateCallBack;

/// MediaLabVR流的viewport字符串参数，欧拉角信息，以","隔开，分别是yaw,pitch,roll,x,y
/// yaw   : 围绕Z轴旋转的角度，pitch : 围绕Y轴旋转的角度，roll  : 围绕X轴旋转的角度
/// x     : FOV的横向弧度，y     : FOV的纵向弧度
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalStringMediaLabVRViewport;

/// 是否开启空间音频渲染
/// 默认值：false
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableAudioSpatialRendering;

/// 是否开启新的腾讯空间音频渲染
/// 默认值：false
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableTxNewAudioSpatialRendering;

/// 是否打开倍速处理新flow - TODO(eriktang,2025/01/14):临时开关,验证完成后删除
/// 默认值：false
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableAudioSpeedEffectNewFlow;

/// 音频后处理pipeline inloop设置playback rate - TODO(eriktang,2025/03/25):临时开关,验证完成后删除
/// 默认值：true
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableAudioSetPlaybackRateInLoop;

/// 指定在某些时间点回调，即播放器播放到某个时间点时，通过OnInfo给外面一个回调。
/// 比如，如果配置[5000, 15000]，则播放器播放到5000ms和15000ms时分别给外面一个回调，
/// 如果设置重复的时间点，内部会去重，即相同时间点只回调一次。
/// 回调请参考TPOnInfoIDLong1TimePointMsReached
/// 设置之后，如要关闭，则将optional param设置为空列表
/// 默认值：空列表
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalQueueLongTimePointMsCallback;

/// 当回调字幕图片数据时，是否携带字幕渲染信息
/// 如果开启，则在通过[ITPPlayerDelegate onPlayer:subtitleDataOut]回调TPSubtitleFrameBuffer时，
/// 会通过TPSubtitleFrameBuffer的additionalInfoDict携带TPSubtitleRenderInfo
/// 默认不开启
FOUNDATION_EXPORT TPOptionalID const TPOptionalIDGlobalBoolEnableSubtitleFrameWithRenderInfoWhenCallbackImageData;

NS_ASSUME_NONNULL_END
