/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPSubtitleFrameBuffer.h
 * @brief    字幕帧数据定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/03/16
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPSubtitleData.h"
#import "TPPixelFormat.h"
#import "TPAdditionalInfoType.h"

NS_ASSUME_NONNULL_BEGIN

/// 字幕帧数据
@interface TPSubtitleFrameBuffer : TPSubtitleData

/// 字幕frame的数据
/// 目前仅支持RGBA，data[0]存放图片数据，lineSize[0]为数据size，
/// 如果某个时间段所选择的每路字幕轨都没有字幕，则data[0]返回完全透明的空白图片。
/// 如果选择了多路，会合成为一张图片
@property (nonatomic, assign, readonly) uint8_t **data;

/// 数据size，lineSize[0]对应的是data[0]的size，lineSize[1]对应data[1]的size，依此类推。
/// mLineSize的长度视格式而定，因目前仅支持RGBA，所以只用到lineSize[0]
@property (nonatomic, strong, readonly) NSArray<NSNumber *> *lineSize;

/// 字幕frame的格式, 目前仅支持TPPixelFormatRGBA
@property (nonatomic, assign, readonly) TPPixelFormat format;

/// 字幕frame的原始宽
@property (nonatomic, assign, readonly) int width;

/// 字幕frame的原始高
@property (nonatomic, assign, readonly) int height;

/// 字幕frame的旋转角度，取值为0、90、180、270
@property (nonatomic, assign) int rotation;

/// 字幕frame的显示宽
@property (nonatomic, assign) int displayWidth;

/// 字幕frame的显示高
@property (nonatomic, assign) int displayHeight;

/// 当前输出的字幕的轨道id列表，该轨道id即ITPPlayer#getTrackInfo返回的数组下标。
@property (nonatomic, strong) NSArray<NSNumber *> *trackIDs;

/// 携带额外信息，请参考TPAdditionalInfoType
/// key类型为NSNumber，其值为TPAdditionalInfoType的枚举值
@property (nonatomic, copy) NSDictionary<NSNumber *, id> *additionalInfoDict;

- (instancetype)init NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END
