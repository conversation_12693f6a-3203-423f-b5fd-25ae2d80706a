/*****************************************************************************
 * @copyright  Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file       TPMediaAssetFactory.h
 * @brief      asset创建工厂
 * <AUTHOR>
 * @version    1.0.0
 * @date       2023/3/13
 * @license    GNU General Public License (GPL)
 *****************************************************************************/

#import "TPDrmType.h"
#import "ITPDrmMediaAsset.h"
#import "ITPMediaAssetParamMap.h"
#import "ITPMultiMediaAsset.h"
#import "ITPRtcMediaAsset.h"
#import "ITPUrlMediaAsset.h"
#import "ITPSimulatedLiveMediaAsset.h"

NS_ASSUME_NONNULL_BEGIN
/// asset创建工厂
@interface TPMediaAssetFactory : NSObject

/// 创建url播放媒体资源
/// @param url 播放地址
/// @return url播放媒体资源实例
+ (id<ITPUrlMediaAsset>)createUrlMediaAsset:(NSString *)url;

/// 创建组合型播放媒体资源，例如多个分片mp4的组合
/// @return TPMultiMediaAsset实例
+ (id<ITPMultiMediaAsset>)createMultiMediaAsset;

/// 创建加密播放媒体资源
/// @param url 播放地址
/// @param drmType drm类型
/// @param certificateUrl 证书url，除FairPlay外其它Drm类型对应理解为provision
/// @param licenseUrl 解密密钥的url
/// @return TPDrmMediaAsset实例
+ (id<ITPDrmMediaAsset>)createDrmMediaAsset:(NSString *)url
                                    drmType:(TPDrmType)drmType
                             certificateUrl:(NSString *)certificateUrl
                                 licenseUrl:(NSString *)licenseUrl;

/// 创建RTC直播媒体资源
/// @param url 播放地址
/// @return TPRtcMediaAsset实例
+ (id<ITPRtcMediaAsset>)createRtcMediaAsset:(NSString *)url;

/// 创建模拟直播资源
/// @param startAsset 起始播放的asset
/// @param simulatedLiveDelegate  delagate
+ (id<ITPSimulatedLiveMediaAsset>)createSimulatedLiveMediaAsset:(id<ITPMediaAsset>)startAsset
                                                       delegate:(nullable id<ITPSimulatedLiveDelegate>)simulatedLiveDelegate;

/// 创建设置给媒体资源的有序参数map
/// @return 返回创建的ITPMediaAssetParamMap
+ (id<ITPMediaAssetParamMap>)createMediaAssetParamMap;
@end

NS_ASSUME_NONNULL_END
