/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPRichMediaFeature.h
 * @brief    富媒体功能信息定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 富媒体功能信息定义. https://iwiki.woa.com/pages/viewpage.action?pageId=327232578
@interface TPRichMediaFeature : NSObject

///富媒体功能类型, 对应master中result的type字段
@property (nonatomic, copy) NSString *featureType;

///绑定的视频格式列表，对应master中result的binding字段. 空数组表示通用于所有格式
@property (nonatomic, copy, nullable) NSArray<NSString *> *bindings;

///当前富媒体功能是否有被选中。
///对应 ITPRichMediaSynchronizer的selectFeatureAsync等相关方法的调用。
///在其他类的接口中，此值无意义，默认值为false.
@property (nonatomic, assign, getter=isSelected) BOOL selected;
@end

NS_ASSUME_NONNULL_END
