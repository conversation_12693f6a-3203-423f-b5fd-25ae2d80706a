/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPTrackInfo.h
 * @brief    track（轨道）信息，包括音频、视频、字幕轨道
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/3
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITPContainerInfo.h"
#import "TPContainerType.h"
#import "TPDashFormat.h"
#import "TPHlsTag.h"
#import "TPMediaType.h"

/// track（轨道）信息，包括音频、视频、字幕轨道
@interface TPTrackInfo : NSObject

/// 轨道媒资类型
@property (nonatomic, assign) TPMediaType mediaType;

/// 轨道名字, 用于外挂字幕/音轨设置名字
@property (nonatomic, copy, nullable) NSString *name;

/// 轨道语言, 用于外挂字幕设置语言
@property (nonatomic, copy, nullable) NSString *language;

/// 当前轨道是否被选中
@property (nonatomic, assign) BOOL selected;

/// 如果是true，该类型轨道每个时刻只有一条能被选中，如果是false，该类型轨道可以同时选中多条
@property (nonatomic, assign) BOOL exclusive;

/// 如果是true，该类型轨道为内部轨道，如果是false，该类型轨道为外部轨道。
@property (nonatomic, assign) BOOL internal;

/// 封装格式信息，可以是TPHlsTag、TPDashFormat或者nil，如果不为nil，可以从ITPContainerInfo获取containerType，
/// 从而根据containerType转换为TPHlsTag或TPDashFormat
@property (nonatomic, strong, nullable) id<ITPContainerInfo> containerInfo;

@end
