/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPPixelFormat.h
 * @brief    视频pixel format定义， 枚举值必须有native层(TPPixelFormat.h)保持一致
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/28
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 视频格式定义，枚举值须跟内核保持一致
typedef NS_ENUM(NSInteger, TPPixelFormat) {
    /// 未定义
    TPPixelFormatUnknown = -1,
    /// planar YUV 4:2:0, 12bpp, (1 Cr & Cb sample per 2x2 Y samples)
    TPPixelFormatYUV420P = 0,
    /// rgb24
    TPPixelFormatRGB24 = 2,
    /// planar YUV 4:2:0, 12bpp, full scale (JPEG), deprecated in favor of TP_PIX_FMT_YUV420P and setting color_range
    TPPixelFormatYUVJ420P = 12,
    /// packed RGBA 8:8:8:8, 32bpp, RGBARGBA.
    TPPixelFormatRGBA = 26,
    /// packed BGRA 8:8:8:8, 32bpp, BGRABGRA...
    TPPixelFormatBGRA = 28,
    /// packed RGB 5:6:5, 16bpp, (msb)   5R 6G 5B(lsb), little-endian.
    TPPixelFormatRGB565 = 37,
    /// planar YUV 4:2:0, 15bpp, (1 Cr & Cb sample per 2x2 Y samples), big-endian
    TPPixelFormatYUV420P10BE = 63,
    /// planar YUV 4:2:0, 15bpp, (1 Cr & Cb sample per 2x2 Y samples), little-endian
    TPPixelFormatYUV420P10LE = 64,
    /// CVPixelBufferRef
    TPPixelFormatCVPixelBuffer = 160,
};
