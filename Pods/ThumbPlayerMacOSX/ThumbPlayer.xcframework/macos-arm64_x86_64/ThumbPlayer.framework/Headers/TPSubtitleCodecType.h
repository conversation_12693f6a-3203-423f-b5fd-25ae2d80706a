/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TPSubtitleCodecType.h
 * @brief    codec type枚举定义，枚举值与内核TPCodec.h文件中TPCodecID的枚举值保持一致
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/23
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 字幕Codec id定义，与内核层数值定义一致
/// Note:枚举的命名采用峰驼无法保留原有意义，+下划线.
typedef NS_ENUM(NSInteger, TPSubtitleCodecType) {
    TPSubtitleCodecTypeUnknown = -1,
#pragma mark - subtitle codecs
    TPSubtitleCodecTypeDVD_SUBTITLE = 6000,
    TPSubtitleCodecTypeDVB_SUBTITLE = 6001,
    /// raw UTF-8 text
    TPSubtitleCodecTypeTEXT = 6002,
    TPSubtitleCodecTypeXSUB = 6003,
    TPSubtitleCodecTypeSSA = 6004,
    TPSubtitleCodecTypeMOV_TEXT = 6005,
    TPSubtitleCodecTypeHDMV_PGS_SUBTITLE = 6006,
    TPSubtitleCodecTypeDVB_TELETEXT = 6007,
    TPSubtitleCodecTypeSRT = 6008,

    TPSubtitleCodecTypeMICRODVD = 6800,
    TPSubtitleCodecTypeEIA_608 = 6801,
    TPSubtitleCodecTypeJACOSUB = 6802,
    TPSubtitleCodecTypeSAMI = 6803,
    TPSubtitleCodecTypeREALTEXT = 6804,
    TPSubtitleCodecTypeSTL = 6805,
    TPSubtitleCodecTypeSUBVIEWER1 = 6806,
    TPSubtitleCodecTypeSUBVIEWER = 6807,
    TPSubtitleCodecTypeSUBRIP = 6808,
    TPSubtitleCodecTypeWEBVTT = 6809,
    TPSubtitleCodecTypeMPL2 = 6810,
    TPSubtitleCodecTypeVPLAYER = 6811,
    TPSubtitleCodecTypePJS = 6812,
    TPSubtitleCodecTypeASS = 6813,
    TPSubtitleCodecTypeHDMV_TEXT_SUBTITLE = 6814,
    TPSubtitleCodecTypeTTML = 6815,
};
