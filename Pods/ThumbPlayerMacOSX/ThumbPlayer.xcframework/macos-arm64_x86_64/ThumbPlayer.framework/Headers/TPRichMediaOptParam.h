/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPRichMediaOptParam.h
 * @brief    富媒体的可选参数定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#import "TPRichMediaDataCallbackType.h"

NS_ASSUME_NONNULL_BEGIN

/// 富媒体的可选参数定义. 用于ITPRichMediaSynchronizer的selectFeatureAsync相关方法
@interface TPRichMediaOptParam : NSObject

/// 富媒体同步器数据回调类型. 默认为TPRichMediaDataCallbackTypeDirect
@property (nonatomic, assign) TPRichMediaDataCallbackType dataCallbackType;
@end

NS_ASSUME_NONNULL_END
