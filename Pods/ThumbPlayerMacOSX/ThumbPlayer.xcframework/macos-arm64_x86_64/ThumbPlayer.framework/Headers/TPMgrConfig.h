/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file    TPMgrConfig.h
 * @brief   全局参数定义
 * 参数通过@see TPMgr#addOptionalParam:设置。
 * key的命名格式为TPMgrConfigKey+ 设置时机(BEFORE/GLOBAL) + 类型 + 名称。
 * 设置时机 BEFORE：表示这个参数必须在TPMgr init之前设置，否则无效，@see PMgr#initThumbPlayer
 * 设置时机 GLOBAL：表示这个参数在init之前和之后都可以设置，设置之后就立即生效
 *
 * 数据类型可以为int、long、float、string
 * 举例说明：TPMgrConfigKeyBeforeStringGUID，表示该参数必须在init之前设置，参数类型为String。
 *         TPMgrConfigKeyGlobalBoolEnablePlayingQualityReport，表示参数可以在任何时机设置，参数类型为bool
 *
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/7
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// TPOptionalId的类型别名
typedef NSString *TPMgrConfigKey NS_STRING_ENUM;

/// guid，用户唯一标识
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyBeforeStringGUID;

/// 平台号
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyBeforeIntPlatform;

/// 是否打开质量上报，默认NO
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnablePlayingQualityReport;

/// 设置播放器否打开新的质量上报, 禁止数据上报时设置为NO, 使能上报时为YES，默认为NO。新上报是新实现的一套数据上报
/// 与老的质量上报并存，但稳定性还有待验证，
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableNewPlayingQualityReport;

/// 设置播放器内部使用的hosts，用于业务方需要定制cgi url的host的情况，以达到隐藏域名的目的。
/// 该param的value以json格式传入，可以设置的host包括"player_host_config"和"httpproxy_config"。
/// "player_host_config"和"httpproxy_config"作为两个大的节点，其下面是一个一个具体的host节点。
/// json示例如下
/// {
///   "player_host_config": {
///     "beacon_policy_host": "new-othstr.play.aiseet.atianqi.co",
///     "beacon_log_host": "new-otheve.play.aiseet.atianqi.com"
///   },
///   "httpproxy_config": {
///     "time_cgi_host": "http://vv.play.aiseet.atianqi.com/checktime",
///     "vinfo_cgi_host": "http://vv.play.aiseet.atianqi.com/getvinfo"
///   }
/// }
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyBeforeStringHosts;

/// ffmpeg log与播放ID绑定使用了POSIX标准中的线程本地存储相关接口
/// 这里担心有平台兼容性问题，先增加配置开关
/// yes表示需要使能相关接口调用, no表示关闭, 默认yes
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableFFmpegLogPthreadLocalStorage;

/// 是否允许HDR开启向下兼容的模式
/// 建议不要在视频播放过程中多次修改此配置
/// YES表示开启向下兼容，NO表示关闭，默认为YES
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableHDRDownwardCompatibility;

/// 是否开启HDRVivid软件动态元数据映射
/// 建议不要在视频播放过程中多次修改此配置
/// YES表示开启HDRVivid软件动态元数据映射，NO表示关闭，默认为YES
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableHDRVividSoftDynamicMapping;

// 是否开启使用metal渲染HDR10的方式
// YES表示开启使用metal渲染HDR10的方式，NO表示关闭。 默认 NO
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableRenderHDR10WithMetal;

/// 是否开启TPCore API(部分)接口异步化
/// YES表示开启TPCore API接口异步化，NO表示关闭，默认为NO
/// 开启异步化后, API调用会立即返回，不会等待内核真正执行完
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableTPCoreAPIAsync;

/// 是否启用新的循环播放方案
/// YES表示开启，NO表示关闭
/// 默认为YES
/// TODO(hemanli, 2024.7.22): 等新方案功能稳定后删除
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableNewLoopback;

/// 是否开启在Mac平台上通过查询系统接口的方式获取HEVC的能力。
/// YES表示开启，NO表示关闭
/// 默认为YES
/// TODO(andygao, 2024.8.29): 待新的能力查询稳定后删除。
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableOsxHevcSupportByQuerySystem;

/// 是否开启视频元数据回调
/// YES表示开启，NO表示关闭
/// 默认为NO
/// TODO(ahailiu, 2024.9.2): 稳定性配置开关, 稳定后删除, 计划ThumbPlayer 3.03版本移除
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableVideoMetadataCallback;

/// 是否启用高版本的AVContentKeySession的fairplay实现方案
/// YES表示开启，NO表示关闭
/// 默认为NO
/// TODO(leeguo, 2024.11.8): 稳定性配置开关, 稳定后删除
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableFairplayWithContentKeySession;

/// 是否使能prepared状态的缓冲，
/// YES表示开启，prepared状态下发生了缓冲，暂时缓存住，等start之后再对外抛出
/// NO表示关闭，prepared状态下发生了缓冲，直接忽略，不对外抛出
/// 默认为NO
/// TODO(hemanli, 2024.12.10): 临时开关，过几个版本删除
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnablelBufferingWhenPrepared;

/// 是否启用demuxer读包报错进行avio_feof的判断，如果avio_feof返回true，认为eof，否则认为读包报错
/// YES表示开启，NO表示关闭
/// 默认为NO
/// TODO(leeguo, 2025.1.7): 稳定性配置开关, 稳定后删除, 解决手Q超短文件报错问题
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyBoolEnableDemuxerReadPacketErrorCheckAvioEof;

/// 是否启用新的hdr能力模型
/// YES表示开启，NO表示关闭
/// 默认NO
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyBoolEnableNewHdrCapability;

/// 是否启用数据错误回调
/// YES表示开启，NO表示关闭
/// 默认NO
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableInvalidDataCallback;

/// 是否启用metal 10位深渲染器，同时相应给metalLayer设置10位深的pixelFormat
/// YES表示开启，NO表示关闭
/// 默认NO
/// TODO(andygao, 2025/07/14): 临时开关，验证稳定后删除
FOUNDATION_EXPORT TPMgrConfigKey const TPMgrConfigKeyGlobalBoolEnableMetal10BitRenderer;

NS_ASSUME_NONNULL_END

