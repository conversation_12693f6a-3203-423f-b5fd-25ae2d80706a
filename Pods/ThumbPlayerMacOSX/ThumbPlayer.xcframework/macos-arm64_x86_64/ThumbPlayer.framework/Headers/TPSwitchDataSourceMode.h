/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPSwitchDataSourceMode.h
 * @brief    切换数据源模式枚举
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 切换数据源模式定义
typedef NS_ENUM(NSInteger, TPSwitchDataSourceMode) {
    /// 默认切换模式，由内部决策选择何种模式
    TPSwitchDataSourceModeDefault                      = 0,
    /// 数据源切换时，清空当前的buffer，立即切换到新的数据源，比如直播场景。该模式可能会发生缓冲和时间不对齐
    TPSwitchDataSourceModeImmediately                  = 1,
    /// 数据源切换时，在保证切换后不卡顿的情况下，快速切换到新的数据源。
    TPSwitchDataSourceModeFastWithKeepNoBuffering      = 2,
    /// 数据源切换时，必须耗费完已经缓存的所有数据后，才能切换到新的数据源。
    TPSwitchDataSourceModeAfterAllBufferedDataConsumed = 3,
    /// 切换数据源时，在下一个网络请求，按字节对齐方式从新的数据源读取，仅对点播有效。一般用于资源过期时给下载组件换播放地址，播放器内核无需处理
    TPSwitchDataSourceModeByteAlignWhenNextRequest     = 4,
};
