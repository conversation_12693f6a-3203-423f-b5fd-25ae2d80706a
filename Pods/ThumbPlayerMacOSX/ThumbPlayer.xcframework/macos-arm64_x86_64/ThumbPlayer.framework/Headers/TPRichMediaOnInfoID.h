/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPRichMediaDataCallbackType.h
 * @brief    富媒体同步器相关的onInfo回调消息类型定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>

/// 富媒体同步器相关的onInfo回调消息类型定义. 见ITPRichMediaSynchronizerDelegate的onRichMedia:infoID:infoParam方法
/// 枚举值必须跟内核相应的值保持一致(TPPlayerRichMediaInfoType枚举值)。
typedef NS_ENUM(NSInteger, TPRichMediaOnInfoID) {
    /// 未知。其他暂时无定义
    TPRichMediaOnInfoIDUnknown = 0
};
