/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPMgr.h
 * @brief    ThumbPlayer对外总的管理类
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#pragma once
#import <Foundation/Foundation.h>
#import "ITPLogDelegate.h"
#import "ITPBeaconDataReporter.h"
#import "TPOptionalParam.h"

#define ENABLE_THUMBPALYER_NEXT [[TPMgr sharedMgr] isThumbPlayerNextEnabled]

NS_ASSUME_NONNULL_BEGIN

/// 播放器模块名称
FOUNDATION_EXPORT NSString *const TPModuleNamePlayerCore;

/// 下载组件模块名称
FOUNDATION_EXPORT NSString *const TPModuleNameDownloadProxy;

/// ThumbPlayer对外总的管理类
@interface TPMgr : NSObject

/// 单例获取
+ (instancetype)sharedMgr;

/// 禁用alloc方法, 请使用sharedMgr获取
+ (instancetype)alloc UNAVAILABLE_ATTRIBUTE;

/// 禁用new方法, 请使用sharedMgr获取
+ (instancetype)new __attribute__((unavailable("replace with 'sharedMgr'")));

/// 禁用copy方法, 请使用sharedMgr获取
- (instancetype)copy __attribute__((unavailable("replace with 'sharedMgr'")));

/// 禁用mutableCopy方法, 请使用sharedMgr获取
- (instancetype)mutableCopy __attribute__((unavailable("replace with 'sharedMgr'")));

/// 设置是否开启ThumbPlayerNext，需要在其它所有接口调用前设置，且全局只有第一次设置生效
/// @param enable 是否开启
- (void)setEnableThumbPlayerNextBeforeOtherAPI:(BOOL)enable;

/// 是否开启ThumbPlayerNext，需要在initThumbPlayer前设置
- (BOOL)isThumbPlayerNextEnabled;

/// 初始化ThumbPlayer
/// @see TPContext 初始化参数
- (void)initThumbPlayer;

/// ThumbPlayer是否初始化完成
/// @return YES 初始化完成，NO 未初始化
- (BOOL)isInitialized;

/// 设置日志回调
/// @see ITPLogDelegate
/// @param delegate 日志回调
- (void)setLogDelegate:(id<ITPLogDelegate>)delegate;

/// 设置日志打印等级
/// @see TPLogLevel
/// @param logLevel 日志等级,等级大于等于logLevel的日志才会输出
- (void)setLogLevel:(TPLogLevel)logLevel;

/// 设置业务方实现的自定义灯塔上报接口. 注意: 调initThumbPlayer前设置, 否则无效
/// 设置后，播放器将不再初始化灯塔
/// 所有的上报数据将会通过ITPBeaconDataReporter#trackCustomKVEvent:appkey:eventId:dataMap回调出来
/// @param beaconDataReporter 不为空时走业务方设置的灯塔接口上报, 否则默认走内部灯塔上报
- (void)setBeaconDataReporterBeforeInit:(id<ITPBeaconDataReporter>)beaconDataReporter;

/// 获取播放器版本号，注意这里返回的是播放器的大版本号，不是TPCore的版本号，TPCore的版本号请调用 #getLibVersion
/// @return 播放器版本号
- (NSString *)thumbPlayerVersion;

/// 通过模块名获取模块版本号. 调用initThumbPlayer:之后才能获取
/// @param libName 参考KTPModuleNamePlayerCore和KTPModuleNameDownloadProxy
/// @return 对应模块的版本号, 如果未初始化或者未找到对应模块返回nil
- (nullable NSString *)libVersionWithLibName:(NSString *)libName;

/// 获取ThumbPlayer全局通用参数
/// @param optionalParam 通用参数
- (void)addOptionalParam:(TPOptionalParam *)optionalParam;

@end

NS_ASSUME_NONNULL_END
