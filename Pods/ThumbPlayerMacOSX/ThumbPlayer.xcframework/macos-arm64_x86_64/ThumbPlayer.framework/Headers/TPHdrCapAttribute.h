/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TPHdrCapAttribute.h
 * @brief    Hdr能力属性
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/5/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPHdrType.h"
#import "TPHdrMappingType.h"

NS_ASSUME_NONNULL_BEGIN
/// HDR能力属性，用于说明设备支持的对应HDR能力的相关特性
@interface TPHdrCapAttribute : NSObject
/// HDR能力类型
@property (nonatomic, assign, readonly) TPHdrType hdrType;
/// 设备支持HDR能力的映射方式集合，集合为空表示不支持（映射方式详见TPHdrMappingType）
@property (nonatomic, strong, readonly) NSSet<NSNumber *> *supportedMappingTypes;

@end

NS_ASSUME_NONNULL_END
