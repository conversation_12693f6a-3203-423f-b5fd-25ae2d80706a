/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPSeekMode.h
 * @brief    seek模式定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// seek模式，用于seekTo:mode:方法中，更细粒度的控制seek方式
typedef NS_ENUM(NSInteger, TPSeekMode) {
    /// 默认模式，由内部决策走何种模式
    TPSeekModeDefault          = 0,
    /// 普通的seek方式，按照时间点找上一个Key Frame
    TPSeekModePreviousKeyFrame = 1,
    /// 寻找当前seek时间点的下一个Key Frame
    TPSeekModeNextKeyFrame     = 2,
    /// 表示精确seek，必须寻找到当前时间点，相对比较耗时
    TPSeekModeAccuratePosition = 3,
    /// 对于分片播放的场景，直接跳过当前片，seek到下一个分片开始播放. 仅multiasse有效
    TPSeekModeNextClip         = 4,
};
