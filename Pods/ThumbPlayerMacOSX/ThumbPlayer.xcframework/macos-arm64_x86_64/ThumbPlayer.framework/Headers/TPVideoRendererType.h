/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPVideoRendererType.h
 * @brief    视频渲染器类型定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 视频 Renderer类型定义
typedef NS_ENUM(NSInteger, TPVideoRendererType) {
    /// None，使用该配置值可以关闭渲染
    TPVideoRendererTypeNone         = -1,
    /// 使用OpenGL渲染视频
    TPVideoRendererTypeOpenGL       = 101,
    /// 使用Metal渲染视频
    TPVideoRendererTypeMetal        = 102,
    /// 使用DisplayLayer渲染视频
    TPVideoRendererTypeDisplayLayer = 103,
    /// 使用MetalLayer渲染视频
    TPVideoRendererTypeMetalLayer   = 104,
    /// 系统播放器的渲染器
    TPVideoRendererTypeAVPlayerLayer = 110,
};
