/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPHdrCapability.h
 * @brief    获取Thumbplayer的hdr能力
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/20
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#pragma once

#import <Foundation/Foundation.h>
#import "TPHdrType.h"
#import "TPPlayerCoreType.h"
#import "TPHdrCapAttribute.h"
#import "TPHdrCapabilityQueryParams.h"
#import "TPHdrRuntimeCondition.h"

/// hdr能力支持情况定义
/// 注意：这些值的定义值不要改动，此值和内核层的值是一一对应关系
typedef NS_ENUM(NSInteger, TPHdrCap) {
    /// 不支持hdr
    TPHdrCapNotSupport = 0,
    /// 支持hdr
    TPHdrCapSupport = 1,
};

NS_ASSUME_NONNULL_BEGIN
/// hdr能力
@interface TPHdrCapability : NSObject

/// 查询特定类型的HDR是否支持，只要自研播放器和系统播放器有一个能支持，就返回支持
/// @param hdrType 对应于TPHdrType里面对应的HDR类型，包括HDR10,HDR10+,HLG,DOLBY VISION等。
/// @return 对应的hdr能力
+ (TPHdrCap)hdrCapability:(TPHdrType)hdrType;

/// 查询特定类型的HDR是否能够特定类型的播放器类型支持
/// @param hdrType 对应于TPHdrType里面对应的HDR类型，包括HDR10,HDR10+,HLG,DOLBY VISION等。
/// @param playerType 具体指定的播放器类型，如果不关心播放器类型，则使用上面的接口
/// @return 对应的hdr能力
+ (TPHdrCap)hdrCapability:(TPHdrType)hdrType
               playerType:(TPPlayerCoreType)playerType;

/// 查询特定类型的HDR是否能够支持,包含自研播放器和系统播放器
/// @param queryParams hdr查询能力相关的参数
/// @return 对应的hdr能力
+ (TPHdrCap)hdrCapabilityWithQueryParams:(TPHdrCapabilityQueryParams *)queryParams;

/// 查询特定类型的HDR是否能够特定类型的播放器类型支持
/// @param playerType 具体指定的播放器类型，如果不关心播放器类型，则使用上面的接口
/// @param queryParams hdr查询能力相关的参数
/// @return 对应的hdr能力
+ (TPHdrCap)hdrCapabilityWithPlayerType:(TPPlayerCoreType)playerType
                            queryParams:(TPHdrCapabilityQueryParams *)queryParams;

/// 查询指定的HDR能力属性，包含自研播放器和系统播放器
/// @param hdrType 对应于TPHdrType里面对应的HDR类型，包括HDR10,HDR10+,HLG,DOLBY VISION等。
/// @return 对应的hdr类型能力属性
+ (nullable TPHdrCapAttribute *)hdrCapAttribute:(TPHdrType)hdrType 
DEPRECATED_MSG_ATTRIBUTE("Will be deprecated, please use @selector(hdrRuntimeConditionWithQueryParams:) instead");

/// 查询指定播放器类型的HDR能力属性
/// @param hdrType 对应于TPHdrType里面对应的HDR类型，包括HDR10,HDR10+,HLG,DOLBY VISION等。
/// @param playerType 具体指定的播放器类型，如果不关心播放器类型，则使用上面的接口
/// @return 对应的hdr类型能力属性
+ (nullable TPHdrCapAttribute *)hdrCapAttribute:(TPHdrType)hdrType playerType:(TPPlayerCoreType)playerType
DEPRECATED_MSG_ATTRIBUTE("Will be deprecated, please use @selector(hdrRuntimeConditionWithQueryParams:) instead");

/// 查询指定的HDR能力属性，包含自研播放器和系统播放器
/// @param queryParams hdr查询能力相关的参数
/// @return 对应的hdr类型能力属性
+ (nullable TPHdrCapAttribute *)hdrCapAttributeWithQueryParams:(TPHdrCapabilityQueryParams *)queryParams 
DEPRECATED_MSG_ATTRIBUTE("Will be deprecated, please use @selector(hdrRuntimeConditionWithQueryParams:) instead");

/// 查询指定播放器类型的HDR能力属性
/// @param playerType 具体指定的播放器类型，如果不关心播放器类型，则使用上面的接口
/// @param queryParams hdr查询能力相关的参数
/// @return 对应的hdr类型能力属性
+ (nullable TPHdrCapAttribute *)hdrCapAttributeWithPlayerType:(TPPlayerCoreType)playerType 
                                                  queryParams:(TPHdrCapabilityQueryParams *)queryParams
DEPRECATED_MSG_ATTRIBUTE("Will be deprecated, please use @selector(hdrRuntimeConditionWithQueryParams:) instead");

/// 查询所有支持的hdr运行时条件集
/// @return HDR能力之运行时条件信息集合，如果无任何支持，将会返回空集合
+ (NSArray<TPHdrRuntimeCondition *> *)hdrRuntimeConditions;

/// 查询指定的HDR能力之运行时条件信息，包含自研播放器和系统播放器
/// @param queryParams hdr查询能力相关的参数
/// @return HDR能力之运行时条件信息，如果无任何支持，将会返回空集合
+ (NSArray<TPHdrRuntimeCondition *> *)hdrRuntimeConditionsWithQueryParams:(TPHdrCapabilityQueryParams *)queryParams;

@end

NS_ASSUME_NONNULL_END
