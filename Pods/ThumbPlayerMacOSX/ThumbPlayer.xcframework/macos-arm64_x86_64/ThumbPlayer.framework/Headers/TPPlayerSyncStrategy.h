/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPPlayerSyncStrategy.h
 * @brief    同步策略
 * <AUTHOR>
 * @version  1.0.0
 * @date     2025/3/11
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>

// 定义枚举类型
typedef NS_ENUM(NSInteger, TPPlayerSyncActionType) {
    TPPlayerSyncActionTypeFloatSpeedRate = 0,     // 调整播放速率
    TPPlayerSyncActionTypeVoidSeek = 1,           // 执行seek
};

/// 同步动作
@interface TPPlayerSyncAction : NSObject
/// 动作类型
@property (nonatomic, assign, readonly) TPPlayerSyncActionType type;
/// 动作的参数
@property (nonatomic, strong, readonly) id param;
/// init方法
- (instancetype)initWithActionType:(TPPlayerSyncActionType)action param:(id)param;
/// init方法
- (instancetype)initWithActionType:(TPPlayerSyncActionType)action;
@end

@interface TPPlayerSyncStrategy : NSObject

/// @brief 获取策略表，可重写该方法修改策略表
/// @return 策略表， {position_gap_ms, TPPlayerSyncAction}
///
///   默认策略表：
///     @(-200LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate param:@(1.05F)],  落后>200ms => 播放速率*1.05
///     @(-600LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate param:@(1.05F)],  落后>600ms => 播放速率*1.1
///     @(-1000LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate param:@(1.2F)],  落后>1s => 播放速率*1.2
///     @(-2000LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate param:@(1.4F)],  落后>2s => 播放速率*1.4
///     @(-4000LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate param:@(1.6F)],  落后>4s => 播放速率*1.6
///     @(-8000LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate],               落后>8s => seek
///
///     @(200LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate param:@(0.95F)],   超前>200ms => 播放速率*0.95
///     @(600LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate param:@(0.9F)],    超前>600ms => 播放速率*0.9
///     @(1000LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate param:@(0.8F)],   超前>1s => 播放速率*0.8
///     @(2000LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate param:@(0.7F)],   超前>2s => 播放速率*0.7
///     @(4000LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate param:@(0.6F)],   超前>4s => 播放速率*0.6
///     @(8000LL): [[TPPlayerSyncAction alloc] initWithActionType:TPPlayerSyncActionFloatSpeedRate]],               超前>8s => seek
- (NSDictionary<NSNumber *, TPPlayerSyncAction *> *)strategyMap;

@end
