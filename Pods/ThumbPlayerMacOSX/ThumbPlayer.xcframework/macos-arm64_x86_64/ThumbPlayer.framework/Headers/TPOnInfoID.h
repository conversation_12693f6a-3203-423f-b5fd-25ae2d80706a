/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPOnInfoID.h
 * @brief    播放器onInfo回调的消息定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/2
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 播放器onInfo回调的消息定义
/// 必须写明触发OnInfo回调的时机
/// 必须写明OnInfo回调数据TPOnInfoParam中的参数、含义及类型，若没有回调数据则写"无"
/// 重构前后变更：https://iwiki.woa.com/pages/viewpage.action?pageId=**********
///
/// 命名格式：
/// TPOnInfoID + 消息参数类型 + 名称
/// 其中消息参数类型有：
/// 1.Void    表示不携带参数
/// 2.Long1   表示使用long型参数1            Long2   表示使用long型参数1和2
/// 3.Float1  表示使用float型参数1           Float2  表示使用float型参数1和2
/// 4.String1 表示使用string型参数1          String2 表示使用string型参数1和2
/// 5.Obj     表示使用Object型扩展参数，Obj类型参数可与2/3/4搭配使用
/// 例如：
/// TPOnInfoIDVoidBufferingStart 表示缓冲开始，不携带消息参数
/// TPOnInfoIdLong1ObjSelectTrackError 表示选轨失败，使用long型参数1和Object型扩展参数携带消息参数
///
///
/// 值定义区间：
/// 1、0～49999        内核层定义的OnInfo类型，与内核定义值一一对应
/// 2、50000～79999    平台层特定的OnInfo类型
/// 3、80000～99999    下载组件的OnInfo类型

typedef NS_ENUM(NSInteger, TPOnInfoID) {
#pragma mark - 内核层上抛
    /// 未知
    TPOnInfoIDVoidUnknown = -1,
    /// 切换数据源成功完成
    /// 切换数据源错误被认为是fatal类型，若出错，则通过onerror通知
    /// 参数：
    /// Long1   函数调用传入的opaque标识
    TPOnInfoIDLong1SwitchDataSourceSuccess = 3,
    /// 选择轨道执行成功
    /// 参数：
    /// Long1   函数调用传入的opaque标识
    TPOnInfoIDLong1SelectTrackSuccess = 10,
    /// 选择轨道执行失败
    /// 选择媒体轨道错误被认为是非fatal类型，所有通过该OnInfo消息通知
    /// 参数：
    /// Long1   函数调用传入的opaque标识
    /// Object  TPError类型，error信息
    TPOnInfoIDLong1ObjSelectTrackError = 11,
    /// 取消选择轨道执行成功
    /// 参数：
    /// Long1   函数调用传入的opaque标识
    TPOnInfoIDLong1DeselectTrackSuccess = 12,
    /// 取消选择轨道执行失败
    /// 取消选择媒体轨道错误被认为是非fatal类型，所有通过该OnInfo消息通知
    /// 参数：
    /// Long1   函数调用传入的opaque标识
    /// Object  TPError类型，error信息
    TPOnInfoIDLong1ObjDeselectTrackError = 13,
    /// 选择节目selectProgramAsync执行成功
    /// 参数：
    /// Long1   函数调用传入的opaque标识
    TPOnInfoIDLong1SelectProgramSuccess = 14,
    /// 选择节目selectProgramAsync执行失败
    /// 选择节目错误被认为是非fatal类型，所有通过该OnInfo消息通知
    /// 参数：
    /// Long1   函数调用传入的opaque标识
    /// Object  TPError类型，error信息
    TPOnInfoIDLong1ObjSelectProgramError = 15,
    /// 首个clip打开
    /// 参数无
    TPOnInfoIDVoidFirstClipOpened = 101,
    /// demuxer首次读到携带关键帧的数据包
    /// 参数无
    TPOnInfoIDVoidVideoFirstKeyPacketRead = 102,
    /// 音频解码器首次打开
    /// 参数无
    TPOnInfoIDVoidFirstAudioDecoderStart = 103,
    /// 视频解码器首次打开
    /// 参数无
    TPOnInfoIDVoidFirstVideoDecoderStart = 104,
    /// 音频帧首次渲染
    /// 参数无
    TPOnInfoIDVoidFirstAudioFrameRendered = 105,
    /// 视频帧首次渲染
    /// 参数无
    TPOnInfoIDVoidFirstVideoFrameRendered = 106,
    /// demuxer首次读到数据包
    /// 参数无
    TPOnInfoIDVoidFirstPacketRead = 107,
    /// 循环播放时，新的循环开始
    /// 参数无
    TPOnInfoIDVoidCurrentLoopStart = 150,
    /// 循环播放时，当前循环结束
    /// 参数无
    TPOnInfoIDVoidCurrentLoopEnd = 151,
    /// 开始一个新的clip播放
    /// 参数：
    /// Long1   clip序号
    TPOnInfoIDLong1ClipStart = 152,
    /// 当前clip播放结束
    /// 参数：
    /// Long1   clip序号
    TPOnInfoIDLong1ClipEnd = 153,
    /// 缓冲开始
    /// 参数无
    TPOnInfoIDVoidBufferingStart = 200,
    /// 缓冲结束
    /// 参数无
    TPOnInfoIDVoidBufferingEnd = 201,
    /// 音频解码器类型发生变化
    /// 第一次创建解码器时触发
    /// 播放过程中切换了解码器类型时触发
    /// 参数：
    /// Long1    变化后的解码器类型，TPDecoderType
    TPOnInfoIDLong1AudioDecoderTypeChanged = 203,
    /// 视频解码器类型发生变化
    /// 第一次创建解码器时触发
    /// 播放过程中切换了解码器类型时触发
    /// 参数：
    /// Long1    变化后的解码器类型，TPDecoderType
    TPOnInfoIDLong1VideoDecoderTypeChanged = 204,
    /// 自适应切换清晰度开始
    /// 播放器内部每5s根据当前网络情况做一次码率检查，若发现更匹配的program码率，则进行清晰度切换并触发回调
    /// 参数：
    /// Long1   目标清晰度的programId
    TPOnInfoIDLong1AdaptiveSwitchDefStart = 251,
    /// 自适应切换清晰度结束
    /// 渲染器首次收到新清晰度program的帧时触发回调
    /// 参数：
    /// Long1   目标清晰度的programId
    TPOnInfoIDLong1AdaptiveSwitchDefEnd = 252,
    /// 字幕相关错误
    /// 由于字幕错误不被认为fatal，所以通过oninfo回调出去
    /// 参数：
    /// Long1   对应轨道id
    /// Object  TPError类型，error信息
    TPOnInfoIDLong1ObjSubtitleError = 254,
    /// 视频丢帧率过高
    /// 由于音画同步问题，播放器可能会丢弃部分帧，不渲染
    /// 播放器每秒统计当前这一秒的丢帧率：渲染帧数/（渲染帧数+丢帧数）
    /// 若高于TPOptionalID中设置的最高阈值，则触发回调
    /// 参数：
    /// Float1  丢帧率
    TPOnInfoIDFloat1VideoHighFrameDropRate = 255,
    /// 视频帧率过低
    /// 播放器每秒统计当前这一秒的帧率：渲染帧数/时长，
    /// 若低于TPOptionalID中设置的最低阈值，则触发回调
    /// 参数
    /// Float1  帧率
    TPOnInfoIDFloat1VideoLowFrameRate = 256,
    /// MediaLabVR 清晰度切换延时数据
    /// 每次切换视角，会有该数据回调
    /// 参数
    /// Long1  延时数据ms
    TPOnInfoIDLong1MediaLabVRLatencyMs = 257,
    /// 视频Hdr信息发生改变
    /// 播放器渲染视频帧时，视频帧的hdr类型或hdr映射类型发生改变，通过此信息回调视频帧的hdr类型和hdr映射类型
    /// 参数
    /// Long1  hdr类型，详见TPHdrType
    /// Long2  hdr映射类型，详见TPHdrMappingType
    TPOnInfoIDLong2VideoHdrInfoChanged = 258,
    /// 音频渲染错误
    /// 由于音频渲染错误不被认为fatal，所以通过oninfo回调出去
    /// 参数
    /// Object  TPError类型，error信息
    TPOnInfoIDObjAudioRenderError = 259,
    /// 视频渲染间隔过大
    /// 若高于TPOptionalID中设置的阈值，则触发回调
    /// 参数
    /// Long1  间隔时间
    TPOnInfoIDLong1VideoLargeRenderIntervalMs = 260,
    /// 数据错误。demuxer或decoder模块检测到数据错误时，则触发回调
    /// 参数
    /// Object  TPError类型，error信息
    TPOnInfoIDObjInvalidData = 262,
    /// 视频的Crop信息发生了变化
    /// 需要对surface输出的视频内容进行进一步处理的应用场景需要考虑Crop信息。
    /// 参数：
    /// Object  TPVideoCropInfo类型，变化后的crop数据信息
    TPOnInfoIDObjVideoCropChanged = 500,
    /// 读取到指定TPOptionalID的HLS私有标签
    /// 参数：
    /// String1 读取到的HLS私有标签内容
    TPOnInfoIDStringPrivateHlsTag = 501,
    /// 读取到指定TPOptionalID的sei类型数据
    /// 参数：
    /// Object  TPVideoSeiInfo类型，读取到的sei数据
    TPOnInfoIDObjVideoSei = 503,
    /// 读取到WebVTT的OTE内容
    /// 参数：
    /// Long1   对应轨道id, 在getTrackInfo中的index
    /// String1 WebVTT的NOTE内容
    TPOnInfoIDLong1String1SubtitleNote = 506,

    /// 更新非空渲染目标(即playerView(UIView/NSNiew))后的首帧视频渲染
    /// 参数：无
    TPOnInfoIDVoidFirstFrameRenderedAfterUpdateRenderTarget = 602,

    /// 视频元数据变更
    /// 参数:
    /// objParam  NSDictionary<NSString *, NSString *> (集合中可能仅包含其中一部份key)
    /// key参考TPPropertyID.h下的定义
    /// TPPropertyIDLongVideoWidth
    /// TPPropertyIDLongVideoHeight
    /// TPPropertyIDFloatVideoFrameRate
    /// TPPropertyIDLongVideoFieldOrder
    /// TPPropertyIDLongVideoColorTransferCharacteristic
    /// TPPropertyIDLongVideoPixelFormat
    /// TPPropertyIDLongVideoBitDepth
    /// TPPropertyIDLongVideoColorSpace
    TPOnInfoIDObjVideoMetadataChanged = 603,

    /// 播放进度到达某个时间点，时间点由TPOptionalIDGlobalQueueLongTimePointMsCallback指定
    /// 参数：
    /// Long1 到达的时间点，单位ms
    TPOnInfoIDLong1TimePointMsReached = 604,

    /// seek之后的首帧视频，如果没有视频流，则不会回调该OnInfo
    /// 参数：
    /// Long1 首帧视频时间戳，单位ms
    /// Long2 seek时携带的opaque
    TPOnInfoIDLong2FirstVideoFrameRenderedAfterSeek = 605,

    /// 数据下载暂停通知，当数据下载由运行态进入暂停态的时候，抛出该通知
    /// 有两种情况会触发该通知：
    /// 1.内部暂停了数据下载
    /// 2.外面调用PauseDownload
    /// 如果已经在暂停态则不触发
    /// 参数：
    /// 无
    TPOnInfoIDVoidDataDownloadPaused = 606,

    /// 数据下载继续通知，当数据下载由暂停态进入运行态的时候，抛出该通知
    /// 有两种情况会触发该通知：
    /// 1.内部暂停了数据下载后继续
    /// 2.外面调用ResumeDownload
    /// 如果已经在运行态则不触发
    /// 参数：
    /// 无
    kTPOnInfoIDVoidDataDownloadResumed = 607,

#pragma mark - 平台层上抛
    /// 播放器重启开始
    /// 播放器重启过程可能较为耗时，可配合使用给予用户提示
    /// 参数：
    /// LONG1   重启前的播放器类型
    /// LONG2   重启后的播放器类型
    /// OBJECT  nullable，若重启由error触发，则为TPError类型
    TPOnInfoIDLong2ObjPlayerRebootStart = 50000,
    /// 播放器重启完成
    /// 播放器重启过程可能较为耗时，可配合使用给予用户提示
    /// 参数无
    TPOnInfoIDVoidPlayerRebootEnd = 50001,
    /// 播放器类型发生变化
    /// 启播成功后触发
    /// 播放器内部重启/切换成功后触发
    /// 参数
    /// Long1   播放器类型，TPPlayerCoreType
    TPOnInfoIDLong1PlayerTypeChanged = 50002,
    /// 播放器对下载组件的使用发生变化
    /// 执行启播时触发
    /// 播放过程中下载组件出现故障，播放器重试不走下载组件而直连cdn地址时触发
    /// 参数：
    /// Long1   是否使用，0不使用，1使用
    TPOnInfoIDLong1IsUseDownloadProxyChanged = 50003,
    /// 系统播放器消息，通知上层系统播放器已可使用。在3D渲染等特殊场景，外部需要获取到系统播放器实例来实现功能。但系统播放器创建时机以及是否确定可用等信息外部无法感知。
    /// 因此通过该消息通知依赖方系统播放器已可使用，并通过参数带出有效的系统播放器实例
    /// 参数
    /// Object  可被使用的AVPlayer实例
    TPOnInfoIDObjAVPlayerAvailable = 50004,
    
    /// 系统播放器消息，通知上层系统播放器将失效。在3D渲染等特殊场景，外部需要获取到系统播放器实例来实现功能，当播放器即失效时，需要通知使用方，以免继续操作一个失效的播放实例。
    /// 因此通过该消息通知依赖方系统播放器失效，并通过参数带出失效的系统播放器实例
    /// 参数
    /// Object  失效的AVPlayer实例
    TPOnInfoIDObjAVPlayerUnavailable = 50005,
    
    /// 系统播放器因为外部保护不足而黑屏时和解除黑屏时回调
    /// Long1   是否黑屏，1 黑屏  0 解除黑屏
    TPOnInfoIDLong1AVPlayerOutputObscuredDueToInsufficientExternalProtectionChanged = 50006,
    
#pragma mark - 传输组件上抛
    /// 播放的视频数据全部下载完毕
    /// 参数无
    TPOnInfoIDVoidDownloadAllFinish = 80000,
    /// 传输组件的下载进度发生变化
    /// 每秒都会触发
    /// 参数：
    /// Object  TPDownloadProgressInfo类型，变化后的下载进度
    TPOnInfoIDObjDownloadProgressChanged = 80001,
    /// 下载组件建议的码率
    /// 需开启 TPOptionalID中建议码率
    /// 当下载组件认为可以切换到更合适的码率的源时触发
    /// 参数：
    /// Long1   建议的码率，单位bps
    TPOnInfoIDLong1DownloadNetworkSuggestBitrate = 80002,
    /// 传输组件发生了错误，业务方收到该error不必报错，可等待播放器的报错
    /// 参数
    /// Object TPError类型，error信息
    TPOnInfoIDObjDataTransportError = 80003,
    /// 数据传输消息，该消息用来透传除了上述传输组件消息之外的其他消息
    /// 参数
    /// Long1 下载组件消息类型
    /// Object 透传下载组件的消息参数对象
    TPOnInfoIDLong1ObjDataTransportInfo = 80100,
};
