/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TPHdrRuntimeCondition.h
 * @brief    hdr运行时条件，用于描述何种条件组合下，支持何种hdr类型
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/12/28
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPHdrType.h"
#import "TPVideoRendererType.h"
#import "TPHdrMappingType.h"
#import "TPPlayerCoreType.h"

NS_ASSUME_NONNULL_BEGIN

/// hdr运行时条件，用于描述何种条件组合下，支持何种hdr类型
@interface TPHdrRuntimeCondition : NSObject
/// hdr类型
@property (nonatomic, assign, readonly) TPHdrType hdrType;
/// 视频渲染器类型
@property (nonatomic, assign, readonly) TPVideoRendererType videoRendererType;
/// 映射类型
@property (nonatomic, assign, readonly) TPHdrMappingType mappingType;
/// 播放内核类型
@property (nonatomic, assign, readonly) TPPlayerCoreType playerCoreType;

/// 禁默认构造
- (instancetype)init NS_UNAVAILABLE;

/// 构造方法
/// @param hdrType hdr类型
/// @param playerCoreType 播放内核类型
/// @param videoRendererType 视频渲染器类型
/// @param mappingType 映射类型
- (instancetype)initWithHdrType:(TPHdrType)hdrType
                 playerCoreType:(TPPlayerCoreType)playerCoreType
              videoRendererType:(TPVideoRendererType)videoRendererType
                    mappingType:(TPHdrMappingType)mappingType;

@end

NS_ASSUME_NONNULL_END
