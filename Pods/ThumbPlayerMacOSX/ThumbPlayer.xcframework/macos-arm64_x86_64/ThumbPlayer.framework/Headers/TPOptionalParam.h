/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPOptionalParam.h
 * @brief    ThumbPlayer可选控制参数
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/03/06
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPOptionalID.h"

NS_ASSUME_NONNULL_BEGIN

/// TODO(leeguo, jahnlang): 考虑NSNumber的问题，重新设计
@interface TPOptionalParam<__covariant T> : NSObject

/// 构造方法禁用，请通过build方法构造
- (nonnull instancetype)init NS_UNAVAILABLE;

///构造方法禁用，请通过build方法构造
+ (nonnull instancetype)new NS_UNAVAILABLE;

/// 创建BOOL类型的可选参数
/// @param value 参数的value，限定Bool类型
/// @param key 参数key值，不可为空
/// @return BOOL类型的可选参数实例
+ (TPOptionalParam<NSNumber *> *)buildBooleanParam:(BOOL)value key:(nonnull NSString *)key;

/// 创建int类型的可选参数
/// @param value 参数的value，限定int类型
/// @param key 参数key值，不可为空
/// @return int类型的可选参数实例
+ (TPOptionalParam<NSNumber *> *)buildIntParam:(int)value key:(nonnull NSString *)key;

/// 创建long类型的可选参数
/// @param value 参数的value，限定long类型
/// @param key 参数key值，不可为空
/// @return long类型的可选参数实例
+ (TPOptionalParam<NSNumber *> *)buildLongParam:(int64_t)value key:(nonnull NSString *)key;

/// 创建float类型的可选参数
/// @param value 参数的value，限定int类型
/// @param key 参数key值，不可为空
/// @return int类型的可选参数实例
+ (TPOptionalParam<NSNumber *> *)buildFloatParam:(float)value key:(nonnull NSString *)key;

/// 创建string类型的可选参数
/// @param value 参数的value，限定string类型
/// @param key 参数key值，不可为空
/// @return string类型的可选参数实例
+ (TPOptionalParam<NSString *> *)buildStringParam:(nonnull NSString *)value key:(nonnull NSString *)key;

/// 创建int数组类型的可选参数
/// @param value 参数的value，限定int数组类型
/// @param key 参数key值，不可为空
/// @return int数组类型的可选参数实例
+ (TPOptionalParam<NSArray<NSNumber *> *> *)buildQueueIntParam:(nonnull NSArray<NSNumber *> *)value key:(nonnull NSString *)key;

/// 创建long类型数组类型的可选参数
/// @param value 参数的value，限定long数组类型
/// @param key 参数key值，不可为空
/// @return long数组类型的可选参数实例
+ (TPOptionalParam<NSArray<NSNumber *> *> *)buildQueueLongParam:(nonnull NSArray<NSNumber *> *)value key:(nonnull NSString *)key;

/// 创建string数组类型的可选参数
/// @param value 参数的value，限定string数组类型
/// @param key 参数key值，不可为空
/// @return string数组类型的可选参数实例
+ (TPOptionalParam<NSArray<NSString *> *> *)buildQueueStringParam:(nonnull NSArray<NSString *> *)value key:(nonnull NSString *)key;

/// 创建object类型的可选参数
/// @param value 参数的value
/// @param key 参数key值，不可为空
/// @return object类型的可选参数实例
+ (TPOptionalParam<id> *)buildObjectParam:(nonnull id)value key:(nonnull NSString *)key;

/// 获取当前参数的key
/// @return 当前参数的key
- (NSString *)key;

/// 获取当前参数的value
/// @return 当前参数的value
- (T)value;

/// 是否是全局OptionalParam
/// @return 是否为全局可设置的OptionalParam
- (BOOL)isGlobalOptionalParam;

@end

NS_ASSUME_NONNULL_END
