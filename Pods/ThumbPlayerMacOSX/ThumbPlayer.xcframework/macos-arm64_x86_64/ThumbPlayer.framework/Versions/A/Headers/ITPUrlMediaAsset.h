/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPUrlMediaAsset.h
 * @brief    url media asset，用于只有一个播放url的情况，比如hls、单片mp4、字幕轨、音轨等。
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/21
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITPMediaAsset.h"

NS_ASSUME_NONNULL_BEGIN

@protocol ITPUrlMediaAsset <ITPMediaAsset>

/// 设置播放地址
/// @param url 播放地址
- (void)setUrl:(NSString *)url;

/// 设置播放地址及其对应http header
/// @param url 播放地址
/// @param httpHeader url的http header
- (void)setUrl:(NSString *)url httpHeader:(NSDictionary<NSString *, NSString *> *)httpHeader;

/// 设置播放地址及其对应http header和URL相关信息
/// @param url 播放地址
/// @param httpHeader url的http header
/// @param extraParams url的额外参数
- (void)setUrl:(NSString *)url httpHeader:(NSDictionary<NSString *, NSString *> *)httpHeader extraParams:(nullable NSDictionary<NSString *, NSString *> *)extraParams;

/// 设置播放地址的http header
/// @param httpHeader url的http header
- (void)setHttpHeader:(NSDictionary<NSString *, NSString *> *)httpHeader;

/// 设置播放地址的http header
/// @param extraParams url的额外参数
- (void)setExtraParams:(NSDictionary<NSString *, NSString *> *)extraParams;

/// 添加备份地址，备份地址可以添加多个
/// @param backUrl 备份地址
- (void)addBackUrl:(NSString *)backUrl;

/// 添加备份地址和其对应的http header
/// @param backUrl 备份地址
/// @param httpHeader 备份地址的http header
- (void)addBackUrl:(NSString *)backUrl httpHeader:(nullable NSDictionary<NSString *, NSString *> *)httpHeader;

/// 添加备份地址和其对应的http header
/// @param backUrl 备份地址
/// @param httpHeader 备份地址的http header
/// @param extraParams url的额外参数
- (void)addBackUrl:(NSString *)backUrl httpHeader:(nullable NSDictionary<NSString *, NSString *> *)httpHeader extraParams:(nullable NSDictionary<NSString *, NSString *> *)extraParams;

@end

NS_ASSUME_NONNULL_END
