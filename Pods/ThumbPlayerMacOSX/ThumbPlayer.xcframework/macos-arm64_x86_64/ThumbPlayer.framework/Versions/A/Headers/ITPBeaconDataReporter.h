/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     ITPBeaconDataReporter.h
 * @brief    灯塔上报注入协议
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/2
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 业务方自定义的灯塔数据上报接口
/// 业务方可通过{@link TPMgr#setBeaconDataReporterBeforeInit(ITPBeaconDataReporter)}将该实例配置给播放器的灯塔模块
/// 播放器的灯塔模块将不进行initSDK，并且所有播放器数据将会通过该通路回调出来
@protocol ITPBeaconDataReporter <NSObject>

/// 上报数据回调
/// @param appKey    上报关联的appKey
/// @param eventId   上报事件id
/// @param dataMap   上报数据
- (void)trackCustomKVEvent:(NSString *)appKey eventId:(NSString *)eventId dataMap:(NSDictionary *)dataMap;

@end
