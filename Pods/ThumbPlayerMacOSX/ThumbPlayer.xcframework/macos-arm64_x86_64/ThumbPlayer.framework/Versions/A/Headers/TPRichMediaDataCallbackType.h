/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPRichMediaDataCallbackType.h
 * @brief    富媒体数据回调类型定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 富媒体数据回调类型定义. 枚举值必须跟内核相应的值保持一致(TPPlayerRichMediaActOnOptional枚举值)。
typedef NS_ENUM(NSInteger, TPRichMediaDataCallbackType) {
    /// 未知富媒体数据回调类型
    TPRichMediaDataCallbackTypeUnknown = -1,
    /// 直接从富媒体回调接口中回抛.见ITPRichMediaSynchronizerDelegate的onRichMediaData方法
    TPRichMediaDataCallbackTypeDirect = 0,
    /// 伴随视频数据, 暂未实现
    TPRichMediaDataCallbackTypeWithVideoFrame = 1,
    /// 伴随音频数据，暂未实现
    TPRichMediaDataCallbackTypeWithAudioFrame = 2,
};

NS_ASSUME_NONNULL_END