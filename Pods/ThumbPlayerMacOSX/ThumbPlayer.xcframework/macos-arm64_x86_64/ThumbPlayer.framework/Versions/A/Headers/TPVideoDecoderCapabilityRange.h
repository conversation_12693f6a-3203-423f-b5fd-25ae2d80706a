/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPVideoDecoderCapabilityRange.h
 * @brief    描述影响视频解码性能的关键要素，需要针对某个Codec的解码能力进行能力自定义时使用，所以是一个区间值
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/5/5
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 描述影响视频解码性能的关键要素，分辨率和帧率的一个区间值
@interface TPVideoDecoderCapabilityRange : NSObject

/// codec支持的width的上限，如果不设置，则默认为最大值
@property (nonatomic, assign) int upperWidth;

/// codec支持的height的上限，如果不设置，则默认为最大值
@property (nonatomic, assign) int upperHeight;

/// codec支持的帧率的上限，如果不设置，则默认为最大值
@property (nonatomic, assign) float upperFrameRate;

/// codec支持的width的下限，如果不设置，则默认为0
@property (nonatomic, assign) int lowerWidth;

/// codec支持的height的下限，如果不设置，则默认为0
@property (nonatomic, assign) int lowerHeight;

/// codec支持的帧率的下限，如果不设置，则默认为0
@property (nonatomic, assign) float lowerFrameRate;

@end
