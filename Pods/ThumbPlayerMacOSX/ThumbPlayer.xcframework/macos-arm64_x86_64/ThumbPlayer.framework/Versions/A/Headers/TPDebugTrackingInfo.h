/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPDebugTrackingInfo.h
 * @brief    播放器运行过程中的一些细节信息，用于调试或者上报
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/1
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

#pragma mark - TPDebugTrackingInfoID
/// 命名规则：TPDebugTrackingInfoID + 事件名称
/// 区域划分 0-999:属于内核层事件定义区
/// 1000-9999:属于上层事件定义区
typedef NS_ENUM(NSInteger, TPDebugTrackingInfoID) {
    /// 未知类型
    TPDebugTrackingInfoIDUnknown = -1,
    /// 首次起播
    TPDebugTrackingInfoIDFirstStart = 0,
    /// 播放调度线程开始prepared
    TPDebugTrackingInfoIDPlayerSchedulingThreadPrepareStart = 1,
    /// Demux 线程开始prepare
    TPDebugTrackingInfoIDDemuxerThreadPrepareStart = 2,
    /// Demuxer打开文件开始
    TPDebugTrackingInfoIDDemuxerFileOpenStart = 3,
    /// Demuxer打开文件结束
    TPDebugTrackingInfoIDDemuxerFileOpenEnd = 4,
    /// Demux 线程开始onPrepared
    TPDebugTrackingInfoIDDemuxerThreadOnPrepared = 5,
    /// 播放调度线程开始onPrepared
    TPDebugTrackingInfoIDPlayerSchedulingThreadOnPrepared = 6,
    /// 首次视频解码器创建开始
    TPDebugTrackingInfoIDFirstVideoDecoderCreateStart = 7,
    /// 首次音频解码器创建结束
    TPDebugTrackingInfoIDFirstAudioDecoderCreateStart = 8,
    /// 首帧视频渲染
    TPDebugTrackingInfoIDFirstVideoFrameRendered = 9,
    /// 首帧音频渲染
    TPDebugTrackingInfoIDFirstAudioFrameRendered = 10,
    /// 第一个packet读取完毕
    TPDebugTrackingInfoIDFirstPacketRead = 19,

    /// ThumbPlayer 上层的打点事件.
    /// TPPlayer api setDataSource 开始
    TPDebugTrackingInfoIDTpApiSetDataSourceStart = 1000,
    /// TPPlayer api setDataSource 结束
    TPDebugTrackingInfoIDTpApiSetDataSourceEnd = 1001,
    /// TPPlayer api prepare 开始
    TPDebugTrackingInfoIDTpApiPrepareStart = 1003,
    /// TPPlayer api onPrepare callback
    TPDebugTrackingInfoIDTpApiOnPrepared = 1004,
    /// TPPlayer api first video frame render callback
    TPDebugTrackingInfoIDTpApiOnFirstVideoFrameRendered = 1005,
    /// TPPlayer api first audio frame render callback
    TPDebugTrackingInfoIDTpApiOnFirstAudioFrameRendered = 1006,
};

#pragma mark - TPDebugTrackingInfo
/// 播放器运行过程中的一些细节信息，用于调试或者上报
@interface TPDebugTrackingInfo : NSObject

- (instancetype)init NS_UNAVAILABLE;

/// 构造方法，内部填充 timeSince1970Ms 和 elapsedTimeSinceBootMs
- (instancetype)initWithDebugTrackingInfoID:(TPDebugTrackingInfoID)infoID;

/// 构造方法
/// @param infoID TPDebugTrackingInfoID
/// @param timeSince1970Ms 从UTC1970-1-1 0:0:0以来的毫秒数
/// @param elapsedTimeSinceBootMs 开机以后的毫秒数
- (instancetype)initWithDebugTrackingInfoID:(TPDebugTrackingInfoID)infoID
                            timeSince1970Ms:(int64_t)timeSince1970Ms
                     elapsedTimeSinceBootMs:(int64_t)elapsedTimeSinceBootMs;

/// 类型
@property (nonatomic, assign, readonly) TPDebugTrackingInfoID trackingInfoID;

/// 信息采集时的系统实时时间，即从UTC1970-1-1 0:0:0开始计时，单位毫秒
@property (nonatomic, assign, readonly) int64_t timeSince1970Ms;

/// 信息采集时的系统自启动以来经过的时间，单位毫秒
@property (nonatomic, assign, readonly) int64_t elapsedTimeSinceBootMs;

@end
