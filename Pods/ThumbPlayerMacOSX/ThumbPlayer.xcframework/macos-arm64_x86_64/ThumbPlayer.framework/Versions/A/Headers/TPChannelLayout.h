/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPChannelLayout.h
 * @brief    音频channel layout定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2022/2/27
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 音频channel layout定义，与内核保持一致
typedef NS_OPTIONS(uint64_t, TPChannelLayout) {
    TPCHUnknown             = 0x00000000,
    TPCHFrontLeft           = 0x00000001,
    TPCHFrontRight          = 0x00000002,
    TPCHFrontCenter         = 0x00000004,
    TPCHLowFrequency        = 0x00000008,
    TPCHBackLeft            = 0x00000010,
    TPCHBackRight           = 0x00000020,
    TPCHFrontLeftOfCenter   = 0x00000040,
    TPCHFrontRightOfCenter  = 0x00000080,
    TPCHBackCenter          = 0x00000100,
    TPCHSideLeft            = 0x00000200,
    TPCHSideRight           = 0x00000400,
    TPCHTopCenter           = 0x00000800,
    TPCHTopFrontLeft        = 0x00001000,
    TPCHTopFrontCenter      = 0x00002000,
    TPCHTopFrontRight       = 0x00004000,
    TPCHTopBackLeft         = 0x00008000,
    TPCHTopBackCenter       = 0x00010000,
    TPCHTopBackRight        = 0x00020000,
    TPCHStereoLeft          = 0x20000000,
    TPCHStereoRight         = 0x40000000,
    TPCHWideLeft            = 0x0000000080000000ULL,
    TPCHWideRight           = 0x0000000100000000ULL,
    TPCHSurroundDirectLeft  = 0x0000000200000000ULL,
    TPCHSurroundDirectRight = 0x0000000400000000ULL,
    TPCHLowFrequency2       = 0x0000000800000000ULL,
    TPCHTopSideLeft         = (1ULL << 36),
    TPCHTopSideRight        = (1ULL << 37),
    TPCHBottomFrontCenter   = (1ULL << 38),
    TPCHBottomFrontLeft     = (1ULL << 39),
    TPCHBottomFrontRight    = (1ULL << 40),
    TPCHLayoutNative        = 0x8000000000000000ULL,
    
    TPCHLayoutMono            = (TPCHFrontCenter),
    TPCHLayoutStereo          = (TPCHFrontLeft|TPCHFrontRight),
    TPCHLayout2Point1         = (TPCHLayoutStereo|TPCHLowFrequency),
    TPCHLayout21              = (TPCHLayoutStereo|TPCHBackCenter),
    TPCHLayoutSurround        = (TPCHLayoutStereo|TPCHFrontCenter),
    TPCHLayout3Point1         = (TPCHLayoutSurround|TPCHLowFrequency),
    TPCHLayout4Point0         = (TPCHLayoutSurround|TPCHBackCenter),
    TPCHLayout4Point1         = (TPCHLayout4Point0|TPCHLowFrequency),
    TPCHLayout22              = (TPCHLayoutStereo|TPCHSideLeft|TPCHSideRight),
    TPCHLayoutQuad            = (TPCHLayoutStereo|TPCHBackLeft|TPCHBackRight),
    TPCHLayout5Point0         = (TPCHLayoutSurround|TPCHSideLeft|TPCHSideRight),
    TPCHLayout5Point1         = (TPCHLayout5Point0|TPCHLowFrequency),
    TPCHLayout5Point0Back     = (TPCHLayoutSurround|TPCHBackLeft|TPCHBackRight),
    TPCHLayout5Point1Back     = (TPCHLayout5Point0Back|TPCHLowFrequency),
    TPCHLayout6Point0         = (TPCHLayout5Point0|TPCHBackCenter),
    TPCHLayout6Point0Front    = (TPCHLayout22|TPCHFrontLeftOfCenter|TPCHFrontRightOfCenter),
    TPCHLayoutHexagonal       = (TPCHLayout5Point0Back|TPCHBackCenter),
    TPCHLayout6Point1         = (TPCHLayout5Point1|TPCHBackCenter),
    TPCHLayout6Point1Back     = (TPCHLayout5Point1Back|TPCHBackCenter),
    TPCHLayout6Point1Front    = (TPCHLayout6Point0Front|TPCHLowFrequency),
    TPCHLayout7Point0         = (TPCHLayout5Point0|TPCHBackLeft|TPCHBackRight),
    TPCHLayout7Point0Front    = (TPCHLayout5Point0|TPCHFrontLeftOfCenter|TPCHFrontRightOfCenter),
    TPCHLayout7Point1         = (TPCHLayout5Point1|TPCHBackLeft|TPCHBackRight),
    TPCHLayout7Point1Wide     = (TPCHLayout5Point1|TPCHFrontLeftOfCenter|TPCHFrontRightOfCenter),
    TPCHLayout7Point1WideBack = (TPCHLayout5Point1Back|TPCHFrontLeftOfCenter|TPCHFrontRightOfCenter),
    TPCHLayoutOctagonal       = (TPCHLayout5Point0|TPCHBackLeft|TPCHBackCenter|TPCHBackRight),
    TPCHLayoutHexadecagonal   = (TPCHLayoutOctagonal|TPCHWideLeft|TPCHWideRight|TPCHTopBackLeft|TPCHTopBackRight|TPCHTopBackCenter|TPCHTopFrontCenter|
                               TPCHTopFrontLeft|TPCHTopFrontRight),
    TPCHLayoutStereoDownmix   = (TPCHStereoLeft|TPCHStereoRight),
    TPCHLayou22Point2         = (TPCHLayout5Point1Back|TPCHFrontLeftOfCenter|TPCHFrontRightOfCenter|
                                 TPCHBackCenter|TPCHLowFrequency2|TPCHSideLeft|TPCHSideRight|TPCHTopFrontLeft|
                                 TPCHTopFrontRight|TPCHTopFrontCenter|TPCHTopCenter|TPCHTopBackLeft|TPCHTopBackRight|
                                 TPCHTopSideLeft|TPCHTopSideRight|TPCHTopBackCenter|TPCHBottomFrontCenter|
                                 TPCHBottomFrontLeft|TPCHBottomFrontRight),
    TPCHLayout10Point2        = (TPCHLayout7Point1WideBack|TPCHTopFrontLeft|
                                 TPCHTopFrontRight|TPCHBackCenter|TPCHLowFrequency2),
    TPCHLayout5Point1Point2   = (TPCHLayout5Point1|TPCHTopFrontLeft|TPCHTopFrontRight),
    TPCHLayout5Point1Point4   = (TPCHLayout5Point1Point2|TPCHTopBackLeft|TPCHTopBackRight),
    TPCHLayout7Point1Point2   = (TPCHLayout7Point1|TPCHTopFrontLeft|TPCHTopFrontRight),
    TPCHLayout7Point1Point4   = (TPCHLayout7Point1|TPCHTopFrontLeft|TPCHTopFrontRight|TPCHTopBackLeft|TPCHTopBackRight),
};
