/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TPLiveReportInfo.h
 * @brief    需要业务侧辅助上报的字段
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/2
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPDefaultReportInfo.h"

/// 直播数据上报基础数据
@interface TPLiveReportInfo : TPDefaultReportInfo

/// 广告播放时长
@property (nonatomic, assign) NSInteger adPlayLength;
/// 节目pid
@property (nonatomic, assign) NSInteger programId;
/// 直播流id
@property (nonatomic, assign) NSInteger streamId;
/// 内容ID
@property (nonatomic, assign) NSInteger contentId;
/// 直播可试看时间
@property (nonatomic, assign) NSInteger playTime;
/// 用户对该频道是否付费
@property (nonatomic, assign) BOOL isUserPay;
/// 流样式,如FLV/HLS-H264-HEVC-VR-DOLBY，5个类型分别用二进制0/1表示，
/// 上报该十进制数，例如：flv h264 dolby的流则二进制为01001，十进制为9，上报值为9
@property (nonatomic, assign) NSInteger liveType;
/// cdn服务器ip, 取值cdn头部response的X-ServerIp值
@property (nonatomic, copy) NSString *cdnServer;
/// 是否回看
@property (nonatomic, assign) BOOL isLookBack;
/// 内容延迟时间
@property (nonatomic, assign) NSInteger liveDelay;

@end
