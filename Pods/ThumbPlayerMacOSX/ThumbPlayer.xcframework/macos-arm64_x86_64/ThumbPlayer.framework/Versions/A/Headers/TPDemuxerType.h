/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPDemuxerType.h
 * @brief    demuxer type枚举定义，其值与内核定义保持一致，不能随便修改
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/1
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 枚举值和内核是保持一致的，不能随意修改
typedef NS_ENUM(NSInteger, TPDemuxerType) {
    /// 未知类型
    TPDemuxerTypeUnknown      = -1,

    /// ffmpeg demuxer
    TPDemuxerTypeFFmpeg       = 0,
    /// 自研standalone demuxer
    TPDemuxerTypeStandalone   = 1,
    /// webrtc demuxer
    TPDemuxerTypeWebrtc       = 2,
    /// webrtcengine demuxer
    /// webrtc-engine基于webrtc-core提供标准webrtc协议栈以及音视频处理能力、网络传输等模块
    TPDemuxerTypeWebrtcEngine = 3,
};
