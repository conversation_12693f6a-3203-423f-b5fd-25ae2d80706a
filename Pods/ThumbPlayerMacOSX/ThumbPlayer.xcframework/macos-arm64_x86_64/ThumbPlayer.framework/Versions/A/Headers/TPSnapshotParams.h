/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPSnapshotParams.h
 * @brief    截图参数
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/21
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPPixelFormat.h"
#import "TPSnapshotMode.h"

NS_ASSUME_NONNULL_BEGIN

/// 截图参数
@interface TPSnapshotParams : NSObject

/// 输出的宽，为0代表使用默认宽，即图像原始的宽
/// 默认为0
@property (nonatomic, assign) int width;
/// 输出的高，为0代表使用默认高，即图像原始的高
/// 默认为0
@property (nonatomic, assign) int height;
/// 截图模式，请参考TPSnapshotMode的注释
/// 默认为TPSnapshotModeAll
@property (nonatomic, assign) TPSnapshotMode snapshotMode;
/// 实际截取的图像时间戳会在[positionMs - positionMsToleranceBefore, positionMs +
/// positionMsToleranceAfter]之间。
/// 两个值均为0时，代表将启用精准seek来进行截图，这将耗费一定的解码时间。
/// 容忍在请求截图时间之前截图的毫秒数，默认为0
@property (nonatomic, assign) int64_t positionMsToleranceBefore;
/// 容忍在请求截图时间之后截图的毫秒数，默认为0
@property (nonatomic, assign) int64_t positionMsToleranceAfter;

@end

NS_ASSUME_NONNULL_END
