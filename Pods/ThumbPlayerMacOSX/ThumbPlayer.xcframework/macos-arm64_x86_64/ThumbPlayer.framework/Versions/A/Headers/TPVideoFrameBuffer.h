/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPVideoFrameBuffer.h
 * @brief    视频帧数据定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/28
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import <CoreVideo/CVPixelBuffer.h>
#import "TPPixelFormat.h"
#import "TPFrameEventFlag.h"

#if TARGET_OS_OSX
#import <AppKit/AppKit.h>
#else
#import <UIKit/UIKit.h>
#endif

NS_ASSUME_NONNULL_BEGIN

/// 视频帧数据定义
@interface TPVideoFrameBuffer : NSObject

/// TPVideoFrameBuffer 释放时，通过该函数指针回调让外部主动释放内存
typedef void (*TPVideoFrameBufferReleaseDataCallback)(id userData, uint8_t **planeData, int planeCnt);

/// 当format为TPPixelFormatCVPixelBuffer时，该值才有效，并且等于data[3]
@property (nonatomic, assign, readonly) CVPixelBufferRef pixelBuffer;

/// 原始视频帧数据
/// 当数据为非planar时，data[0]存放数据，
/// 当数据为planar时，data[0] 至 data[planeCount-1] 存放每个通道的数据。
/// 当format为TPPixelFormatCVPixelBuffer时，data[3]指向CVPixelBufferRef数据。
@property (nonatomic, assign, readonly) uint8_t **data;

/// 每个通道的linesize
@property (nonatomic, strong, readonly) NSArray<NSNumber *> *lineSize;

/// 旋转角度，取值为0、90、180、270
@property (nonatomic, assign) int rotation;

/// 显示宽
/// 部分带有SAR信息的视频，数据宽高不一定等于显示宽高，需要根据该值来决定如何做显示
@property (nonatomic, assign) int displayWidth;

/// 显示高
/// 部分带有SAR信息的视频，数据宽高不一定等于显示宽高，需要根据该值来决定如何做显示
@property (nonatomic, assign) int displayHeight;

/// presentation time stamp, 单位毫秒
@property (nonatomic, assign) int64_t ptsMs;

/// 当前帧的轨道id，与ITPPlayer中的getTrackInfo返回的数组的下标对应
@property (nonatomic, assign) int trackID;

/// 扩展参数，播放器事件标记，默认为TPFrameEventFlagUnknown，按位或（bitwise-or)
@property (nonatomic, assign) TPFrameEventFlag eventFlag;

/// 视频格式
@property (nonatomic, assign, readonly) TPPixelFormat format;

/// 视频原始宽度，即数据宽度
@property (nonatomic, assign, readonly) int width;

/// 视频原始高度，即数据高度
@property (nonatomic, assign, readonly) int height;

/// 默认构造方法禁止使用，请使用其他构造方法
- (instancetype)init NS_UNAVAILABLE;

/// 使用CVPixelBufferRef数据转TPVideoFrameBuffer的构造方法，复用pixelBuffer
/// @param pixelBuffer CVPixelBufferRef数据
- (instancetype)initWithCVPixelBuffer:(CVPixelBufferRef)pixelBuffer;

/// 使用planar数据指针创建TPVideoFrameBuffer的构造方法，planeData数据内存进行复用，需要传递一个函数指针来释放内存数据.
/// @param planeData 复用的planar数据指针
/// @param lineSize 每个通道的linesize
/// @param format 视频格式
/// @param width 视频宽
/// @param height 视频高
/// @param userData user数据，回调让外部注释内存时需传回去
/// @param releaseCb 给定一个释放回调函数指针，当该对象释放时回调该方法以便内存的所有者可以释放内存。不能为空
/// @return TPVideoFrameBuffer，若format与lineSize的长度不匹配则返回nil
- (nullable instancetype)initWithPlanarData:(uint8_t **)planeData
                                   lineSize:(NSArray<NSNumber *> *)lineSize
                                     format:(TPPixelFormat)format
                                      width:(int)width
                                     height:(int)height
                                   userData:(id)userData
                                  releaseCb:(TPVideoFrameBufferReleaseDataCallback)releaseCb;

/// 使用planar数据指针创建TPVideoFrameBuffer的构造方法，内部会进行数据内存拷贝
/// @param planeData 复用的planar数据指针
/// @param lineSize 每个通道的linesize
/// @param format 视频格式
/// @param width 视频宽
/// @param height 视频高
/// @return TPVideoFrameBuffer，若format与lineSize的长度不匹配则返回nil
- (nullable instancetype)initWithPlanarData:(uint8_t **)planeData
                                   lineSize:(NSArray<NSNumber *> *)lineSize
                                     format:(TPPixelFormat)format
                                      width:(int)width
                                     height:(int)height;

/// 将TPVideoFrameBuffer转化为UIImage
/// 目前仅支持TPPixelFormatRGBA、TPPixelFormatRGB24和TPPixelFormatCVPixelBuffer
/// @return 转换后的UIImage或NSImage，若转化失败则返回nil
#if TARGET_OS_OSX
- (nullable NSImage *)toImage;
#else
- (nullable UIImage *)toImage;
#endif
@end

NS_ASSUME_NONNULL_END
