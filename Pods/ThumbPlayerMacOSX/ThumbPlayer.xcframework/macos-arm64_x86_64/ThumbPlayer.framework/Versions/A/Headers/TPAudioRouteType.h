/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TPAudioRouteType.h
 * @brief    音频route类型定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/4/23
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
 
/// 音频route类型定义
typedef NS_ENUM(NSInteger, TPAudioRouteType) {
    /// 未知类型
    TPAudioRouteTypeUnknown = -1,
    /// 扬声器输出
    TPAudioRouteTypeOutBuiltInSpeaker = 0,
    /// 耳机输出
    TPAudioRouteTypeOutHeadphone = 1,
    /// 蓝牙A2DP输出
    TPAudioRouteTypeOutBluetoothA2dp = 2,

    TPAudioRouteTypeOutOthers = 99,

    /// 音质麦克风输入
    TPAudioRouteTypeInBuiltInMicrophone = 100,
    /// 耳机输入
    TPAudioRouteTypeInHeadphone = 101,
    /// 蓝牙A2DP输入
    TPAudioRouteTypeInBluetoothA2dp = 102,

    TPAudioRouteTypeInOthers = 199,
};
