/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TPDefaultReportInfo.h
 * @brief    需要业务侧辅助上报的字段
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/2
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

#if TARGET_OS_OSX
#import <AppKit/AppKit.h>
#else
#import <UIKit/UIKit.h>
#endif

/// 数据上报基础数据
@interface TPDefaultReportInfo : NSObject

/// 是否在线
@property (nonatomic, assign) BOOL isOnline;
/// 是否启用P2P
@property (nonatomic, assign) BOOL enableP2p;
/// 测试id,如果有
@property (nonatomic, assign) NSInteger testId;
/// cdn id
@property (nonatomic, assign) NSInteger cdnId;
/// 下载类型,区分是mp4还是hls, 1:mp4 3:hls
@property (nonatomic, assign) NSInteger dlType;
/// 登录类型，0:未登录 1:qq 2:QQopenid登陆 3:WXopenid 登陆 4:commonid 5:手机号
@property (nonatomic, assign) NSInteger loginType;
/// 首次加载对应的视频格式, 10203
@property (nonatomic, assign) NSInteger mediaFormat;
/// 首次加载对应的音视频码率（单位：KBps）
@property (nonatomic, assign) NSInteger mediaRate;
/// cgi返回的url index
@property (nonatomic, assign) NSInteger cdnUrlIndex;
/// 平台号（业务号），用于区分业务，同一个业务区分安卓 和 IOS
@property (nonatomic, assign) NSInteger platform;
/// 视频本身时长(单位:s)
@property (nonatomic, assign) CGFloat mediaDuration;
/// QQ号，当前情况下仍然可以拿到用户qq号码，可以直接上报用户QQ号码
@property (nonatomic, copy) NSString *uin;
/// QQ互联登陆情况下无法获取用户QQ号码，上报用户QQ对应openid
@property (nonatomic, copy) NSString *qqOpenId;
/// 微信登陆情况下只能获取用户微信openid
@property (nonatomic, copy) NSString *wxOpenId;
/// 未登录情况下，用户的唯一标示，各业务内保持唯一即可（因为不同业务会有业务id进行区分），主要用于单用户问题定位
@property (nonatomic, copy) NSString *guid;
/// 业务后台或者客户端能拿到的用户IP
@property (nonatomic, copy) NSString *uip;
/// CDN返回的用户IP: ipv4, ipv6 该IP 为实际访问CDN的IP，用于定位多出口情况
@property (nonatomic, copy) NSString *cdnUip;
/// 下载CDN对应的IP:ipv4,ipv6
@property (nonatomic, copy) NSString *cdnIp;
/// 终端版本
@property (nonatomic, copy) NSString *appVersion;
/// 视频ID，（各业务向后台换CDN下载地址传入的id）
@property (nonatomic, copy) NSString *vid;
/// 播放视频分辨率
@property (nonatomic, copy) NSString *mediaResolution;
/// 对应的CDN类型,类似cdnId
@property (nonatomic, assign) NSInteger subtitleCdnType;
/// 对应的CGI返回的第几条地址,主要反映重试几次才成功 ，第一次成功上报1，重试第二次才成功上报2，依次类推。
@property (nonatomic, assign) NSInteger subtitleUrlIndex;
/// 免流类型
@property (nonatomic, assign) NSInteger freeType;
/// 业务场景ID，区分同一个平台号不同场景；0，普通播放；1 广告播放；
@property (nonatomic, assign) NSInteger scenesId;
/// 透传app层的上报，比如直播需要的vip_type
@property (nonatomic, copy) NSMutableDictionary<NSString *, NSString *> *extraReportInfo;
/// 播放类型，外部不用设置 0: 点播；1: 直播；-1: 其他;
@property (nonatomic, readonly) NSInteger playType;

@end
