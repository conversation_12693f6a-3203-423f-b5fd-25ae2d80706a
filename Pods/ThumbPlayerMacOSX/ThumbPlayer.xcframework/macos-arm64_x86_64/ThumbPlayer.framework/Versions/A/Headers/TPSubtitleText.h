/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPSubtitleText.h
 * @brief    字幕文本
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/03/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPSubtitleData.h"

/// 字幕文本，用于字幕输出文本
@interface TPSubtitleText : TPSubtitleData

/// 字幕文本字符串，utf-8编码。
/// 对于同一路字幕，如果一个时间点同时命中了两段字幕文本，也是一个一个输出。
/// 对于选择了多路字幕的情况，多个轨道按照选择顺序依次输出
@property (nonatomic, copy) NSString *text;

/// 当前输出的字幕的轨道id，该轨道id即 ITPPlayer#getTrackInfo 返回的数组下标。
@property (nonatomic, assign) int trackID;

@end

