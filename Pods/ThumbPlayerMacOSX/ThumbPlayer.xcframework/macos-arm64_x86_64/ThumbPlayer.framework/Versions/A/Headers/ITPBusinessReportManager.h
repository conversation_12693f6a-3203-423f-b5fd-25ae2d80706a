/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     ITPBusinessReportManager.h
 * @brief    提供给业务侧调用的上报类，由业务侧进行辅助上报
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/2
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPDefaultReportInfo.h"

/// 数据上报管理器
/// 可通过{@link ITPPlayer#getReportManager()}获取
@protocol ITPBusinessReportManager <NSObject>

/// 设置上报信息获取的接口，方便从业务侧获取某些上报字段
/// @param infoGetter infoGetter
- (void)setReportInfoGetter:(nonnull TPDefaultReportInfo *)infoGetter;


/// 设置上报抽样率。只能在prepare()前调用
/// 播放器 prepare() => stop()/reset()/release() 为一次播放流水，这里以播放流水为最小单位进行抽样
/// 若被抽样，则该次播放流水中所有的上报事件都会进行上报
/// 若不设置抽样率，则默认每一次播放流水都会进行数据上报
/// @param rate 抽样率，取值[0.0, 1.0]，若<0.0则为0.0，若大于1.0则为1.0
- (void)setReportSamplingRate:(CGFloat)rate;

@end

