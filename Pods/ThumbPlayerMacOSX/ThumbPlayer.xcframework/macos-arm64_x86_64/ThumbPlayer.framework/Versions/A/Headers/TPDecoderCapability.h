/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPDecoderCapability.h
 * @brief    获取Thumbplayer的解码能力
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/23
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPVideoCodecType.h"
#import "TPAudioCodecType.h"
#import "TPVideoDecoderType.h"
#import "TPAudioDecoderType.h"
#import "TPVideoDecoderCapabilityRange.h"
#import "TPAudioCapabilityQueryParams.h"

/// 解码能力支持情况定义
/// 注意：这些值的定义值不要改动，此值和内核层的值是一一对应关系
typedef NS_ENUM(NSInteger, TPDecoderCap) {
    /// 不支持解码
    TPDecoderCapNotSupport = 0,
    /// 支持解码
    TPDecoderCapSupport = 1,
};

/// 解码能力
@interface TPDecoderCapability : NSObject

/// 获取指定的video codec类型对应的解码能力支持情况，不关注解码模式。
/// @param codecType 具体codec类型
/// @param width 宽
/// @param height 高
/// @param frameRate 帧率
/// @return 对应的解码能力
+ (TPDecoderCap)videoDecoderCapability:(TPVideoCodecType)codecType
                                 width:(int)width
                                height:(int)height
                             frameRate:(float)frameRate;

/// 获取指定的video codec类型对应的解码能力支持情况，判断该编码格式在该宽、高及帧率下是否支持
/// @param codecType 具体codec类型
/// @param decoderType 解码器类型-ffmpeg，VideoToolbox等。
/// @param width 宽
/// @param height 高
/// @param frameRate 帧率
/// @return 对应的解码能力
+ (TPDecoderCap)videoDecoderCapability:(TPVideoCodecType)codecType
                           decoderType:(TPVideoDecoderType)decoderType
                                 width:(int)width
                                height:(int)height
                             frameRate:(float)frameRate;

/// 获取指定的audio codec类型对应的解码能力支持情况，不关注解码模式。
/// @param codecType 具体codec类型
/// @return 对应的解码能力
+ (TPDecoderCap)audioDecoderCapability:(TPAudioCodecType)codecType;

/// 获取指定的audio codec类型对应的解码能力支持情况，判断该编码格式在该宽、高及帧率下是否支持
/// @param codecType 具体codec类型
/// @param decoderType 解码器类型-ffmpeg，AudioToolbox等。
/// @return 对应的解码能力
+ (TPDecoderCap)audioDecoderCapability:(TPAudioCodecType)codecType
                           decoderType:(TPAudioDecoderType)decoderType;

/// 获取指定的audio codec类型对应的解码能力支持情况，判断该编码格式在该宽、高及帧率下是否支持
/// @param queryParams 解码器查询参数
/// @return 对应的解码能力
+ (TPDecoderCap)audioDecoderCapabilityWithParams:(TPAudioCapabilityQueryParams *)queryParams;

/// 添加定制化的视频硬解能力，描述该编码格式的videotoolbox硬解能力支持情况，一般可用于业务方控制codec能力黑白名单
/// @param codecType 具体codec类型
/// @param decoderCap 自定义的解码能力
+ (void)addCustomizedVideoToolboxCapability:(TPVideoCodecType)codecType
                                 decoderCap:(TPDecoderCap)decoderCap;

/// 添加定制化的视频硬解能力，描述该编码格式的videotoolbox硬解能力支持情况，一般可用于业务方控制codec能力黑白名单
/// @param codecType 具体codec类型
/// @param capabilityRange 视频解码性能区间，指定codec的解码能力区间
/// @param decoderCap 自定义的解码能力
+ (void)addCustomizedVideoToolboxCapability:(TPVideoCodecType)codecType
                            capabilityRange:(TPVideoDecoderCapabilityRange *)capabilityRange
                                 decoderCap:(TPDecoderCap)decoderCap;

/// 添加定制化的音频硬解能力，描述该编码格式的audiotoolbox硬解能力支持情况，一般可用于业务方控制codec能力黑白名单
/// @param codecType 具体codec类型
/// @param decoderCap 自定义的解码能力
+ (void)addCustomizedAudioToolboxCapability:(TPAudioCodecType)codecType
                                 decoderCap:(TPDecoderCap)decoderCap;

@end
