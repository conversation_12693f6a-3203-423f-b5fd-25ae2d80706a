/*****************************************************************************
 * @copyright  Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file       ITPDrmMediaAsset.h
 * @brief      DRM类型媒体资源接口
 * <AUTHOR>
 * @version    1.0.0
 * @date       2023/3/8
 * @license    GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPUrlMediaAsset.h"
#import "TPDrmType.h"

NS_ASSUME_NONNULL_BEGIN

/// 自研DRM密钥参数
/// 配合TPDrmType TP_DRM_TYPE_SELF_DEVELOPED使用
/// 用于SDD(Self-Developed Decryptor)方案
/// 设置和获取都通过：ITPMediaAsset setParamValue:forKey:， ITPMediaAsset paramValueForKey:
FOUNDATION_EXPORT NSString *const TP_ASSET_PARAM_KEY_STRING_SDD_KEY;

/// 自研DRM随机数参数
/// 配合TPDrmType TP_DRM_TYPE_SELF_DEVELOPED使用
/// 用于SDD(Self-Developed Decryptor)方案
/// 设置和获取都通过：ITPMediaAsset setParamValue:forKey:， ITPMediaAsset paramValueForKey:
FOUNDATION_EXPORT NSString *const TP_ASSET_PARAM_KEY_STRING_SDD_NONCE;

/// DRM asset接口，用于描述DRM资源
@protocol ITPDrmMediaAsset <ITPUrlMediaAsset>

/// 设置Drm的类型
/// @param drmType Drm类型
- (void)setDrmType:(TPDrmType)drmType;

/// 设置证书url，对于除FairPlay外的其它Drm类型，此证书url对应理解为provision url
/// @param certificateUrl 证书url
- (void)setCertificateUrl:(NSString *)certificateUrl;

/// 设置证书url及httpHeader，对于除FairPlay外的其它Drm类型，此证书url对应理解为provision url
/// @param certificateUrl 证书url
/// @param httpHeader certificateUrl的http header
- (void)setCertificateUrl:(NSString *)certificateUrl httpHeader:(nullable NSDictionary<NSString *, NSString *> *)httpHeader;

/// 设置license的url,license中封装了加密媒体流的解密密钥和密钥使用规则
/// @param licenseUrl license url
- (void)setLicenseUrl:(NSString *)licenseUrl;

/// 设置license的url,license中封装了加密媒体流的解密密钥和密钥使用规则
/// @param licenseUrl license url
/// @param httpHeader license url的http header
- (void)setLicenseUrl:(NSString *)licenseUrl httpHeader:(nullable NSDictionary<NSString *, NSString *> *)httpHeader;

/// 设置drm数据保存路径
/// @param cacheDir drm数据保存路径
- (void)setCacheDir:(nullable NSString *)cacheDir;
@end

NS_ASSUME_NONNULL_END
