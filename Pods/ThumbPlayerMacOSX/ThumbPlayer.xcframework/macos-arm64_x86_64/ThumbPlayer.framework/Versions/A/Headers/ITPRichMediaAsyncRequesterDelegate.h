/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPRichMediaAsyncRequesterDelegate.h
 * @brief    ITPRichMediaAsyncRequester的delegate定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPRichMediaData.h"
#import "TPError.h"

@protocol ITPRichMediaAsyncRequester;

NS_ASSUME_NONNULL_BEGIN

/// ITPRichMediaAsyncRequester的delegate定义
@protocol ITPRichMediaAsyncRequesterDelegate <NSObject>

/// 加载资源准备成功
/// 在ITPRichMediaAsyncRequester的prepareAsync发起资源加载后回调
/// @param requester 富媒体请求器实例
- (void)onRequesterPrepared:(id<ITPRichMediaAsyncRequester>)requester;

/// 富媒体请求器运行出错，无法再继续工作。只能释放当前对象后，通过重新创建实例来解决。
/// @param requester 富媒体请求器实例
/// @param error 错误信息
- (void)onRequester:(id<ITPRichMediaAsyncRequester>)requester error:(TPError *)error;

/// 表示下列数据请求接口调用，数据返回成功：
/// ITPRichMediaAsyncRequester的requestDataAsync:AtPositionMs
/// ITPRichMediaAsyncRequester的requestDataAsync:AtPositionMsArray
/// ITPRichMediaAsyncRequester的requestDataAsync:AtTimeRange
/// ITPRichMediaAsyncRequester的requestDataAsync:AtPositionMsArray
/// @param requester 富媒体请求器实例
/// @param requestID 请求ID
/// @param featureIndex 富媒体功能下标索引
/// @param data 返回的富媒体功能数据
- (void)onRequestDataSuccess:(id<ITPRichMediaAsyncRequester>)requester
                   requestID:(int)requestID
                featureIndex:(int)featureIndex
                        data:(TPRichMediaData *)data;

/// 表示下列数据请求接口调用，数据返回失败：
/// ITPRichMediaAsyncRequester的requestDataAsync:AtPositionMs
/// ITPRichMediaAsyncRequester的requestDataAsync:AtPositionMsArray
/// ITPRichMediaAsyncRequester的requestDataAsync:AtTimeRange
/// ITPRichMediaAsyncRequester的requestDataAsync:AtPositionMsArray
/// @param requester 富媒体请求器实例
/// @param requestID 请求ID
/// @param featureIndex 富媒体功能下标索引
/// @param error 错误信息
- (void)onRequestDataError:(id<ITPRichMediaAsyncRequester>)requester
                 requestID:(int)requestID
              featureIndex:(int)featureIndex
                     error:(TPError *)error;
@end

NS_ASSUME_NONNULL_END
