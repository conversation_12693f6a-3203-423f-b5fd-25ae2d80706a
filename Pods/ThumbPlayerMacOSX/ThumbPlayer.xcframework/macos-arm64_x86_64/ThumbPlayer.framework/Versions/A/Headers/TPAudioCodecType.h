/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TPAudioCodecType.h
 * @brief    codec type枚举定义，枚举值与内核TPCodec.h文件中TPCodecID的枚举值保持一致
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/23
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 音频Codec id定义，与内核层数值定义一致
/// Note:枚举的命名采用峰驼无法保留原有意义，+下划线.
typedef NS_ENUM(NSInteger, TPAudioCodecType) {
    TPAudioCodecTypeUnknown = -1,
    
#pragma mark - audio codecs
    TPAudioCodecTypeMP2 = 5000,
    /// preferred ID for decoding MPEG audio layer 1, 2 or 3
    TPAudioCodecTypeMP3 = 5001,
    TPAudioCodecTypeAAC = 5002,
    TPAudioCodecTypeAC3 = 5003,
    TPAudioCodecTypeDTS = 5004,
    TPAudioCodecTypeVORBIS = 5005,
    TPAudioCodecTypeDVAUDIO = 5006,
    TPAudioCodecTypeWMAV1 = 5007,
    TPAudioCodecTypeWMAV2 = 5008,
    TPAudioCodecTypeMACE3 = 5009,
    TPAudioCodecTypeMACE6 = 5010,
    TPAudioCodecTypeVMDAUDIO = 5011,
    TPAudioCodecTypeFLAC = 5012,
    TPAudioCodecTypeMP3ADU = 5013,
    TPAudioCodecTypeMP3ON4 = 5014,
    TPAudioCodecTypeSHORTEN = 5015,
    TPAudioCodecTypeALAC = 5016,
    TPAudioCodecTypeWESTWOOD_SND1 = 5017,
    /// as in Berlin toast format
    TPAudioCodecTypeGSM = 5018,
    TPAudioCodecTypeQDM2 = 5019,
    TPAudioCodecTypeCOOK = 5020,
    TPAudioCodecTypeTRUESPEECH = 5021,
    TPAudioCodecTypeTTA = 5022,
    TPAudioCodecTypeSMACKAUDIO = 5023,
    TPAudioCodecTypeQCELP = 5024,
    TPAudioCodecTypeWAVPACK = 5025,
    TPAudioCodecTypeDSICINAUDIO = 5026,
    TPAudioCodecTypeIMC = 5027,
    TPAudioCodecTypeMUSEPACK7 = 5028,
    TPAudioCodecTypeMLP = 5029,
    /// as found in WAV
    TPAudioCodecTypeGSM_MS = 5030,
    TPAudioCodecTypeATRAC3 = 5031,
    TPAudioCodecTypeAPE = 5032,
    TPAudioCodecTypeNELLYMOSER = 5033,
    TPAudioCodecTypeMUSEPACK8 = 5034,
    TPAudioCodecTypeSPEEX = 5035,
    TPAudioCodecTypeWMAVOICE = 5036,
    TPAudioCodecTypeWMAPRO = 5037,
    TPAudioCodecTypeWMALOSSLESS = 5038,
    TPAudioCodecTypeATRAC3P = 5039,
    TPAudioCodecTypeEAC3 = 5040,
    TPAudioCodecTypeSIPR = 5041,
    TPAudioCodecTypeMP1 = 5042,
    TPAudioCodecTypeTWINVQ = 5043,
    TPAudioCodecTypeTRUEHD = 5044,
    TPAudioCodecTypeMP4ALS = 5045,
    TPAudioCodecTypeATRAC1 = 5046,
    TPAudioCodecTypeBINKAUDIO_RDFT = 5047,
    TPAudioCodecTypeBINKAUDIO_DCT = 5048,
    TPAudioCodecTypeAAC_LATM = 5049,
    TPAudioCodecTypeQDMC = 5050,
    TPAudioCodecTypeCELT = 5051,
    TPAudioCodecTypeG723_1 = 5052,
    TPAudioCodecTypeG729 = 5053,
    TPAudioCodecType8SVX_EXP = 5054,
    TPAudioCodecType8SVX_FIB = 5055,
    TPAudioCodecTypeBMV_AUDIO = 5056,
    TPAudioCodecTypeRALF = 5057,
    TPAudioCodecTypeIAC = 5058,
    TPAudioCodecTypeILBC = 5059,
    TPAudioCodecTypeOPUS = 5060,
    TPAudioCodecTypeCOMFORT_NOISE = 5061,
    TPAudioCodecTypeTAK = 5062,
    TPAudioCodecTypeMETASOUND = 5063,
    TPAudioCodecTypePAF_AUDIO = 5064,
    TPAudioCodecTypeON2AVC = 5065,
    TPAudioCodecTypeDSS_SP = 5066,
    TPAudioCodecTypeCODEC2 = 5067,
    
    TPAudioCodecTypeFFWAVESYNTH = 5800,
    TPAudioCodecTypeSONIC = 5801,
    TPAudioCodecTypeSONIC_LS = 5802,
    TPAudioCodecTypeEVRC = 5803,
    TPAudioCodecTypeSMV = 5804,
    TPAudioCodecTypeDSD_LSBF = 5805,
    TPAudioCodecTypeDSD_MSBF = 5806,
    TPAudioCodecTypeDSD_LSBF_PLANAR = 5807,
    TPAudioCodecTypeDSD_MSBF_PLANAR = 5808,
    TPAudioCodecType4GV = 5809,
    TPAudioCodecTypeINTERPLAY_ACM = 5810,
    TPAudioCodecTypeXMA1 = 5811,
    TPAudioCodecTypeXMA2 = 5812,
    TPAudioCodecTypeDST = 5813,
    TPAudioCodecTypeATRAC3AL = 5814,
    TPAudioCodecTypeATRAC3PAL = 5815,
    TPAudioCodecTypeDOLBY_E = 5816,
    TPAudioCodecTypeAPTX = 5817,
    TPAudioCodecTypeAPTX_HD = 5818,
    TPAudioCodecTypeSBC = 5819,
    TPAudioCodecTypeATRAC9 = 5820,
    TPAudioCodecTypeAVS3 = 5821,
};
