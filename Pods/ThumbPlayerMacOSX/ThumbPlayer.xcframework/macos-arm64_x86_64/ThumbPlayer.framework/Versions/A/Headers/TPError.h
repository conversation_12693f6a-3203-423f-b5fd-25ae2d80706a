/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPError.h
 * @brief    错误结构定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/3
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "TPErrorType.h"

/// 非法的播放位置定义
FOUNDATION_EXTERN int64_t const TPErrorInvalidPositionMs;

/// 出错的track index。对应于{@link ITPPlayer#getTrackInfo()}的下标。
/// NSNumber-int类型
FOUNDATION_EXPORT NSString *const TPErrorExtraParamIntTrackIndex;
/// 出错的asset的opaque标识。对应于{@link ITPMediaAsset#TPAssetParamKeyStringOpaque}
/// NSString类型
FOUNDATION_EXPORT NSString *const TPErrorExtraParamStringAssetOpaque;

/// 错误结构定义
@interface TPError : NSObject

/// 错误类型
@property (nonatomic, assign, readonly) TPErrorType errorType;
/// 错误码
@property (nonatomic, assign, readonly) int errorCode;
/// 出错时的播放位置，单位ms. 如果currentPosMs为TPErrorInvalidPositionMs时，表示无效值。
@property (nonatomic, assign, readonly) int64_t currentPosMs;

/// 初始化
/// @param errorCode 错误码
/// @return TPError
- (instancetype)initWithErrorCode:(int)errorCode;

/// 初始化
/// @param errorType 错误类型
/// @param errorCode 错误码
/// @return TPError
- (instancetype)initWithErrorType:(TPErrorType)errorType errorCode:(int)errorCode;

/// 初始化
/// @param errorType 错误类型
/// @param errorCode 错误码
/// @param currentPosMs 错误发生时间
/// @return TPError
- (instancetype)initWithErrorType:(TPErrorType)errorType errorCode:(int)errorCode currentPosMs:(int64_t)currentPosMs NS_DESIGNATED_INITIALIZER;

/// 获取key对应的扩展参数
/// @param key 键值
/// @return 扩展参数
- (id)extraParamForKey:(NSString *)key;

/// 获取所有扩展参数
/// @return 扩展参数
- (NSDictionary<NSString *, id> *)allExtraParams;

/// 添加扩展参数
/// @param extraParam 扩展参数
/// @param key 键值
- (void)addExtraParam:(id)extraParam forKey:(NSString *)key;
@end
