/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPPlayer.h
 * @brief    播放器接口定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/03/10
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPMediaAsset.h"
#import "ITPPlayerDelegate.h"
#import "ITPRichMediaSynchronizer.h"
#import "TPPlayerConstructParams.h"
#import "TPVideoGravity.h"
#import "TPRetCode.h"
#import "TPProgramInfo.h"
#import "TPSeekMode.h"
#import "TPSwitchDataSourceMode.h"
#import "ITPReportExtendedController.h"
#import "ITPBusinessReportManager.h"
#import "ITPPlayerSynchronizer.h"
#import "TPTrackInfo.h"
#import "TPSnapshotParams.h"

NS_ASSUME_NONNULL_BEGIN

/// loopback的start position的默认值，默认从头轮播，在 setLoopback:loopStartPositionMs:loopEndPositionMs: 中使用
FOUNDATION_EXPORT const int64_t TPPlayerLoopbackStartPositionDefaultMs;

/// loopback的end position的默认值，默认从视频尾部轮播，在 setLoopback:loopStartPositionMs:loopEndPositionMs: 中使用
FOUNDATION_EXPORT const int64_t TPPlayerLoopbackEndPositionDefaultMs;

/// 获取播放器字符串属性失败非法值定义，在 ITPPlayer#property: 中使用
FOUNDATION_EXPORT NSString *const TPPlayerInvalidPropertyStringValue;

/// 默认opaque值，比如调用 seekToAsync: 时，外面不传opaque， 则默认外面传的opaque为TPPlayerDefaultOpaque
FOUNDATION_EXPORT const int64_t TPPlayerDefaultOpaque;

/// 获取播放器时长失败的非法值定义，在 ITPPlayer#durationMs 中使用
FOUNDATION_EXPORT const int64_t TPPlayerInvalidDurationValue;

@protocol ITPPlayer <NSObject>
@required

/// 添加播放器可选参数
/// @param optionalParam 可选参数
- (void)addOptionalParam:(TPOptionalParam *)optionalParam;

/// 设置播放delegate，用于监听播放prepared、completion等播放事件
/// @param playerDelegate 见ITPPlayerDelegate定义
- (void)setPlayerDelegate:(nullable id<ITPPlayerDelegate>)playerDelegate;

/// 设置用于视频显示的view
/// @param playerView 用于用于视频显示的view
#if TARGET_OS_OSX
- (void)setPlayerView:(nullable NSView *)playerView;
#else
- (void)setPlayerView:(nullable UIView *)playerView;
#endif

/// 设置播放源，必须在prepareAsync之前设置
/// @param mediaAsset 播放源，是一个继承于ITPMediaAsset的对象，可以是
///                   ITPUrlMediaAsset
///                   ITPMultiMediaAsset
///                   ITPDrmMediaAsset
///                   ITPRtcMediaAsset
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)setDataSource:(id<ITPMediaAsset>)mediaAsset;

/// 添加外挂字幕轨，添加的轨道信息可通过 ITPPlayer#trackInfo 获取
/// @param subtitleAsset 字幕资源。目前仅支持URL asset，后续支持其他asset后再放开限制
/// @param name 该外挂字幕轨名字
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)addSubtitleTrackSource:(id<ITPMediaAsset>)subtitleAsset name:(NSString *)name;

/// 添加外挂音轨，添加的轨道信息可通过 ITPPlayer#trackInfo 获取
/// @param audioAsset 音频资源。目前仅支持URL asset，后续支持其他asset后再放开限制
/// @param name 该外挂音轨名字
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)addAudioTrackSource:(id<ITPMediaAsset>)audioAsset name:(NSString *)name;

/// 移除track
/// @param trackIndex track的索引，需要配合 ITPPlayer#trackInfo 使用，使用其返回的索引作为参数
- (TPRetCode)removeTrack:(int)trackIndex;

/// 选择track，等同于selectTrackAsync:opaque函数的opaque值填写为TPPlayerDefaultOpaque
/// @param trackIndex track的索引，需要配合 ITPPlayer#trackInfo 使用，使用其返回的索引作为参数
- (void)selectTrackAsync:(int)trackIndex;

/// 选择track
/// @param trackIndex track的索引，需要配合 ITPPlayer#trackInfo 使用，使用其返回的索引作为参数
/// @param opaque 调用者自定义的一个值，底层不理解它的含义，在回调TPOnInfoID#TPOnInfoIDLong1SelectTrackSuccess
///               和TPOnInfoID#TPOnInfoIDLong1ObjSeclectTrackError消息时，会把这个值带回给调用者。
- (void)selectTrackAsync:(int)trackIndex opaque:(int64_t)opaque;

/// 取消选择track, 目前仅支持字幕轨，等同于deselectTrackAsync:opaque函数的opaque值填写为TPPlayerDefaultOpaque
/// @param trackIndex track的索引，需要配合 ITPPlayer#trackInfo 使用，使用其返回的索引作为参数
- (void)deselectTrackAsync:(int)trackIndex;

/// 取消选择track, 目前仅支持字幕轨
/// @param trackIndex track的索引，需要配合 ITPPlayer#trackInfo 使用，使用其返回的索引作为参数
/// @param opaque 调用者自定义的一个值，底层不理解它的含义，在回调TPOnInfoID#TPOnInfoIDLong1DeselectTrackSuccess
///               和TPOnInfoID#TPOnInfoIDLong1ObjDeselectTrackError消息时，会把这个值带回给调用者。
- (void)deselectTrackAsync:(int)trackIndex opaque:(int64_t)opaque;

/// 选择一路节目，仅用于HLS，比如选择HLS中某一种码率对应的节目），等同于selectProgramAsync:opaque函数的opaque值填写为TPPlayerDefaultOpaque
/// @param programIndex 要选择的节目的index。该index是指 ITPPlayer#programInfo 返回的TPProgramInfo 的数组下标。
- (void)selectProgramAsync:(int)programIndex;

/// 选择一路节目，仅用于HLS，比如选择HLS中某一种码率对应的节目）
/// @param programIndex 要选择的节目的index。该index是指 ITPPlayer#programInfo 返回的TPProgramInfo 的数组下标。
/// @param opaque 调用者自定义的一个值，底层不理解它的含义，在回调 TPOnInfoID#TPOnInfoIDLong1SelectProgramSuccess
///               和 TPOnInfoID#TPOnInfoIDLong1ObjSelectProgramError 消息时，会把这个值带回给调用者。
- (void)selectProgramAsync:(int)programIndex opaque:(int64_t)opaque;

/// 返回节目信息，用于hls嵌套m3u8的情况，嵌套m3u8可以包含多路视频轨、多路音频轨和多路字幕轨，每路视频轨可以有各自的分辨率、带宽等，
/// 而音频轨可以是不同语言的音频，字幕轨可以是不同语言的字幕。一个节目是一个“一路视频轨+多路音频轨+多路字幕轨”的组合，即stream变体。
/// 请参考 https://www.rfc-editor.org/rfc/rfc8216.html#page-25。
/// 该方法返回所有的节目信息，请注意与 ITPPlayer#trackInfo 的不同。
/// 返回的节目可以通过 ITPPlayer#selectProgramAsync:opaque: 进行选择
/// @return 当前所有的节目的信息
- (nullable NSArray<TPProgramInfo *> *)programInfo;

/// prepare播放器，异步调用，需要等到prepared回调之后才能start
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)prepareAsync;

/// 启动播放器，视频prepared之后才能调用
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)start;

/// 暂停播放器，该方法只暂停播放，但不会暂停数据的下载，如果在pause时也要暂停数据的下载，请调用 ITPPlayer#pauseDownload
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)pause;

/// 同步停止播放器
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)stop;

/// 异步停止播放器
/// 播放器的stop会做一些资源释放，如果在UI线程调用同步stop接口，偶尔会卡一下UI线程。
/// 而此接口是在异步线程中进行释放，不会卡住调用线程。但是后续调用上与同步stop不同，
/// 在 ITPPlayerDelegate#onStopAsyncCompleteWithPlayer:回调消息通知之后，
/// 再继续调用其他接口，比如reset等接口。
- (void)stopAsync;

/// reset播放器
- (void)reset;

/// release播放器，release之后播放器不能再使用
- (void)releasePlayer;

/// seek到指定的时间点，等同于seekToAsync:opaque:函数的opaque值填写为TPPlayerDefaultOpaque
/// @param positionMs seek的position，单位毫秒
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)seekToAsync:(int64_t)positionMs;

/// seek到指定的时间点，等同于seekToAsync:mode:opaque:函数的mode值填写为TPSeekModeDefault
/// @param positionMs seek的position，单位毫秒
/// @param opaque 调用者自定义的一个值，底层不理解它的含义，
///               在回调 ITPPlayerDelegate#onPlayer:seekCompleteWithOpaque: 消息时，
///               会把这个值带回给调用者。
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)seekToAsync:(int64_t)positionMs opaque:(int64_t)opaque;

/// seek到指定的时间点，等同于seekToAsync:mode:opaque:方法的opaque填写为TPPlayerDefaultOpaque
/// @param positionMs seek的position，单位毫秒
/// @param mode seek的模式，参考TPSeekMode
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)seekToAsync:(int64_t)positionMs mode:(TPSeekMode)mode;

/// seek到指定的时间点
/// @param positionMs seek的position，单位毫秒
/// @param mode seek的模式，参考TPSeekMode
/// @param opaque 调用者自定义的一个值，底层不理解它的含义，
///               在回调 ITPPlayerDelegate#onPlayer:seekCompleteWithOpaque: 消息时，
///               会把这个值带回给调用者。
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)seekToAsync:(int64_t)positionMs mode:(TPSeekMode)mode opaque:(int64_t)opaque;

/// 设置静音模式
/// @param isMute 是否静音
- (void)setAudioMute:(BOOL)isMute;

/// 设置音量
/// @param volume 音量的大小值，默认值为1.0
- (void)setAudioVolume:(float)volume;

/// 设置音量均衡参数
/// @param params 音量均衡参数 缺省为关闭状态，各参数值为0.0f
///        case0：如果要开启音量均衡并且下发参数，params可以为这样：
///               "enable,loudnorm=I=-24.0:TP=-1.5:LRA=11.0:measured_I=-11.4:measured_TP=-1.2:measured_LRA=0.5
///                :measured_thresh=-21.4:offset=-0.1:linear=true"
///        case1：如果播放过程中要修改所有参数，params可以为这样：
///               "loudnorm=I=-30.0:TP=-1.5:LRA=11.0:measured_I=-12.0:measured_TP=-1.2:measured_LRA=0.5
///                :measured_thresh=-21.4:offset=-0.1:linear=true"
///        case2：如果播放过程中要修改某一参数，params可以为这样："loudnorm=I=-30.0"
///        case3: 如果播放过程中要关闭音量均衡，params可以为这样:"disable"，但是这种情况下在整个播放过程中内部各浮点参数还会留存
///        case4: 如果在case3情况下要恢复开启音量均衡，params只需要为这样："enable"，参数依旧使用case3留存的那些
- (void)setAudioNormalizeVolumeParams:(NSString *)params;

/// 设置播放速度
/// @param speedRatio 播放速度
- (void)setPlaySpeedRatio:(float)speedRatio;

/// 返回播放器当前可支持的最大播放倍速，内部会根据播放器类型、机型来做判断
- (float)maxPlaySpeedRatio;

/// 设置循环播放
/// @param isLoopback 是否循环播放，如果设置为true，默认播放到视频结尾之后又开始从0开始播放。此接口会忽略跳过片头片尾的设置
- (void)setLoopback:(BOOL)isLoopback;

/// 设置循环播放，可以指定循环播放的区间
/// @param isLoopback 是否循环播放
/// @param loopStartPositionMs 需要循环播放的起始点，如果是从头开始可以填写 TPPlayerLoopbackStartPositionDefaultMs
/// @param loopEndPositionMs 需要循环播放的结束点，如果是从尾部轮播可以填写 TPPlayerLoopbackEndPositionDefaultMs，
///                          也可以填写视频的duration
- (void)setLoopback:(BOOL)isLoopback
loopStartPositionMs:(int64_t)loopStartPositionMs
  loopEndPositionMs:(int64_t)loopEndPositionMs;

/// 设置视频gravity
/// @param gravity 见TPVideoGravity定义
- (void)setVideoGravity:(TPVideoGravity)gravity;

/// 切换播放源，即切换同一个视频的不同源，比如同一个视频，从一个分辨率切换到另一个分辨率。
/// 该方法等同于switchDataSourceAsync:opaque:方法的opaque值填写TPPlayerDefaultOpaque
/// @param mediaAsset 播放源
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)switchDataSourceAsync:(id<ITPMediaAsset>)mediaAsset;

/// 切换播放源，即切换同一个视频的不同源，比如同一个视频，从一个分辨率切换到另一个分辨率。
/// 该方法等同于switchDataSourceAsync:mode:opaque:方法的mode值填写TPSwitchDataSourceModeDefault
/// @param mediaAsset 播放源
/// @param opaque 调用者自定义的一个值，底层不理解它的含义，在回调TPOnInfoID#TPOnInfoIDLong1SwitchDataSourceSuccess
///               消息时，会把这个值带回给调用者。
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)switchDataSourceAsync:(id<ITPMediaAsset>)mediaAsset opaque:(int64_t)opaque;

/// 切换播放源，即切换同一个视频的不同源，比如同一个视频，从一个分辨率切换到另一个分辨率。
/// 该方法等同于switchDataSourceAsync:mode:opaque:方法的opaque值填写TPPlayerDefaultOpaque
/// @param mediaAsset 播放源
/// @param mode 换源模式
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)switchDataSourceAsync:(id<ITPMediaAsset>)mediaAsset mode:(TPSwitchDataSourceMode)mode;

/// 切换播放源，即切换同一个视频的不同源，比如同一个视频，从一个分辨率切换到另一个分辨率。
/// @param mediaAsset 播放源
/// @param mode 换源模式
/// @param opaque 调用者自定义的一个值，底层不理解它的含义，在回调TPOnInfoID#TPOnInfoIDLong1SwitchDataSourceSuccess消息时，会把这个值带回给调用者。
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)switchDataSourceAsync:(id<ITPMediaAsset>)mediaAsset
                              mode:(TPSwitchDataSourceMode)mode
                            opaque:(int64_t)opaque;

/// 切换播放源同时选择轨道
/// @param mediaAsset 播放源
/// @param mode 换源模式
/// @param tracksIndex 待选择的轨道下标
/// @param opaque 调用者自定义的一个值，底层不理解它的含义，在回调TPOnInfoID#TPOnInfoIDLong1SwitchDataSourceSuccess消息时，会把这个值带回给调用者。
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)switchDataSourceAsync:(id<ITPMediaAsset>)mediaAsset
                              mode:(TPSwitchDataSourceMode)mode
                    andSelectTrack:(NSArray<NSNumber *> *)tracksIndex
                            opaque:(int64_t)opaque;

/// 截取当前播放位置的视频帧，异步回调结果，等同于snapshotAsyncWithParams:opaque函数的opaque值填写为TPPlayerDefaultOpaque
/// @param params 截图参数
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)snapshotAsyncWithParams:(TPSnapshotParams *)params;

/// 截取当前播放位置的视频帧，异步回调结果
/// @param params 截图参数
/// @param opaque 调用者自定义的一个值，底层不理解它的含义，
///               在回调 ITPPlayerDelegate#onPlayer:snapshotSuccessWithOpaque:frameBuffer: 消息时，会把这个值带回给调用者。
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码
- (TPRetCode)snapshotAsyncWithParams:(TPSnapshotParams *)params
                              opaque:(int64_t)opaque;

/// 获取播放器属性
/// @param propertyID 属性ID. 参考 TPPropertyID
/// @return propertyID对应的参数值.如果获取失败，将返回 TPPlayerInvalidPropertyStringValue
- (NSString *)property:(NSString *)propertyID;

/// 获取视频的时长.如果获取失败，将返回非法值 TPPlayerInvalidDurationValue
/// @return 返回视频时长
- (int64_t)durationMs;

/// 获取当前播放的位置
/// @return 播放位置，单位为毫秒
- (int64_t)currentPositionMs;

/// 获取当前播放的状态，注意该接口可能比较耗时
/// @return 播放器的状态
- (TPPlayerState)currentState;

/// 获取播放器当前缓存数据可播放到的位置。单位毫秒。
/// 例如，当前播放位置是5000ms，而播放器已缓冲到10000ms，则该方法返回10000ms，即可以继续从5000ms播放到10000ms。
/// 注意:在使用下载组件的时候，MP4等格式文件，此数值是根据码率、下载后的文件长度以及总文件长度进行估算所得，所以会有一些偏差.
/// @return 可播位置。单位为毫秒
- (int64_t)availablePositionMs;

/// 获取视频宽
/// @return 视频宽
- (int)width;

/// 获取视频高
/// @return 视频高
- (int)height;

/// 返回轨道信息，包括外部添加的轨道和播放源中内嵌的轨道。
/// 对于音频轨和字幕轨，可以通过 ITPPlayer#selectTrackAsync:opaque，通过deselectTrackAsync:opaque反选。
/// 如果有多路视频轨，比如hls嵌套m3u8的情况（见 ITPPlayer#programInfo），该方法只返回当前播放的视频轨道，且不可select和deselect。
/// 注意：请不要存储返回信息，每次需要信息时，直接调用此接口，因为内部发生重试等逻辑时，TrackInfo可能会发生变更
/// @return TrackInfo 轨道数组
- (nullable NSArray<TPTrackInfo *> *)trackInfo;

/// 暂停数据下载，该方法与pause方法不同，pause只是暂停播放，该方法只暂停数据的下载
/// pauseDownload之后，若要继续数据下载，须调用resumeDownload
/// @return 成功返回TPRetCodeOk，否则返回相应的错误码, 如果prepared之前状态，需要返回对应的错误码
- (TPRetCode)pauseDownload;

/// 恢复数据下载
- (void)resumeDownload;

/// 返回当前播放内核类型
/// @return 播放内核类型
- (TPPlayerCoreType)playerCoreType;

/// 设置富媒体同步器. 播放器任何状态下都可以设置此实例.
/// @param synchronizer 富媒体同步器实例
- (void)setRichMediaSynchronizer:(nullable id<ITPRichMediaSynchronizer>)synchronizer;

/// 返回业务侧数据上报的接口
/// 若未开启数据上报功能（默认开启），则返回nil，参考 TPMgrConfig#TPMgrConfigKeyGlobalBoolEnablePlayingQualityReport
/// @return 数据上报的接口
- (nullable id<ITPBusinessReportManager>)reportManager;

/// 获取播放器数据上报控制模块，与上面ITPBusinessReportManager不同，该数据上报是新的质量上报，与老的质量上报并存，但稳定性还有待验证，
/// 等稳定后代替老的质量上报。
/// 业务方可通过该模块实现如下功能：
/// 1、获取播放器数据上报所有的数据
/// 2、向上报数据中添加业务方自定义的字段及数据
///
/// 若未开启新数据上报功能（默认关闭），则返回nil。
/// 新数据上报模块开启方法：通过TPMgr#addOptionalParam设置TPMgrConfigKeyGlobalBoolEnableNewPlayingQualityReport为YES
/// @return 数据上报控制模块
- (nullable id<ITPReportExtendedController>)reportExtendedController;

/// 获取播放器的同步器
/// @return 同步器
- (id<ITPPlayerSynchronizer>)synchronizer;

/// 设置log tag前缀，实时生效。
/// 建议在idle状态设置，不要在播放过程中设置，比如构造完播放器实例之后立刻设置，或者在复用同一个播放器实例情况下，
/// 调用reset之后，启动一次新的播放前，调用方如想更新log tag前缀，可调用该方法更新log tag前缀。
/// @param logTagPrefix 日志前缀
- (void)setLogTagPrefix:(NSString *)logTagPrefix;

#pragma mark - AirPlay
/// 设置airplay的delegate，用于监听airplay状态等，详细见ITPPlayerAirplayDelegate
/// @param airplayDelegate 见ITPPlayerAirplayDelegate定义
- (void)setAirplayDelegate:(nullable id<ITPPlayerAirplayDelegate>)airplayDelegate;

/// 获取AirPlay是否激活
/// @return AirPlay是否激活
- (BOOL)isExternalPlaybackActive;

/// 设置是否允许AirPlay投射，默认为YES
/// @param allowsExternalPlayback 是否允许AirPlay投射
- (void)setAllowsExternalPlayback:(BOOL)allowsExternalPlayback;

/// 设置AirPlay镜像是否使用AirPlay投射，默认为YES
/// @param use AirPlay镜像是否使用AirPlay投射
- (void)setUsesExternalPlaybackWhileExternalScreenIsActive:(BOOL)use;

/// 投射播放时图像的拉伸方式。默认：TPVideoGravityResizeAspectFit
/// @param gravity 见TPVideoGravity定义
- (void)setExternalPlaybackVideoGravity:(TPVideoGravity)gravity;

#pragma mark - PIP
/// 设置画中画的delegate，用于监听画中画状态等，详细见ITPPlayerPictureInPictureDelegate
/// @param pictureInPictureDelegate 见ITPPlayerPictureInPictureDelegate定义
- (void)setPictureInPictureDelegate:(nullable id<ITPPlayerPictureInPictureDelegate>)pictureInPictureDelegate;

/// 是否可开启画中画
/// 只有播放内核为TPPlayerCoreTypeSystemAVPlayer或TPPlayerCoreTypeSystemAVQueuePlayer时，才支持画中画功能，
/// 通过initWithPlayingQueue:delegateQueue:constructParams:构造方法可设置播放内核类型
/// @return 可开启则返回YES，否则返回NO
- (BOOL)isPictureInPicturePossible;

/// 画中画是否激活
/// 画中画开启中则返回YES，否则返回NO
- (BOOL)isPictureInPictureActive;

/// 启动画中画，要启用画中画功能，在创建播放器时，传入的播放内核类型须为TPPlayerCoreTypeSystemAVPlayer或TPPlayerCoreTypeSystemAVQueuePlayer
- (void)startPictureInPicture;

/// 停止画中画
- (void)stopPictureInPicture;

@end

NS_ASSUME_NONNULL_END

