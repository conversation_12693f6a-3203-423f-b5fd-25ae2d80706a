/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPRichMediaAsyncRequester.h
 * @brief    富媒体请求的接口定义.
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPRichMediaData.h"
#import "TPRetCode.h"
#import "ITPRichMediaAsyncRequesterDelegate.h"
#import "TPTimeRange.h"
#import "ITPUrlMediaAsset.h"
#import "TPRichMediaFeature.h"

NS_ASSUME_NONNULL_BEGIN

///富媒体请求的接口定义
///此接口主要是面向类似明星识别这样的功能，不需要和播放器的播放时间同步，
///直接通过此类的接口获取指定时间或者指定时间区间的富媒体数据。
///使用方法：
/// 1、通过delegate设置监听器
/// 2、使用setRichMediaSource设置富媒体请求数据源
/// 3、使用prepareAsync进行富媒体功能类型加载
/// 4、等待接收ITPRichMediaAsyncRequesterDelegate的onRequesterPrepared()回调
/// 5、使用features获取加载的富媒体功能列表
/// 6、使用requestDataAsyncAtPositionMs等数据请求接口获取数据
@protocol ITPRichMediaAsyncRequester <NSObject>

/// 设置富媒体异步请求监听器
/// @param delegate 富媒体异步请求监听器
- (void)setDelegate:(nullable id<ITPRichMediaAsyncRequesterDelegate>)delegate;

/// 设置富媒体异步请求的数据源，必须在prepareAsync前设置
/// @param urlMediaAsset 请求数据源
/// @return 设置是否成功，见TPRetCode定义
- (TPRetCode)setRichMediaSource:(id<ITPUrlMediaAsset>)urlMediaAsset;

/// 使用setRichMediaSource后，进行功能加载等准备工作，
/// 成功后会通过ITPRichMediaAsyncRequesterDelegate的onRequesterPrepared通知
/// @return 调用结果，见TPRetCode定义
- (TPRetCode)prepareAsync;

/// 收到onRequesterPrepared后，可通过该接口获取富媒体资源中的所有功能
/// @return 富媒体资源中的功能列表
- (NSArray<TPRichMediaFeature *> *)features;

/// 请求指定富媒体功能的指定时间点的内容，请求成功的数据回调
/// ITPRichMediaAsyncRequesterDelegate的onRequestDataSuccess:requestID:featureIndex:data
/// 请求失败的通知
/// ITPRichMediaAsyncRequesterDelegate的onRequestDataError:requestID:featureIndex:errorCode
/// @param featureIndex 富媒体功能索引，来自于features返回功能列表的下标索引
/// @param positionMs 请求对应视频流中的时间点位(毫秒)
/// @return 请求ID，用于唯一标识此次请求。如果返回负值，表示请求失败。
- (int)requestDataAsync:(int)featureIndex atPositionMs:(int64_t)positionMs;

/// 请求指定富媒体功能多个时间点的内容,请求成功的数据回调
/// ITPRichMediaAsyncRequesterDelegate的onRequestDataSuccess:requestID:featureIndex:data
/// 请求失败的通知
/// ITPRichMediaAsyncRequesterDelegate的onRequestDataError:requestID:featureIndex:errorCode
/// @param featureIndex 富媒体功能索引，来自于features返回功能列表的下标索引
/// @param positionMsArray 请求对应视频流中的时间点位数组(毫秒)
/// @return 请求ID，用于唯一标识此次请求。如果返回负值，表示请求失败。
- (int)requestDataAsync:(int)featureIndex atPositionMsArray:(NSArray<NSNumber *> *)positionMsArray;

/// 请求指定富媒体功能的指定时间区间的内容，请求成功的数据回调
/// ITPRichMediaAsyncRequesterDelegate的onRequestDataSuccess:requestID:featureIndex:data
/// 请求失败的通知
/// ITPRichMediaAsyncRequesterDelegate的onRequestDataError:requestID:featureIndex:errorCode
/// @param featureIndex 富媒体功能索引，来自于features返回功能列表的下标索引
/// @param timeRange 请求对应视频流中的时间区间(毫秒)
/// @return 请求ID，用于唯一标识此次请求。如果返回负值，表示请求失败。
- (int)requestDataAsync:(int)featureIndex atTimeRange:(TPTimeRange *)timeRange;

/// 请求指定富媒体功能的多个时间区间的内容，请求成功的数据回调
/// ITPRichMediaAsyncRequesterDelegate的onRequestDataSuccess:requestID:featureIndex:data
/// 请求失败的通知
/// ITPRichMediaAsyncRequesterDelegate的onRequestDataError:requestID:featureIndex:errorCode
/// @param featureIndex 富媒体功能索引，来自于features返回功能列表的下标索引
/// @param timeRangeArray 时间区间数组，毫秒
/// @return 请求ID，用于唯一标识此次请求。如果返回负值，表示请求失败。
- (int)requestDataAsync:(int)featureIndex atTimeRangeArray:(NSArray<TPTimeRange *> *)timeRangeArray;

/// 取消指定请求ID的请求
/// 取消请求后，不再有对应的onRequest回调
///
/// @param requestID 请求ID，以下几个函数的返回值
///                   requestDataAsync:atPositionMs
///                   requestDataAsync:atPositionMsArray
///                   requestDataAsync:atTimeRange
///                   requestDataAsync:atTimeRangeArray
- (void)cancelRequest:(int)requestID;

/// 释放本对象资源，释放后本对象将不能再继续使用
- (void)releaseRequester;

@end

NS_ASSUME_NONNULL_END
