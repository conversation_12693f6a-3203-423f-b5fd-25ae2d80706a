/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TPSpatialAudioRenderingCapability.h
 * @brief    空间音频渲染能力的对象
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/4/23
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPSpatialAudioRenderingMode.h"
#import "TPAudioRouteType.h"

/// 空间音频能力支持情况定义
/// 注意：这些值的定义值不要改动，此值和内核层的值是一一对应关系
typedef NS_ENUM(NSInteger, TPSpatialAudioCap) {
    /// 不支持
    TPSpatialAudioCapNotSupport = 0,
    /// 支持
    TPSpatialAudioCapSupport = 1,
};


/// 空间音频渲染能力
@interface TPSpatialAudioRenderingCapability : NSObject

/// 添加定制化空间音频渲染的能力
/// @param outRouteType 具体输出设备类型
/// @param renderingMode 指定的渲染器模式 参考TPSpatialAudioRenderingMode
/// @param decoderCap 自定义的空间音频渲染能力
+ (void)addCustomizedCapability:(TPAudioRouteType)outRouteType
                  renderingType:(TPSpatialAudioRenderingMode)renderingMode
                spatialAudioCap:(TPSpatialAudioCap)cap;

@end
