/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPBufferStrategy.h
 * @brief    demuxer缓冲策略枚举定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/27
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// demuxer缓冲策略枚举定义，与内核保持一致，不能随意修改
typedef NS_ENUM(NSInteger, TPBufferStrategy) {
    /// 自动模式，由内部自己决策
    TPBufferStrategyAuto     = -1,
    /// 禁用缓冲
    TPBufferStrategyDisabled = 0,
    /// 固定缓冲区大小
    TPBufferStrategyNormal   = 1,
    /// 动态修改缓冲区大小
    TPBufferStrategyJitter   = 2,
};
