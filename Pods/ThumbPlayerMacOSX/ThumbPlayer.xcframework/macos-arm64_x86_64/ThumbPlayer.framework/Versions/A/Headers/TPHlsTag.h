/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPHlsTag.h
 * @brief    Hls封装格式信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/1
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITPContainerInfo.h"

NS_ASSUME_NONNULL_BEGIN

/// Hls封装格式信息
@interface TPHlsTag : NSObject <ITPContainerInfo>

/// 轨道名称，对应HLS master playlist audio/subtitle内字段NAME, 用于唯一标识音轨/字幕
@property (nonatomic, copy) NSString *name;

/// 轨道语言，对应HLS master playlist内audio/subtitle的字段LANGUAGE, 用于音轨/字幕选择
@property (nonatomic, copy) NSString *language;

/// 轨道组id，对应HLS master playlist内audio/subtitle的字段GROUP-ID, 用于音轨/字幕匹配不同视频
@property (nonatomic, copy) NSString *groupID;

/// 轨道带宽，对应HLS master playlist内video的字段BANDWIDTH, 标识视频码率信息, 匹配相应的网络带宽
@property (nonatomic, assign) int64_t bandwidth;

/// 轨道分辨率(用于HLS master playlist video对应字段RESOLUTION), 标识视频分辨率
@property (nonatomic, copy) NSString *resolution;

/// 轨道帧率，对应HLS master playlist内video的字段FRAME-RATE, 标识视频帧率
@property (nonatomic, assign) float frameRate;

/// 轨道编解码信息，对应HLS master playlist内video的字段CODECS, 标识视频编解码信息
@property (nonatomic, copy) NSString *codecs;

@end

NS_ASSUME_NONNULL_END
