/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPLocalProxyFactory.h
 * @brief    构建媒体资源的本地代理的工厂类
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPLocalProxy.h"

/// 构建媒体资源的本地代理的工厂类
@interface TPLocalProxyFactory : NSObject

/// @brief 创建媒体资源的本地代理实例
/// @return 本地代理实例
+ (id<ITPLocalProxy>)createTPLocalProxy;

@end

