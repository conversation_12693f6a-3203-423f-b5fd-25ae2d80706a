/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPMultiMediaAsset.h
 * @brief    组合型media asset，用于将多个media asset拼接在一起的情况，比如多个分片mp4
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/21
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITPMediaAsset.h"

NS_ASSUME_NONNULL_BEGIN

@protocol ITPMultiMediaAsset <ITPMediaAsset>

/// 添加一个asset，内部按添加的顺序排列。
/// @param asset           目前只支持ITPUrlMediaAsset
/// @param clipDurationMs  分片播放时长
/// @return YES:添加成功 NO:添加失败
-(BOOL)addAsset:(id<ITPMediaAsset>)asset clipDurationMs:(int64_t)clipDurationMs;

@end

NS_ASSUME_NONNULL_END
