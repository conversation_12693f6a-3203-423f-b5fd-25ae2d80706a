/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPRichMediaSynchronizer.h
 * @brief    富媒体同步器接口定义。
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPRichMediaData.h"
#import "TPRetCode.h"
#import "ITPRichMediaSynchronizerDelegate.h"
#import "ITPUrlMediaAsset.h"
#import "TPRichMediaFeature.h"
#import "TPRichMediaOptParam.h"

NS_ASSUME_NONNULL_BEGIN

/// 富媒体同步器接口定义。
/// 主要面向智能倍速等富媒体功能，通过ITPPlayer的setRichMediaSynchronizer:
/// 将富媒体同步器设置给播放器，富媒体内容将与播放器同步地、持续性地回抛出数据。
/// 使用方法：
/// 1、设置富媒体同步器的监听器
/// 2、通过setRichMediaSource:设置富媒体数据源
/// 3、通过prepareAsync进行富媒体功能类型加载
/// 4、等待接收onRichMediaPrepared:回调
/// 5、使用features获取加载的富媒体功能列表
/// 6、使用selectFeatureAsync:optParam:选择富媒体功能类型
/// 7、通过onRichMedia:featureIndex:data:接收与播放器同步的富媒体数据回调
@protocol ITPRichMediaSynchronizer <NSObject>

/// 设置富媒同步器的监听器
/// @param delegate 富媒体同步器的监听器
- (void)setDelegate:(nullable id<ITPRichMediaSynchronizerDelegate>)delegate;

/// 设置富媒体的数据源，必须在{@link #prepareAsync()}前设置
/// @param urlMediaAsset 数据源
/// @return 调用结果，见TPRetCode定义
- (TPRetCode)setRichMediaSource:(id<ITPUrlMediaAsset>)urlMediaAsset;

/// 使用setRichMediaSource后，进行功能加载等准备工作，成功后会通过ITPRichMediaSynchronizerDelegate的onRichMediaPrepared:通知
/// @return 调用结果，见TPRetCode定义
- (TPRetCode)prepareAsync;

/// 收到ITPRichMediaSynchronizerDelegate的onRichMediaPrepared:后，可通过该接口获取富媒体资源中的所有功能类型
/// @return 富媒体资源中的功能类型列表
- (nullable NSArray<TPRichMediaFeature *> *)features;

/// 选择富媒体功能类型
/// 选择后，该富媒体功能类型的数据将会通过下面接口与播放器同步地持续地回抛
/// ITPRichMediaSynchronizerDelegate的onRichMedia:featureIndex:data:
/// @param featureIndex 选择的富媒体功能类型的索引，参考features
/// @param optParam 富媒体可选参数
/// @return 调用结果，见TPRetCode定义
- (TPRetCode)selectFeatureAsync:(int)featureIndex optParam:(TPRichMediaOptParam *)optParam;

/// 取消选择富媒体功能类型
/// 取消选择后，该富媒体功能类型的数据将不再回抛
/// @param featureIndex 取消选择的富媒体功能类型的索引，参考features
/// @return 调用结果，见TPRetCode定义
- (TPRetCode)deselectFeatureAsync:(int)featureIndex;

/// 重置富媒体同步器
/// 重置后，对象和重新创建一个对象一样
/// @return 调用结果，见TPRetCode定义
- (TPRetCode)reset;

/// 释放本对象资源，释放后本对象将不能再继续使用
- (void)releaseSynchronizer;

@end

NS_ASSUME_NONNULL_END
