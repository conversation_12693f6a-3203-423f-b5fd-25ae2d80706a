/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPVideoCropInfo.h
 * @brief    video裁剪信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/27
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// video裁剪信息
///                         mWidth
/////////////////////////////////////////////////////////////////
///                             -                       -       /
///                             |                       |       /
///                          mCropTop                   |       /
///                             |                       |       /
///                             -                  mCropBottom  /
///                 //////////////////////////////      |       /
///                 /                            /      |       /
///                 /       CROP RECTANGLE       /      |       / mHeight
///                 /                            /      |       /
///|-- mCropLeft --|//////////////////////////////      -       /
///                                                             /
///|-------------- mCropRight -------------------|              /
///                                                             /
/////////////////////////////////////////////////////////////////
///
@interface TPVideoCropInfo : NSObject
/// 视频帧的宽
@property (nonatomic, assign) int width;
/// 视频帧的高
@property (nonatomic, assign) int height;
/// 裁剪区域上下边界 与 视频帧上边界的距离
@property (nonatomic, assign) int cropLeft;
/// 裁剪区域上下边界 与 视频帧下边界的距离
@property (nonatomic, assign) int cropRight;
/// 裁剪区域左右边界 与 视频帧左边界的距离
@property (nonatomic, assign) int cropTop;
/// 裁剪区域左右边界 与 视频帧右边界的距离
@property (nonatomic, assign) int cropBottom;

@end

NS_ASSUME_NONNULL_END
