/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPPlayerConnectionNode.h
 * @brief    播放器连接中的节点接口,作用于ITPPlayerConnection,当做一个连接的节点
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#import "ITPPlayer.h"

NS_ASSUME_NONNULL_BEGIN

/// 播放器连接中的节点接口,作用于ITPPlayerConnection,当做一个连接的节点
@protocol ITPPlayerConnectionNode

/// 节点参数的约束定义
typedef NS_ENUM(NSInteger, TPConnectionConfigType) {
    /// 节点参数定义：配置同步时钟的offset值，单位ms
    /// @see setConfig:value方法来配置
    TPConnectionConfigTypeSyncClockOffsetMs = 0,
};

/// 获取节点中的播放器，创建节点的时候必须传入
/// @return 节点中的播放器
- (id<ITPPlayer>)player;

/// 设置节点的配置参数
/// @param cfgType 参数名称
/// @param value 参数值
- (void)setConfig:(TPConnectionConfigType)cfgType value:(int64_t)value;

/// 获取节点的配置参数
/// @param cfgType 参数名称
/// @return 参数值
- (int64_t)configValueWith:(TPConnectionConfigType)cfgType;

@end

NS_ASSUME_NONNULL_END
