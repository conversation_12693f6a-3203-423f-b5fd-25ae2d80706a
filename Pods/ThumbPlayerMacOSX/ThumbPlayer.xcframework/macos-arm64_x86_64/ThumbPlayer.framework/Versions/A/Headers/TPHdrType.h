/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPHdrType.h
 * @brief    HDR类型定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/20
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// HDR类型定义. 与内核层(tp_hdr_type.h)枚举值保持一致，不要改动
typedef NS_ENUM(NSInteger, TPHdrType) {
    TPHdrTypeNone             = -1,
    /// HDR10
    TPHdrTypeHDR10            = 0,
    /// HDR10Plus
    TPHdrTypeHDR10Plus        = 1,
    /// HDR DolbyVision
    TPHdrTypeDolbyVision      = 2,
    /// HDR HLG
    TPHdrTypeHLG              = 3,
    /// HDR Vivid(CUVA)
    TPHdrTypeHDRVivid         = 4,
};
