/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPAudioCapabilityQueryParams.h
 * @brief    音频能力查询参数
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/11/14
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

#import "TPChannelLayout.h"
#import "TPAudioCodecType.h"
#import "TPAudioDecoderType.h"

/// 音频能力查询参数
/// hoaOrder不为0时，参数仅HOA阶数，采样率有效。
/// hoaOrder为0时，参数仅声道布局，采样率，对象数有效。若无对象可不用设置。
@interface TPAudioCapabilityQueryParams : NSObject

/// codec类型
@property (nonatomic, assign) TPAudioCodecType codecType;
/// 解码器类型
@property (nonatomic, assign) TPAudioDecoderType decoderType;
/// 音频的channel layout，见 TPChannelLayout.h 中的定义
@property (nonatomic, assign) TPChannelLayout channelLayout;
/// 采样率
@property (nonatomic, assign) int sampleRate;
/// 对象数量
@property (nonatomic, assign) int objectCount;
/// hoa阶数
@property (nonatomic, assign) int hoaOrder;

@end
