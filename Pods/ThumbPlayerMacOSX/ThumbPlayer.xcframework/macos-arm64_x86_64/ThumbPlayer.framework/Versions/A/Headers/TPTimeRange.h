/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPTimeRange.h
 * @brief    时间区间定义，用于指定媒体流的某一段时间
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/27
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 时间区间定义，用于指定媒体流的某一段时间
@interface TPTimeRange : NSObject

/// 开始时间，单位为毫秒
@property (nonatomic, assign) int64_t startTimeMs;

/// 结束时间，单位为毫秒，值为-1则表示直到媒体流末尾
@property (nonatomic, assign) int64_t endTimeMs;

@end

NS_ASSUME_NONNULL_END
