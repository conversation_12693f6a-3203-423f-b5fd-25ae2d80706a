/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPReduceLiveLatencyStrategy.h
 * @brief    降低直播延迟策略定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 直播延迟策略reduce live latency strategy类型
typedef NS_ENUM(NSInteger, TPReduceLiveLatencyStrategy) {
    /// 不采取任何措施
    TPReduceLiveLatencyStrategyNone      = 0,
    /// 通过加速播放来降低延迟
    TPReduceLiveLatencyStrategySpeedUp   = 1,
    /// 通过丢帧来降低延迟
    TPReduceLiveLatencyStrategySkipFrame = 2,
};
