/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPAudioAVSyncStrategy.h
 * @brief    音频同步策略枚举定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/27
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 音频同步策略枚举定义，与内核层数值定义一致
typedef NS_ENUM(NSInteger, TPAudioAVSyncStrategy) {
    /// 默认模式, 同步sleep
    TPAudioAVSyncStrategyNormal  = 0,
    /// 持续FreeRun模式, audio不做sleep, 但其pts参与同步更新clock
    TPAudioAVSyncStrategyFreeRun = 1,
    /// 自动调整模式, 根据音频缓存数据大小来决定采用何种策略。
    /// 当音频渲染缓存区内数据小于最小缓存阈值（即低水位阈值，各渲染器最小缓存阈值不一样，内部设定，外面不可配）时,
    /// 走FreeRun模式，audio不做sleep，其pts参与更新clock。
    /// 当音频渲染缓存区内数据大于等于最小缓存阈值时，走默认模式，同步sleep
    TPAudioAVSyncStrategyAdaptive = 2,
};
