/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPThreadPriority.h
 * @brief    TPPlayer中线程优先级档位定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/28
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 线程优先级枚举定义，跟内核的TPThreadPriority枚举值保持一致，方便直接转换到内核的TPThreadPriority枚举值
/// 该值非实际Apple端线程优先级，但内部使用时最终会转换成TPAppleThreadUtils中的AppleThreadPriority值
/// AppleThreadPriority优先级是根据实测，Apple thread是遵循了POSIX线程标准，POSIX线程优先级从[1，99]
/// 注释中有每个枚举对应的AppleThreadPriority中的值
typedef NS_ENUM(NSInteger, TPThreadPriority) {
    TPThreadPriorityDefault = -1,
    /// 对应AppleThreadPriority中kThreadPriorityLowest = 1
    TPThreadPriorityLowest = 0,
    /// 对应AppleThreadPriority中kThreadPriorityBackground = 4，参考的NSQualityOfService.NSQualityOfServiceBackground, POSIX接口读到是4
    TPThreadPriorityBackground = 9,
    /// 播放器线程前台优先级，对应AppleThreadPriority中kThreadPriorityForeground = 34，比Apple OS创建的默认优先级31(0.5),要稍大一些，34(0.55)
    TPThreadPriorityForeground = 21,
    /// 播放器display线程优先级, 比如非紧要的toast，对应AppleThreadPriority中kThreadPriorityDisplay = 37
    TPThreadPriorityDisplay = 23,
    /// 播放器紧急display线程优先级, 字幕线程，对应AppleThreadPriority中kThreadPriorityUrgentDisplay = 43
    TPThreadPriorityUrgentDisplay = 27,
    /// 播放器视频线程优先级，该档与APP UI线程同一个级别.对应AppleThreadPriority中kThreadPriorityVideo = 47
    TPThreadPriorityVideo = 29,
    /// 播放器视频音频优先级, 播放器可能为了不更多抢占UI线程的调度，会保守设置该线程.
    /// 对应AppleThreadPriority中kThreadPriorityAudio = 56
    TPThreadPriorityAudio = 35,
    /// 播放器紧急音频优先级，对应AppleThreadPriority中kThreadPriorityUrgentAudio = 62
    /// Note:建议不要轻易使用,最高档位，比UI线程高两个档位.
    TPThreadPriorityUrgentAudio = 38,
};
