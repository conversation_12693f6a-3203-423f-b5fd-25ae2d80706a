/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPRichMediaFactory.h
 * @brief    富媒体同步器和异步请求器的创建工厂
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/9
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#import "ITPRichMediaSynchronizer.h"
#import "ITPRichMediaAsyncRequester.h"

NS_ASSUME_NONNULL_BEGIN

/// 富媒体同步器和异步请求器的创建工厂
@interface TPRichMediaFactory : NSObject

/// 创建富媒体同步器
/// @return 富媒体同步器
+ (id<ITPRichMediaSynchronizer>)createRichMediaSynchronizer;

/// 创建富媒体异步请求器
/// @return 富媒体异步请求器
+ (id<ITPRichMediaAsyncRequester>)createRichMediaAsyncRequester;

@end

NS_ASSUME_NONNULL_END
