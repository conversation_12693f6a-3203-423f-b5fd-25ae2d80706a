/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPErrorType.h
 * @brief    播放器的错误类型定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/3
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#pragma mark - 错误码定义

/// 播放器的错误类型和错误码定义
/// 数值[1, 100)部分为通用错误类型
/// 数值[100, 150)部分为Android平台通用错误类型
/// 数值[150, 200)部分为iOS平台通用错误类型
/// 数值[200, 250)部分为Windows平台通用错误类型
/// 数值[250, 1000)部分为以后其他平台预留
/// 数值[1000, 2000)部分是自研内核错误类型，跟native层(ITPPlayerMessageCallback#ErrorType)保持一致
/// 数值[2000, 2500)是Android MediaPlayer错误类型
/// 数值[2500, 3000)为iOS AVPlayer错误类型
/// 数值[3000, 3500)为Windows系统播放器错误类型
/// 数值[3500, 6000)为以后其他播放器的错误类型预留
/// 数值[6000, 6500)是下载组件错误码类型

typedef NS_ENUM(NSInteger, TPErrorType) {
    /// 未知错误类型
    TPErrorTypeUnknown = -1,
#pragma mark - iOS平台通用错误类型
    /// iOS平台层框架层通用错误类型
    TPErrorTypeiOSPlatformGeneral = 150,
#pragma mark - 自研内核错误类型定义
    /// 自研内核通用错误类型
    TPErrorTypeSelfDevPlayerGeneral = 1001,

    /// Demuxer错误
    /// 自研内核Demuxer通用错误类型
    TPErrorTypeSelfDevPlayerDemuxerGeneral = 1100,
    /// 自研内核Demuxer网络错误
    TPErrorTypeSelfDevPlayerDemuxerNetwork = 1101,
    /// 自研内核Demuxer解析时遇到码流数据错误
    TPErrorTypeSelfDevPlayerDemuxerStream = 1102,
    /// Demuxer缓冲时间超过
    TPErrorTypeSelfDevPlayerDemuxerBufferingTimeout = 1103,
    /// Demuxer预加载prepare超过
    TPErrorTypeSelfDevPlayerDemuxerPrepareTimeout = 1104,

    /// Decoder错误
    /// 自研内核Decoder通用错误类型
    TPErrorTypeSelfDevPlayerDecoderGeneral = 1200,
    /// 自研内核Decoder不支持当前音频格式
    TPErrorTypeSelfDevPlayerDecoderAudioNotSupport = 1210,
    /// 自研内核Decoder解码音频数据有错误，无法解码
    TPErrorTypeSelfDevPlayerDecoderAudioStream = 1211,
    /// 自研内核Decoder不支持当前视频格式
    TPErrorTypeSelfDevPlayerDecoderVideoNotSupport = 1220,
    /// 自研内核Decoder解码当前视频数据有错误，无法解码
    TPErrorTypeSelfDevPlayerDecoderVideoStream = 1221,
    /// 自研内核Decoder当前字幕格式解码器不支持
    TPErrorTypeSelfDevPlayerDecoderSubtitleNotSupport = 1230,
    /// 自研内核Decoder当前字幕数据有错误，无法解码
    TPErrorTypeSelfDevPlayerDecoderSubtitleStream = 1231,

    /// Renderer错误
    /// 自研内核Renderer通用错误类型
    TPErrorTypeSelfDevPlayerRenderGeneral = 1300,

    /// 音频后处理错误
    /// 自研内核音频后处理通用错误类型
    TPErrorTypeSelfDevPlayerAudioPostProcessGeneral = 1500,

    /// 视频后处理错误
    /// 自研内核视频后处理未知错误
    TPErrorTypeSelfDevPlayerVideoPostProcessGeneral = 1600,

    /// 自研内核错误类型类型到此为止
    TPErrorTypeSelfDevPlayerEnd = 1999,
#pragma mark - iOS系统播放器错误类型定义
    /// 系统播放器通用错误类型
    TPErrorTypeSystemAVPlayerGeneral = 2500,
    /// 系统播放器网络错误
    TPErrorTypeSystemAVPlayerNetwork = 2501,
    /// 系统播放器画中画错误
    TPErrorTypeSystemAVPlayerPictureInPicture = 2502,
    /// 系统播放器AirPlay错误
    TPErrorTypeSystemAVPlayerAirPlay = 2503,
    /// 系统播放器截图错误
    TPErrorTypeSystemAVPlayerSnapshot = 2504,
    /// 系统播放器错误到此为止
    TPErrorTypeSystemAVPlayerEnd = 2599,

#pragma mark - 下载组件错误类型定义
    /// 下载组件通用错误类型
    TPErrorTypeDownloadProxyGeneral = 6000,
    /// 下载组件错误类型到此为止
    TPErrorTypeDownloadProxyEnd = 6499,
};
