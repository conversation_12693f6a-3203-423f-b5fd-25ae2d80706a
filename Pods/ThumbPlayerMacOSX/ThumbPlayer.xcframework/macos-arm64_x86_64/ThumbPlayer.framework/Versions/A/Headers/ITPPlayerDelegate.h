/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPPlayerDelegate.h
 * @brief    ITPPlayer的delegate定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/03/10
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPOnInfoID.h"
#import "TPOnInfoParam.h"
#import "TPError.h"
#import "TPVideoFrameBuffer.h"
#import "TPAudioFrameBuffer.h"
#import "TPPlayerState.h"
#import "TPDebugTrackingInfo.h"
#import "ITPMediaAssetRequest.h"
#import "TPSubtitleData.h"

@protocol ITPPlayer;

NS_ASSUME_NONNULL_BEGIN

/// 播放器delegate定义
@protocol ITPPlayerDelegate <NSObject>

@required

 /// 视频prepared回调
 /// @param player 当前播放器实例
- (void)onPreparedWithPlayer:(id<ITPPlayer>)player;

/// 播放结束回调
/// @param player 当前播放器实例
- (void)onCompletionWithPlayer:(id<ITPPlayer>)player;

/// 播放错误回调
/// @param player 当前播放器的实例
/// @param error 错误信息
- (void)onPlayer:(id<ITPPlayer>)player error:(TPError *)error;

@optional

/// 播放器消息回调
/// @param player 当前播放器的实例
/// @param onInfoID 消息ID，参考TPOnInfoID
/// @param onInfoParam 消息参数
- (void)onPlayer:(id<ITPPlayer>)player onInfoID:(TPOnInfoID)onInfoID onInfoParam:(nullable TPOnInfoParam *)onInfoParam;

/// seek结束回调
/// @param player 当前播放器的实例
/// @param opaque 调用ITPPlayer#seekToAsync:opaque或ITPPlayer#seekToAsync:mode:opaque:时传入的opaque参数
- (void)onPlayer:(id<ITPPlayer>)player seekCompleteWithOpaque:(int64_t)opaque;

/// 视频size发生改变的回调
/// @param player 当前播放器的实例
/// @param width 视频宽
/// @param height 视频高
- (void)onPlayer:(id<ITPPlayer>)player videoSizeChangedWithWidth:(int)width height:(int)height;

/// 视频帧回调，输出视频解码后的数据
/// TODO: 以前默认为视频数据格式为TPPixelFormatCVPixelBuffer，现在默认为视频源数据格式。业务方若依然需要使用CVPixelBuffer获取数据，
///       则需在启播前配置TPOptionalIDBeforeIntOutVideoPixelFormat为TPPixelFormatCVPixelBuffer
/// @param player 当前播放器的实例
/// @param videoFrameBuffer 视频帧数据
- (void)onPlayer:(id<ITPPlayer>)player videoFrameOut:(TPVideoFrameBuffer *)videoFrameBuffer;

/// 音频帧回调，输出音频解码后的数据
/// @param player 当前播放器的实例
/// @param audioFrameBuffer 音频PCM数据
- (void)onPlayer:(id<ITPPlayer>)player audioFrameOut:(TPAudioFrameBuffer *)audioFrameBuffer;

/// 音频后处理回调，输出音频解码后的数据，调用方收到后进行后处理，然后再返回一个音频帧
/// @param player 当前播放器的实例
/// @param audioFrameBuffer 音频pcm数据
/// @return 处理后的音频pcm数据
- (TPAudioFrameBuffer *)onPlayer:(id<ITPPlayer>)player audioProcessFrameOut:(TPAudioFrameBuffer *)audioFrameBuffer;

/// 字幕数据回调
/// @param player 当前播放器的实例
/// @param subtitleData 字幕数据
- (void)onPlayer:(id<ITPPlayer>)player subtitleDataOut:(TPSubtitleData *)subtitleData;

/// 播放状态回调
/// @param player 当前播放器的实例
/// @param preState 前一个状态
/// @param curState 当前状态
- (void)onPlayer:(id<ITPPlayer>)player stateChangedFromState:(TPPlayerState)preState toState:(TPPlayerState)curState;

/// 异步停止播放器完成回调，调用ITPPlayer#stopAsync后，通过此接口通知异步停止播放器已经完成。
/// 可以在此接口内再继续调用其他接口，比如reset等接口。
/// @param player 当前播放器的实例
- (void)onStopAsyncCompleteWithPlayer:(id<ITPPlayer>)player;

/// 播放器运行过程中的一些细节信息回调，用于调试或者上报
/// @param player 当前播放器的实例
/// @param info 详细信息
- (void)onPlayer:(id<ITPPlayer>)player debugTrackingInfo:(TPDebugTrackingInfo *)info;

/// 获取离播放开始还需要等待的时间，用于内部做数据下载策略
/// @param player 当前播放器的实例
/// @return 播放开始需要的时间，单位毫秒
- (int64_t)onGetRemainTimeBeforePlayMsWithPlayer:(id<ITPPlayer>)player;

/// 截图成功回调
/// @param player 当前播放器的实例
/// @param opaque 调用ITPPlayer#snapshotAsync:opaque:时传入的opaque参数
/// @param frameBuffer 截图数据帧，格式为TPPixelFormatRGBA
- (void)onPlayer:(id<ITPPlayer>)player
snapshotSuccessWithOpaque:(int64_t)opaque
     frameBuffer:(TPVideoFrameBuffer *)frameBuffer;

/// 截图失败的回调
/// @param player 当前播放器的实例
/// @param opaque 调用ITPPlayer#snapshotAsync:opaque时传入的opaque参数
/// @param error 错误信息
- (void)onPlayer:(id<ITPPlayer>)player snapshotFailedWithOpaque:(int64_t)opaque error:(TPError *)error;

/// asset过期回调，asset的过期时间通过ITPMediaAsset#setParamValue:forKey来传递，key为
/// "task_param_url_expire_time_sec"，内部每隔一段时间判断asset播放时间是否接近设置的过期时间，如果即将过期，
/// 则抛该回调，通知外面更新资源。如果没有设置过期时间，则不会抛该回调。
/// @param player 当前播放器的实例
/// @param request 调用方通过该request向播放器更新资源，调用方如需向后台请求资源，需先保存下该request，待资源准备
///                      好后，通过该request来向播放器传递一个新的asset。
- (void)onPlayer:(id<ITPPlayer>)player mediaAssetExpireWithRequest:(id<ITPMediaAssetRequest>)request;

@end

/// airplay回调
@protocol ITPPlayerAirplayDelegate <NSObject>

/// AirPlay状态回抛
/// @param player 当前播放器的实例
/// @param active AirPlay是否激活，也可以通过ITPPlayer#isExternalPlaybackActive查询
- (void)onPlayer:(id<ITPPlayer>)player airplayStateChanged:(BOOL)active;

/// AirPlay错误回抛
/// @param player 播放器实例
/// @param error 错误信息
- (void)onPlayer:(id<ITPPlayer>)player airplayError:(TPError *)error;

@end

/// 画中画delegate定义
@protocol ITPPlayerPictureInPictureDelegate <NSObject>

@required

/// 画中画即将开始
/// @param player 当前播放器的实例
- (void)onPictureInPictureWillStartWithPlayer:(id<ITPPlayer>)player;

/// 画中画已经开始
/// @param player 当前播放器的实例
- (void)onPictureInPictureDidStartWithPlayer:(id<ITPPlayer>)player;


/// 画中画开启失败
/// @param player 当前播放器的实例
/// @param error 错误信息
- (void)onPlayer:(id<ITPPlayer>)player pictureInPictureStartFailed:(TPError *)error;


/// 画中画即将停止
/// @param player 当前播放器的实例
- (void)onPictureInPictureWillStopWithPlayer:(id<ITPPlayer>)player;

/// 画中画已经停止
/// @param player 当前播放器的实例
- (void)onPictureInPictureDidStopWithPlayer:(id<ITPPlayer>)player;

/// 从画中画恢复到默认的UI，即从画中画退出，恢复到画中画之前的UI。调用方响应该回调进行UI恢复。
/// @param player 当前播放器的实例
/// @param completionHandler 调用方完成UI回复后，须调用该completionHandler通知播放器完成。
- (void)onPlayer:(id<ITPPlayer>)player restoreUserInterfaceForPictureInPictureStopWithCompletionHandler:(void (^)(BOOL restored))completionHandler;

@end

NS_ASSUME_NONNULL_END
