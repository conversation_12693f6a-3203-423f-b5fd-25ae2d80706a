/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPDrmCapability.h
 * @brief    获取Thumbplayer的drm能力
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/20
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPDrmType.h"

/// drm能力支持情况定义。
/// 注意：这些值的定义值不要改动，此值和内核层的值是一一对应关系
typedef NS_ENUM(NSInteger, TPDrmCap) {
    /// 不支持drm
    TPDrmCapNotSupport = 0,
    /// 支持drm
    TPDrmCapSupport = 1,
};

/// drm能力
@interface TPDrmCapability : NSObject

/// 查询特定类型的DRM是否支持
/// @param drmType DRM类型
/// @return 返回对应的drm能力
+ (TPDrmCap)drmCapability:(TPDrmType)drmType;

@end
