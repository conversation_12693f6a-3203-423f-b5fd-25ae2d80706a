/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPMediaAssetRequest.h
 * @brief    媒体资源更新请求
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/04/04
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPMediaAsset.h"

NS_ASSUME_NONNULL_BEGIN

/// ITPMediaAssetRequest用于播放器向调用方请求资源更新的情况，配合 ITPPlayerDelegate#onPlayer:mediaAssetExpireWithRequest:一起使用。
/// 播放器通过ITPPlayerDelegate#onPlayer:mediaAssetExpireWithRequest:向外回调时，会携带一个ITPMediaAssetRequest实例，调用方更新完资源后，
/// 通过该ITPMediaAssetRequest实例向播放器传递一个新的asset。
@protocol ITPMediaAssetRequest <NSObject>

/// 更新asset
/// @param asset ITPMediaAsset实例
- (void)updateMediaAsset:(id<ITPMediaAsset>)asset;

@end

NS_ASSUME_NONNULL_END
