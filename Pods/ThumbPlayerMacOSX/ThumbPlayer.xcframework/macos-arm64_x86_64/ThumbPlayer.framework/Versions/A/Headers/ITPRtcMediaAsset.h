/*****************************************************************************
 * @copyright  Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file       ITPRtcMediaAsset.h
 * @brief      RTC直播资源
 * <AUTHOR>
 * @version    1.0.0
 * @date       2023/3/10
 * @license    GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPUrlMediaAsset.h"

NS_ASSUME_NONNULL_BEGIN
/// 当前只有Standalone Leb快直播支持播控参数 是否开启自动升降档，参数:true 开启 false 不开启
FOUNDATION_EXPORT NSString *const TPAssetParamKeyBoolEnableServerAbr;

/// SDP(Session Description Protocol)交互模式的定义
typedef NS_ENUM(NSUInteger, TPSdpExchangeMode) {
    /// 内核提供的默认模式，使用标准的sdp格式
    TPSdpExchangeModeNormal = 0,
    /// 内核提供的mini sdp模式，会先将标准sdp压缩，在使用udp交互
    /// 可以加速起播，但需要sdp信令服务器支持
    TPSdpExchangeModeMini = 1,
    /// 由业务自己实现sdp交互，内核会回调offer SDP，业务做完SDP交互后把结果通过callback传回给内核
    TPSdpExchangeModeCustom = 2
};

#pragma mark - TPRemoteSdpInfo
/// 远端SDP信息，用于TPSdpExchangeModeCustom
@interface TPRemoteSdpInfo : NSObject
/// 本次SDP交互是否成功
@property (nonatomic, assign) BOOL success;
/// 当前会话的id
@property (nonatomic, assign) int sessionID;
/// 做完SDP交互之后得到的对端SDP字符串
@property (nonatomic, copy) NSString *remoteSdp;
@end

#pragma mark - ITPRtcSdpExchangeListener
/// SDP交互delegate，用于TPSdpExchangeModeCustom
@protocol ITPRtcSdpExchangeDelegate <NSObject>
/// rtc播放SDP信息回调
/// @param localSdp 本地通过 webrtc-core CreateOffer api拿到的会话描述字符串
/// @param sessionID 当前当话ID, 在webrtc demuxer内部维护
/// @return 做完媒体协商的结果
- (TPRemoteSdpInfo *)onSdpExchangeLocalSdp:(NSString *)localSdp sessionID:(int)sessionID;
@end

#pragma mark - ITPRtcMediaAsset
/// Rtc直播资源接口
@protocol ITPRtcMediaAsset <ITPUrlMediaAsset>
/// 设置SDP交互模式
/// @param exchangeMode SDP交互模式
- (void)setSdpExchangeMode:(TPSdpExchangeMode)exchangeMode;

/// 设置RTC直播的信令服务器地址，SDP交互模式为TPSdpExchangeModeNormal和TPSdpExchangeModeMini时需要设置
/// @param sdpServerUrl RTC直播的信令服务器地址
- (void)setSdpServerUrl:(NSString *)sdpServerUrl;

/// 设置SDP交换delegate，在SDP交互模式为TPSdpExchangeModeCustom时需要设置
/// @param delegate ITPRtcSdpExchangeDelegate
- (void)setSdpExchangeDelegate:(id<ITPRtcSdpExchangeDelegate>)delegate;
@end

NS_ASSUME_NONNULL_END
