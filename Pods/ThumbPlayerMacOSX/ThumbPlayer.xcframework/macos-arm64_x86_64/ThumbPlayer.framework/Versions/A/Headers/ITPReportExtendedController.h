/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     ITPReportExtendedController.h
 * @brief    数据上报扩展接口
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/3
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPReportInfoGetter.h"

/// 数据上报扩展接口
/// 开启数据上报后，可通过{@link ITPPlayer#getReportExtendedController()}获取该接口的实例
@protocol ITPReportExtendedController <NSObject>

/// 设置扩展上报信息
/// 设置后播放器的数据上报模块会通过该接口获取业务方设置的上报数据，并与播放器自身的数据一同进行数据上报
/// 必须在启播{@link ITPPlayer#prepareAsync()}前设置，否则无效
/// @param getter 参考{@link ITPReportInfoGetter}
- (void)setReportInfoGetter:(nullable id<ITPReportInfoGetter>)getter;

/// 设置上报抽样率
/// 播放器 prepare() => stop()/reset()/release() 为一次播放流水，这里以播放流水为最小单位进行抽样
/// 若被抽样，则该次播放流水中所有的上报事件都会进行上报
/// 抽样率默认值为1，即默认每轮播放流水都会被上报
/// 必须在启播{@link ITPPlayer#prepareAsync()}前设置，否则无效
/// @param rate 抽样率，取值[0.0f, 1.0f]，若<0则为0，若大于1则为1
- (void)setReportSamplingRate:(float)rate;

@end
