/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPMediaAssetParamMap.h
 * @brief    有序key-value对参数，调用方通过add方法一个一个的添加，内部按添加的顺序保存
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/17
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITPMediaAssetObjectParam.h"

NS_ASSUME_NONNULL_BEGIN

/// 这里定义的参数用于在hls avs 分离或dash中预选则某一路视频、音频或字幕。
/// key的命名规则：TPAssetParamKey + 类型 + 名字，
/// 其中"类型"表示key对应value的类型，可以为int、long、float、String，不过所有类型都是通过string传入，内部按照key指定的类型来取值。

/// 以下这些参数用于hls情况下通过[ITPMediaAsset setObjectParamValue:(TPMediaAssetObjectParam*) forKey:(NSString*)]
/// 设置ITPMediaAsset TPAssetParamKeyObjectPreferredVideo
///
/// 通过指定这些参数中的一个或多个，来指定选择哪一路视频流。value的取值须参考hls master playlist中
/// 相应字段的取值方式和范围。内部按照参数传入的顺序来匹配，参数越靠前，优先级越高。内部将这些传入的参数跟hls master playlist中
/// 各个stream的参数进行比较，从而选出最匹配的一条流。如果不能匹配到任何流，则默认选择playlist中第一条。
/// 例如：传入FRAME-RATE=30，BANDWIDTH=20000000，会首先匹配帧率，然后再从匹配的结果中匹配带宽。
/// 注意dash和hls的匹配规则稍有不同，hls会顺序匹配各个传入参数，而dash一旦匹配到某个参数，就不再匹配后面的参数。请看下面dash部分的注释。

/// 单个参数比较时，不同的参数匹配规则不同，目前匹配规则有三种：
/// 精准匹配：必须完全相等，比如轨道名字
/// 向下匹配：找到对应参数等于传入参数、或者比传入参数小且最接近的那一路流，比如分辨率
/// 最近匹配：找到对应参数和传入参数最接近的那一路流，比如帧率

/// hls video track framerate， 对应HLS master playlist内video的FRAME-RATE字段
/// 匹配规则：最近匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyFloatHlsTrackFrameRate;

/// hls video track codecs，对应HLS master * *   playlist内video的CODECS字段，比如：avc1.4d401f,mp4a.40.2
/// 匹配规则：精准匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyStringHlsTrackCodecs;

/// hls video track bandwidth，对应HLS master playlist内video的BANDWIDTH字段
/// 匹配规则：向下匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyIntHlsTrackBandWidth;

/// hls video track resolution，对应HLS master playlist内video的RESOLUTION字段，value以"width x height"的格式传入，比如：1920x1080
/// 匹配规则：向下匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyStringHlsTrackResolution;

/// 以下这些参数用于hls情况下通过[ITPMediaAsset setObjectParamValue:(TPMediaAssetObjectParam*) forKey:(NSString*)]设置
/// ITPMediaAsset TPAssetParamKeyObjectPreferredAudio 或
/// ITPMediaAsset TPAssetParamKeyObjectPreferredSubtitle
/// 通过指定这些参数中的一个或多个，来指定选择哪一路音频/字幕。value的类型都为String，value的取值须参考hls master playlist中
/// 相应字段的取值方式和范围。匹配规则见上述注释。

/// hls audio / subtitle track group id，对应HLS master playlist内audio/subtitle的GROUP-ID字段
/// 匹配规则：精准匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyStringHlsTrackGroupID;

/// hls audio / subtitle track name，对应HLS master playlist内audio/subtitle的NAME字段
/// 匹配规则：精准匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyStringHlsTrackName;

/// hls audio / subtitle track language，对应HLS master playlist内audio/subtitle的LANGUAGE字段
/// 匹配规则：精准匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyStringHlsTrackLanguage;

/// 以下这些参数用于dash情况下通过[ITPMediaAsset setObjectParamValue:(TPMediaAssetObjectParam*) forKey:(NSString*)]设置
/// ITPMediaAsset TPAssetParamKeyObjectPreferredVideo 或
/// ITPMediaAsset TPAssetParamKeyObjectPreferredAudio 或
/// ITPMediaAsset TPAssetParamKeyObjectPreferredSubtitle
///
/// 通过指定这些参数中的一个或多个，来指定选择哪一路视频/音频/字幕。value的取值须参考dash mpd(media presentation description)中
/// 这相应字段的取值方式和范围。内部按照参数传入的顺序来匹配，参数越靠前，优先级越高。内部将这些传入的参数跟dash mpd中
/// 各个stream的参数进行比较，从而选出最匹配的一条流。如果不能匹配到任何流，则默认选择playlist中第一条。
/// dash的匹配规则目前较简单，一旦命中，不再往后匹配，
/// 例如：预选择视频时，传入frameRate=30，bandwidth=20000000，首先匹配到帧率相近的一路流，后面的带宽不再匹配，
/// 例如：预选则音频时，传入lang=eng，bandwidth=96636，首先匹配lang，如果匹配到，则停止，否则继续匹配bandwidth

/// 单个参数比较时，不同的参数匹配规则不同，目前匹配规则有三种：
/// 精准匹配：必须完全相等，比如轨道名字
/// 向下匹配：找到对应参数等于传入参数、或者比传入参数小且最接近的那一路流，比如分辨率
/// 最近匹配：找到对应参数和传入参数最接近的那一路流，比如帧率

/// dash video track framerate，对应dash mpd内video的frameRate字段
/// 匹配规则：最近匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyFloatDashTrackFrameRate;

/// dash video track bandwidth，对应dash mpd内video/audio的bandwidth字段
/// 匹配规则：向下匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyIntDashTrackBandWidth;

/// dash video track resolution 对应dash mpd内video的width字段和height字段，以"width x height"的格式传入，比如：1920x1080
/// 匹配规则：向下匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyStringDashTrackResolution;

/// dash video track luma samples，与resolution不同的是，此参数传入dash mpd内video的width和height的乘积结果
/// 匹配规则：向下匹配
FOUNDATION_EXPORT NSString *const TPAssetKeyIntDashTrackLumaSamples;

/// dash audio/subtitle track language，对应dash mpd内audio/subtitle的lang字段
/// 匹配规则：精准匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyStringDashTrackLanguage;

/// dash audio/subtitle track label，对应dash mpd内audio/subtitle的label字段
/// 匹配规则：精准匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyStringDashTrackLabel;


/// 以下这些参数用于hls多program情况下通过[ITPMediaAsset setObjectParamValue:(TPMediaAssetObjectParam*) forKey:(NSString*)]设置，
/// key为：ITPMediaAsset TP_ASSET_PARAM_KEY_OBJECT_PREFERRED_PROGRAM
///  通过指定这些参数中的一个或多个，来指定选择哪一路program。value的取值须参考hls master playlist中
///  相应字段的取值方式和范围。内部按照参数传入的顺序来匹配，参数越靠前，优先级越高。内部将这些传入的参数跟hls
///  master playlist中的各个program的参数进行比较，从而选出最匹配的一条program。如果不能匹配到任何program，则默认选择playlist中第一条。
///  优先级的顺序为：programId > bandwidth > luma samples

///  单个参数比较时，不同的参数匹配规则不同，目前对于program匹配规则有两种：
///  精准匹配：必须完全相等，比如startProgramIndex
///  向下匹配：找到对应参数等于传入参数、或者比传入参数小且最接近的那一路流，比如bandwidth, luma samples

/// 设置Hls多Program的默认起播通道，为Program列表的下标
/// 配置有效值为>=-1的整形数转换的string. 缺省为-1播放内核理解为应使用优先级更低的信息进行配置，bandwidth、luma samples
/// 0/1/2，表示使用对应下标的Program Info进行起播，此时不会Open其他的Program对应的stream
/// 匹配规则：精准匹配
FOUNDATION_EXPORT NSString *const TPAssetParamKeyIntHlsStartProgramIndex;

/// 设置Hls多Program的默认起播通道，根据设定的Prefer Bandwidth选最优的Program进行起播，单位是bps hls program bandwidth，对应HLS master
/// playlist内program的BANDWIDTH字段 配置有效值为>=-1的整形数转换的string. 缺省为-1播放内核理解为应使用优先级更低的信息进行配置，luma samples
/// 匹配规则：向下匹配
/// 如：bandwidth1 : 2462348 , bandwidth2 : 1105139， 输入：2462348，匹配bandwidth1,  输入 2462348 -1 ， 匹配bandwidth2
FOUNDATION_EXPORT NSString *const TPAssetParamKeyIntHlsProgramPreferredBandWith;

/// 设置Hls多Program的默认起播通道，根据设定的Prefer Luma Samples选最优的Program进行起播
/// hls program luma samples，对应HLS master playlist内program的RESOLUTION字段的width和height的乘积结果
/// 配置有效值为>=-1的整形数转换的string. 缺省为-1播放内核理解为应使用优先级更低的信息进行配置
/// 匹配规则：向下匹配
/// 如：luma samples1 : 1920 * 1080 , luma samples2 : 1280 * 720
/// 输入：1920 * 1080，匹配luma samples1,  输入 1920 * 1080 -1 ， 匹配luma samples2
FOUNDATION_EXPORT NSString *const TPAssetParamKeyIntHlsProgramPreferredLumaSamples;

@protocol ITPMediaAssetParamMap <ITPMediaAssetObjectParam>

/// 添加key-value. 内部按照添加的顺序存放键值对
/// @param value 要设置的参数的value
/// @param key 要设置的参数的key
- (void)addStringValue:(NSString *)value forKey:(NSString *)key;

/// 返回根据添加顺序存放的key/value NSMutableDictionary
/// @return 返回所有已添加的key和其对应的value
- (NSDictionary<NSString*, NSString*> *)paramDictionary;

@end

NS_ASSUME_NONNULL_END
