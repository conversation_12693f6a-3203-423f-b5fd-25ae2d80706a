/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPDashFormat.h
 * @brief    Dash容器格式信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/1
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITPContainerInfo.h"

NS_ASSUME_NONNULL_BEGIN

/// Dash容器格式信息
@interface TPDashFormat : NSObject <ITPContainerInfo>

/// 轨道id，对应dash mpd内Representation元素的id属性
@property (nonatomic, copy) NSString *representationId;

/// 轨道编码集，对应dash mpd内的codecs属性
@property (nonatomic, copy) NSString *codecs;

/// 轨道互联网媒体类型，对应dash mpd内的mimeType属性
@property (nonatomic, copy) NSString *mimeType;

/// 轨道语言，对应dash mpd内的lang属性
@property (nonatomic, copy) NSString *language;

/// 轨道名称，对应dash mpd内的Label属性
@property (nonatomic, copy) NSString *label;

/// 轨道宽，仅用于video，对应dash mpd内的width属性
@property (nonatomic, assign) int width;

/// 轨道高，仅用于video，对应dash mpd内的height属性
@property (nonatomic, assign) int height;

/// 轨道带宽，对应dash mpd内的bandwidth属性
@property (nonatomic, assign) int bandwidth;

/// 轨道音频频道数目，从dash mpd AudioChannelConfiguration元素的属性中解析出的通道数
@property (nonatomic, assign) int channels;

/// 轨道音频采样率，对应dash mpd内的audioSamplingRate属性
@property (nonatomic, assign) int samplingRate;

/// 轨道帧率，仅用于video，对应dash mpd内的frameRate属性
@property (nonatomic, assign) float frameRate;

@end

NS_ASSUME_NONNULL_END
