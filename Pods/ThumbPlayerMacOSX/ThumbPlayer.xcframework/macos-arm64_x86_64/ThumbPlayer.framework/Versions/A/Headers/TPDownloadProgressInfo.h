/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPDownloadProgressInfo.h
 * @brief    下载进度相关数据参数
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/1
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

NS_ASSUME_NONNULL_BEGIN

/// 下载组件返回的下载进度信息变更
/// 由于时间是根据文件预估，playableDurationMS（可播放时长）稍有偏差。
/// 包含在TPPlayerInfoObjectDownloadProgressUpdate
@interface TPDownloadProgressInfo : NSObject
/// 当前的可播时长，以时间position=0起始计算，单位毫秒
@property (nonatomic, assign) int64_t availablePositionMs;
/// 当前的下载速度bps
@property (nonatomic, assign) int64_t downloadSpeedbps;
/// 当前文件已经下载的大小
@property (nonatomic, assign) int64_t downloadBytes;
/// 当前文件总大小
@property (nonatomic, assign) int64_t fileTotalBytes;
/// 扩展信息
@property (nonatomic, copy) NSString *extraInfo;

@end
NS_ASSUME_NONNULL_END
