/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPMediaAsset.h
 * @brief    资源接口，用于描述资源。该接口是资源类的顶层接口，所有类型的asset都继承于该接口
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/16
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

#import "ITPMediaAssetObjectParam.h"

NS_ASSUME_NONNULL_BEGIN

/// 以下是参数key定义，用于设置扩展参数。
/// key的命名规则：TPAssetParamKey + 类型 + 名字，
/// 其中"类型"表示key对应value的类型，可以为bool、int、long、float、String，
/// 不过所有类型都是通过string传入，内部按照key指定的类型来取值。
/// 如果类型为"OBJECT"，则表示该key的value是一个对象，该对象必须继承于TPMediaAssetObjectParam

/// 用于avs分离，指定优先选择哪一路视频流，该参数的value是一个ITPMediaAssetParamMap的实例对象
FOUNDATION_EXPORT NSString *const TPAssetParamKeyObjectPreferredVideo;

/// 用于avs分离，指定优先选择哪一路音频流，该参数的value是一个ITPMediaAssetParamMap的实例对象
FOUNDATION_EXPORT NSString *const TPAssetParamKeyObjectPreferredAudio;

/// 用于avs分离，指定优先选择哪一路字幕，该参数的value是一个ITPMediaAssetParamMap的实例对象
FOUNDATION_EXPORT NSString *const TPAssetParamKeyObjectPreferredSubtitle;

/// 用于hls多program情况下，指定优先选择哪一路program，该参数的value是一个ITPMediaAssetParamMap的实例对象
FOUNDATION_EXPORT NSString *const TPAssetParamKeyObjectPreferredProgram;

/// asset透传标识
FOUNDATION_EXPORT NSString *const TPAssetParamKeyStringOpaque;

typedef NS_ENUM(NSInteger, TPAssetType) {
    TPAssetTypeUnknown = 0,
    /// 1和2对应安卓的asset类型Afd和Pfd，这里从3开始
    /// 单个url播放资源类型
    TPAssetTypeUrl = 3,
    /// 直播RTC播放资源类型
    TPAssetTypeRtc = 4,
    /// 加密播放资源类型
    TPAssetTypeDrm = 5,
    /// 组合型播放资源类型
    TPAssetTypeMultiAsset = 6,
    /// qlv占用了7
    /// 模拟直播资源类型
    TPAssetTypeSimulatedLive = 8,
};

@protocol ITPMediaAsset <NSObject>

/// 获取当前asset类型
/// @return AssetType
- (TPAssetType)assetType;

/// 当前asset资源是否有效. 一些必要的参数不能为空, 例如TPUrlMediaAsset中的url不能为空, 为空则无效
/// @return YES:有效 NO: 无效
- (BOOL)isValid;

/// 设置扩展参数，基本类型——int、float、double、bool以及字符串都通过该方法设置
/// 公共的key定义在ITPMediaAsset中，不同asset特有的param的key定义在各自的asset中
/// @param value 该param的值
/// @param key 健值，如果key已经存在，则覆盖原有值
- (void)setParamValue:(NSString *)value forKey:(NSString *)key;

/// 获取扩展参数
/// @param key 键值
/// @return key对应的param
- (nullable NSString *)paramValueForKey:(NSString *)key;

/// 设置对象参数，value的类型必须继承于TPMediaAssetObjectParam
/// @param value 该param的值
/// @param key 健值，如果key已经存在，则覆盖原有值
- (void)setObjectParamValue:(id<ITPMediaAssetObjectParam>)value forKey:(NSString *)key;

/// 获取对象参数
/// @param key 键值
/// @return key对应对象参数
- (nullable id<ITPMediaAssetObjectParam>)objectParamValueForKey:(NSString *)key;

@end

NS_ASSUME_NONNULL_END
