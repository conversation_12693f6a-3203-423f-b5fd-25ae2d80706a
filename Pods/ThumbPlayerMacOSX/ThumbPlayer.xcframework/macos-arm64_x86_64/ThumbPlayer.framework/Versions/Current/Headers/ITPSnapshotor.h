/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     ITPSnapshotor.h
 * @brief    不依赖于播放器实例的截视频图像截取接口
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/11/28
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPMediaAsset.h"
#import "TPError.h"
#import "TPVideoFrameBuffer.h"
#import "TPSnapshotParams.h"

NS_ASSUME_NONNULL_BEGIN
///视频图像截取回调
@protocol ITPSnapshotorDelegate <NSObject>

@required
///截图成功
///@param taskId 截图任务id
///@param requestedPositionMs 请求截图的播放位置
///@param actualPositionMs 实际截图的播放位置
///@param frameBuffer 截图数据帧
- (void)onSuccessWithTaskId:(int64_t)taskId
        requestedPositionMs:(int64_t)requestedPositionMs
           actualPositionMs:(int64_t)actualPositionMs
                frameBuffer:(TPVideoFrameBuffer *)frameBuffer;

///截图失败
///@param taskId 截图任务id
///@param requestedPositionMs 请求截图的播放位置
///@param error 错误信息
- (void)onErrorWithTaskId:(int64_t)taskId
      requestedPositionMs:(int64_t)requestedPositionMs
                    error:(TPError *)error;

@end

///不依赖于播放器实例的截视频图像截取接口
@protocol ITPSnapshotor <NSObject>

///截取单张图的接口
///@param positionMs 希望截图的播放位置,单位毫秒
///@param params 截图的参数
///@return 任务ID，用于回调区分任务
- (int64_t)snapshotAsyncAtPosition:(int64_t)positionMs
                            params:(nullable TPSnapshotParams *)params;

///截取多张图的接口
///@param positionsMs 希望截取的时间戳集合
///@param params 截图的参数
///@return 任务ID，用于回调区分任务
- (int64_t)snapshotAsyncForPositions:(NSArray<NSNumber *> *)positionsMs
                              params:(nullable TPSnapshotParams *)params;

///取消所有截图任务操作
- (void)cancelAll;

@end

NS_ASSUME_NONNULL_END
