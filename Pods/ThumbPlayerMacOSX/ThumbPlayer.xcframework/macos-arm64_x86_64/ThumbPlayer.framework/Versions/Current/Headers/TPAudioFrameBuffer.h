/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TPAudioFrameBuffer.h
 * @brief    音频帧数据定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/27
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPSampleFormat.h"
#import "TPChannelLayout.h"
#import "TPFrameEventFlag.h"

NS_ASSUME_NONNULL_BEGIN

/// 音频帧数据
@interface TPAudioFrameBuffer : NSObject

/// TPAudioFrameBuffer 释放时，通过该回调让外部主动释放内存
typedef void(*TPAudioFrameBufferReleaseCallback)(id userData, uint8_t **planeData, int planeCnt);

/// 音频sample数据
/// 当数据为非planar时，data[0]存放数据，
/// 当数据为planar时，data[0] 至 data[channels-1] 存放每个声道的数据
@property (nonatomic, assign, readonly) uint8_t **data;

/// 音频sample数据大小
/// 当数据为非planar时，size[0]存放数据长度
/// 当数据为planar时，size[0] 至 size[channels-1] 存放每个声道的size
@property (nonatomic, strong, readonly) NSArray<NSNumber *> *dataSize;

/// 音频的采样率
@property (nonatomic, assign, readonly) unsigned int sampleRate;

/// 音频的channel layout，见 TPChannelLayout.h 中的定义
@property (nonatomic, assign, readonly) TPChannelLayout channelLayout;

/// 音频格式，见TPSampleFormat.h中的定义
@property (nonatomic, assign, readonly) TPSampleFormat format;

/// 当前sample的PTS（Presentation Time Stamp）：即显示时间戳
@property (nonatomic, assign) int64_t ptsMs;

/// 每个声道的采样数
@property (nonatomic, assign, readonly) int nbSamples;

/// 声道数
@property (nonatomic, assign, readonly) int channels;

/// 事件标识
@property (nonatomic, assign) TPFrameEventFlag eventFlag;

/// 默认构造方法禁止使用，请使用其他构造方法
- (instancetype)init NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END
