/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPLogDelegate.h
 * @brief    日志回调定义
 * <AUTHOR> chen
 * @version  1.0.0
 * @date     2019/10/2
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 日志打印等级，用于控制应打印的日志类型
typedef NS_ENUM(NSInteger, TPLogLevel) {
    TPLogLevelVerbose = 0,
    TPLogLevelDebug = 1,
    TPLogLevelInfo = 2,
    TPLogLevelWarning = 3,
    TPLogLevelError = 4,
};

/// 播放器内部没有日志打印模块，为便于问题定位，APP要实现此日志打印接口，方便将播放器内的日志打印到APP的日志中，方便问题定位
@protocol ITPLogDelegate <NSObject>

/// 日志打印接口
/// @param logLevel 日志打印级别
/// @param tag 日志tag
/// @param file 文件名称
/// @param function 函数名称
/// @param line 代码行
/// @param content 日志内容
- (void)logWithLevel:(TPLogLevel)logLevel
                 tag:(NSString *)tag
                file:(NSString *)file
            function:(NSString *)function
                line:(NSUInteger)line
             content:(NSString *)content;

@end
