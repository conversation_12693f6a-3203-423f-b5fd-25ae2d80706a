/*****************************************************************************
 * @copyright  Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file       ThumbPlayer.h
 * @brief      ThumbPlayer SDK Umbrella Header
 * <AUTHOR>
 * @version    1.0.0
 * @date       2023/4/27
 * @license    GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

//! Project version number for ThumbPlayer.
FOUNDATION_EXPORT double ThumbPlayerVersionNumber;

//! Project version string for ThumbPlayer.
FOUNDATION_EXPORT const unsigned char ThumbPlayerVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <ThumbPlayer/PublicHeader.h>

#import <ThumbPlayer/ITPBeaconDataReporter.h>
#import <ThumbPlayer/ITPBusinessReportManager.h>
#import <ThumbPlayer/ITPContainerInfo.h>
#import <ThumbPlayer/ITPDrmMediaAsset.h>
#import <ThumbPlayer/ITPLocalProxy.h>
#import <ThumbPlayer/ITPLogDelegate.h>
#import <ThumbPlayer/ITPMediaAsset.h>
#import <ThumbPlayer/ITPMediaAssetObjectParam.h>
#import <ThumbPlayer/ITPMediaAssetParamMap.h>
#import <ThumbPlayer/ITPMediaAssetRequest.h>
#import <ThumbPlayer/ITPMultiMediaAsset.h>
#import <ThumbPlayer/ITPPlayer.h>
#import <ThumbPlayer/ITPPlayerProxy.h>
#import <ThumbPlayer/ITPPlayerConnection.h>
#import <ThumbPlayer/ITPPlayerConnectionNode.h>
#import <ThumbPlayer/ITPPlayerSynchronizer.h>
#import <ThumbPlayer/ITPPlayerExternalSyncClockGetter.h>
#import <ThumbPlayer/ITPPlayerSyncSource.h>
#import <ThumbPlayer/TPPlayerSyncClock.h>
#import <ThumbPlayer/ITPPlayerDelegate.h>
#import <ThumbPlayer/ITPPreloader.h>
#import <ThumbPlayer/ITPReportExtendedController.h>
#import <ThumbPlayer/ITPReportInfoGetter.h>
#import <ThumbPlayer/ITPRichMediaAsyncRequester.h>
#import <ThumbPlayer/ITPRichMediaAsyncRequesterDelegate.h>
#import <ThumbPlayer/ITPRichMediaSynchronizer.h>
#import <ThumbPlayer/ITPRichMediaSynchronizerDelegate.h>
#import <ThumbPlayer/ITPRtcMediaAsset.h>
#import <ThumbPlayer/ITPUrlMediaAsset.h>
#import <ThumbPlayer/ITPSnapshotor.h>
#import <ThumbPlayer/TPAudioAVSyncStrategy.h>
#import <ThumbPlayer/TPAudioCodecType.h>
#import <ThumbPlayer/TPAudioDecoderType.h>
#import <ThumbPlayer/TPAudioFrameBuffer.h>
#import <ThumbPlayer/TPAudioRendererType.h>
#import <ThumbPlayer/TPBufferStrategy.h>
#import <ThumbPlayer/TPChannelLayout.h>
#import <ThumbPlayer/TPColorSpace.h>
#import <ThumbPlayer/TPDashFormat.h>
#import <ThumbPlayer/TPDecoderCapability.h>
#import <ThumbPlayer/TPDebugTrackingInfo.h>
#import <ThumbPlayer/TPDefaultReportInfo.h>
#import <ThumbPlayer/TPDemuxerType.h>
#import <ThumbPlayer/TPDownloadProgressInfo.h>
#import <ThumbPlayer/TPDrmType.h>
#import <ThumbPlayer/TPDrmCapability.h>
#import <ThumbPlayer/TPError.h>
#import <ThumbPlayer/TPErrorType.h>
#import <ThumbPlayer/TPFrameEventFlag.h>
#import <ThumbPlayer/TPHdrCapability.h>
#import <ThumbPlayer/TPHdrCapAttribute.h>
#import <ThumbPlayer/TPHdrMappingType.h>
#import <ThumbPlayer/TPHdrType.h>
#import <ThumbPlayer/TPHlsTag.h>
#import <ThumbPlayer/TPJitterBufferParams.h>
#import <ThumbPlayer/TPLiveReportInfo.h>
#import <ThumbPlayer/TPLocalProxyFactory.h>
#import <ThumbPlayer/TPMgr.h>
#import <ThumbPlayer/TPMgrConfig.h>
#import <ThumbPlayer/TPMediaAssetFactory.h>
#import <ThumbPlayer/TPMediaType.h>
#import <ThumbPlayer/TPOnInfoID.h>
#import <ThumbPlayer/TPOnInfoParam.h>
#import <ThumbPlayer/TPOptionalID.h>
#import <ThumbPlayer/TPOptionalParam.h>
#import <ThumbPlayer/TPPixelFormat.h>
#import <ThumbPlayer/TPPlayerCapability.h>
#import <ThumbPlayer/TPPlayerConnectionFactory.h>
#import <ThumbPlayer/TPPlayerConstructParams.h>
#import <ThumbPlayer/TPPlayerCoreType.h>
#import <ThumbPlayer/TPPlayerFactory.h>
#import <ThumbPlayer/TPPlayerState.h>
#import <ThumbPlayer/TPPreloaderFactory.h>
#import <ThumbPlayer/TPProgramInfo.h>
#import <ThumbPlayer/TPPropertyID.h>
#import <ThumbPlayer/TPReduceLiveLatencyStrategy.h>
#import <ThumbPlayer/TPReportExtendedCommonKey.h>
#import <ThumbPlayer/TPRetCode.h>
#import <ThumbPlayer/TPRichMediaData.h>
#import <ThumbPlayer/TPRichMediaDataCallbackType.h>
#import <ThumbPlayer/TPRichMediaFactory.h>
#import <ThumbPlayer/TPRichMediaFeature.h>
#import <ThumbPlayer/TPRichMediaOnInfoID.h>
#import <ThumbPlayer/TPRichMediaOptParam.h>
#import <ThumbPlayer/TPSampleFormat.h>
#import <ThumbPlayer/TPSeekMode.h>
#import <ThumbPlayer/TPSnapshotMode.h>
#import <ThumbPlayer/TPSnapshotParams.h>
#import <ThumbPlayer/TPSnapshotorFactory.h>
#import <ThumbPlayer/TPSubtitleCodecType.h>
#import <ThumbPlayer/TPSubtitleData.h>
#import <ThumbPlayer/TPSubtitleDataType.h>
#import <ThumbPlayer/TPSubtitleFrameBuffer.h>
#import <ThumbPlayer/TPSubtitleRenderParams.h>
#import <ThumbPlayer/TPSubtitleText.h>
#import <ThumbPlayer/TPSwitchDataSourceMode.h>
#import <ThumbPlayer/TPThreadPriority.h>
#import <ThumbPlayer/TPTimeRange.h>
#import <ThumbPlayer/TPTrackInfo.h>
#import <ThumbPlayer/TPVideoCodecType.h>
#import <ThumbPlayer/TPVideoCropInfo.h>
#import <ThumbPlayer/TPVideoDecoderCapabilityRange.h>
#import <ThumbPlayer/TPVideoDecoderType.h>
#import <ThumbPlayer/TPVideoFrameBuffer.h>
#import <ThumbPlayer/TPVideoGravity.h>
#import <ThumbPlayer/TPVideoRendererType.h>
#import <ThumbPlayer/TPVideoSeiInfo.h>
#import <ThumbPlayer/TPVideoSeiType.h>
#import <ThumbPlayer/TPVodReportInfo.h>
#import <ThumbPlayer/ITPSimulatedLiveMediaAsset.h>
#import <ThumbPlayer/TPSubtitleRendererType.h>
#import <ThumbPlayer/TPHdrCapabilityQueryParams.h>
#import <ThumbPlayer/TPFieldOrder.h>
#import <ThumbPlayer/TPColorTransferCharacteristic.h>
#import <ThumbPlayer/TPSubtitleRenderInfo.h>
#import <ThumbPlayer/TPAdditionalInfoType.h>
