/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TPSubtitleRenderInfo.h
 * @brief    字幕渲染信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/11/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 字幕渲染信息，在对外回调字幕frame时，作为额外信息放到TPSubtitleFrameBuffer中，
/// 参考TPAdditionalInfoType#TPAdditionalInfoTypeSubtitleRenderInfo
///
/// 在该结构体中，包含绘制一帧字幕时的文本段列表。在绘制一帧字幕的文本时，由于不同部分有不同的格式，
/// 所以会被划分为不同的文本段分别进行绘制，比如下列webVTT字幕中一条字幕文本：
/// "The breeze <b>blows</b> gently"
/// 其中的单词blows左右有加粗标签。该条文本会被划分为三段：
/// "The breeze "
/// "blows"
/// " gently"

@interface TPSubtitleTextSegmentRenderInfo : NSObject

- (instancetype)init NS_UNAVAILABLE;

/// 纯文本内容
@property (nonatomic, copy) NSString *text;

/// 该段文本颜色，ARGB格式，默认0xFFFFFFFF
@property (nonatomic, assign) int color;

/// 该段文本在画布中的绘制区域
@property (nonatomic, assign) CGRect rect;

@end

@interface TPSubtitleRenderInfo : NSObject

- (instancetype)init NS_UNAVAILABLE;

/// 文本段列表
@property (nonatomic, copy) NSArray<TPSubtitleTextSegmentRenderInfo *> *textSegmentRenderInfos;

@end

NS_ASSUME_NONNULL_END
