/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPJitterBufferParams.h
 * @brief    JitterBuffer配置
 * <AUTHOR>
 * @version  1.0.0
 * @date     2021/9/30
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// JitterBuffer配置项，使用方式可参考：
/// https://iwiki.woa.com/pages/viewpage.action?pageId=847912025
@interface TPJitterBufferParams : NSObject

/// 允许下调高水位阈值的最小时长，低于此值将不在下调，单位ms
@property (nonatomic, assign) int64_t minDecreaseDurationMs;

/// 允许上调高水位阈值的最小时长，高于此值将不在上调，单位ms
@property (nonatomic, assign) int64_t maxIncreaseDurationMs;

/// 每次上调高水位阈值的时长，单位ms
@property (nonatomic, assign) int64_t perIncreaseDurationMs;

/// 每次下调高水位阈值的时长，单位ms
@property (nonatomic, assign) int64_t perDecreaseDurationMs;

/// 调整高水位阈值的时间间隔，防止频繁调整，单位ms
@property (nonatomic, assign) int64_t adjustIntervalThresholdMs;

/// 读包卡顿认定时长，超过则需要调整高水位阈值，单位ms
@property (nonatomic, assign) int64_t frozenThresholdMs;

@end
