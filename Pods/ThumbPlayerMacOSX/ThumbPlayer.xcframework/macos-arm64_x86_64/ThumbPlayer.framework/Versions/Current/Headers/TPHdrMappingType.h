/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPHdrMappingType.h
 * @brief    HDR映射类型定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/5/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// HDR映射类型定义. 与内核层枚举值保持一致，不要改动
typedef NS_ENUM(NSInteger, TPHdrMappingType) {
    /// 非HDR映射（TPHdrTypeNone类型视频帧的映射类型）
    TPHdrMappingTypeNone                    = 0,
    /// 硬件HDR动态元数据映射（系统硬件支持的Hdr动态元数据映射，如DolbyVision，HDRVivid，HDR10+）
    TPHdrMappingTypeHardHdrDynamicMapping   = 1,
    /// 软件HDR动态元数据映射（软件支持的HDR动态元数据映射，如HDRVivid软件渲染适配HDR屏）
    TPHdrMappingTypeSoftHdrDynamicMapping   = 2,
    /// 硬件HDR10静态元数据映射（系统硬件支持的HDR10静态元数据映射，如HDR10或HDRVivid降级为HDR10兼容模式）
    TPHdrMappingTypeHardHdr10StaticMapping  = 3,
    /// 软件HDR10静态元数据映射（软件支持的HDR10静态元数据映射）
    TPHdrMappingTypeSoftHdr10StaticMapping  = 4,
    /// 软件SDR动态元数据映射（软件支持的SDR屏胴体元数据映射，如HDRVivid软件渲染适配SDR屏）
    TPHdrMappingTypeSoftSdrDynamicMapping   = 5,
    /// 硬件HLG映射（系统硬件支持的Hlg映射）
    TPHdrMappingTypeHardHlgMapping          = 6,
    /// 软件HLG映射（软件支持的的Hlg映射）
    TPHdrMappingTypeSoftHlgMapping          = 7,
    /// 硬件PQ10映射（硬件支持的PQ10映射，不使用元数据进行tone mapping）
    TPHdrMappingTypeHardPQ10Mapping         = 8,
    /// 软件PQ10映射（软件支持的PQ10映射，不使用元数据进行tone mapping）
    TPHdrMappingTypeSoftPQ10Mapping         = 9
};
