/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TPAddtionalInfoType.h
 * @brief    frame或packet中携带的额外信息
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/11/08
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// frame或packet中携带的额外信息，
/// 枚举值和内核保持一致，若要修改，必须同步修改
typedef NS_ENUM(NSInteger, TPAdditionalInfoType) {
    /// 字幕渲染信息，参考TPSubtitleRenderInfo
    TPAdditionalInfoTypeSubtitleRenderInfo = 18,
};
