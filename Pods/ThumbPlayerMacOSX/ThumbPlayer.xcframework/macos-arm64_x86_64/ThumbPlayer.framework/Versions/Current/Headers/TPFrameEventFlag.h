/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPFrameEventFlag.h
 * @brief    音视频帧数据事件Flag.用于音视频帧数据中的事件标记eventFlag
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/6
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 音视频帧数据事件Flag
typedef NS_OPTIONS(NSInteger, TPFrameEventFlag) {
    /// 未定义事件
    TPFrameEventFlagUnknown = 0,
    /// Eos事件
    TPFrameEventFlagEos = 1 << 0,
    /// Flush事件
    TPFrameEventFlagFlush = 1 << 1,
};
