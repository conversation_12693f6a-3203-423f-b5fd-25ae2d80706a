/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPRichMediaData.h
 * @brief    富媒体数据定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPRichMediaDataCallbackType.h"
#import "TPTimeRange.h"

NS_ASSUME_NONNULL_BEGIN

/// 富媒体内容定义
@interface TPRichMediaContent : NSObject

/// 对应detail中result的startTime和endTime字段
@property (nonatomic, strong) TPTimeRange *timeRange;

/// 对应detail中result的content字段
@property (nonatomic, copy) NSString *content;

@end

/// 富媒体数据定义
/// https://iwiki.woa.com/pages/viewpage.action?pageId=327232578
@interface TPRichMediaData : NSObject
/// 富媒体功能类型，对应detail中的type字段
@property (nonatomic, copy) NSString *featureType;

/// 富媒体协议版本号，对应detail中的version字段
@property (nonatomic, copy) NSString *version;

/// 富媒体的算法全局信息，对应detail中的env字段
@property (nonatomic, copy) NSString *env;

/// 富媒体内容列表，对应detail中的results字段
@property (nonatomic, copy) NSArray<TPRichMediaContent *> *contents;
@end

NS_ASSUME_NONNULL_END
