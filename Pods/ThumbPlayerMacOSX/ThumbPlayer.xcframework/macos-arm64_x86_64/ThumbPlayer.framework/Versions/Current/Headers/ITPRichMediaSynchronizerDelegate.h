/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPRichMediaSynchronizerDelegate.h
 * @brief    富媒体同步器ITPRichMediaSynchronizer的delegate接口定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "TPRichMediaData.h"
#import "TPOnInfoParam.h"
#import "TPRichMediaOnInfoID.h"
#import "TPError.h"

@protocol ITPRichMediaSynchronizer;

NS_ASSUME_NONNULL_BEGIN

/// 富媒体同步器ITPRichMediaSynchronizer的delegate接口定义
@protocol ITPRichMediaSynchronizerDelegate <NSObject>

/// 加载资源准备成功
/// 在ITPRichMediaSynchronizer的prepareAsync发起资源加载成功后回调
/// @param synchronizer 富媒体同步器实例
- (void)onRichMediaPrepared:(id<ITPRichMediaSynchronizer>)synchronizer;

/// 富媒体数据回抛
/// 使用ITPRichMediaSynchronizerDelegate的onSelectFeatureSuccess:featureIndex:选择富媒体功能类型后，
/// 富媒体数据将通过该接口与播放器同步地持续地回抛
/// @param synchronizer 富媒体同步器实例
/// @param featureIndex 数据回抛所属的富媒体功能类型索引
/// @param data 富媒体数据
- (void)onRichMediaFeature:(id<ITPRichMediaSynchronizer>)synchronizer featureIndex:(int)featureIndex data:(TPRichMediaData *)data;

/// 富媒体同步器运行出错
/// 富媒体同步器无法再继续工作，只能通过ITPRichMediaSynchronizer的reset或重新创建实例来解决
/// @param synchronizer 富媒体同步器实例
/// @param error 错误信息
- (void)onRichMedia:(id<ITPRichMediaSynchronizer>)synchronizer error:(TPError *)error;

/// 富媒体引擎消息定义通知
/// @param synchronizer 富媒体同步器实例
/// @param infoID 消息ID，参考TPRichMediaOnInfoID
/// @param infoParam 消息携带的数据
- (void)onRichMedia:(id<ITPRichMediaSynchronizer>)synchronizer infoID:(TPRichMediaOnInfoID)infoID infoParam:(nullable TPOnInfoParam *)infoParam;

/// 某个富媒体功能类型出错
/// 某个富媒体功能类型无法再回抛数据，但不影响其它富媒体功能类型的数据继续回抛
/// @param synchronizer 富媒体同步器实例
/// @param featureIndex 富媒体功能类型
/// @param error 错误信息
- (void)onRichMediaFeature:(id<ITPRichMediaSynchronizer>)synchronizer featureIndex:(int)featureIndex error:(TPError *)error;

/// 选择富媒体功能类型 ITPRichMediaSynchronizer的selectFeatureAsync:optParam:成功通知
/// @param synchronizer 富媒体同步器实例
/// @param featureIndex 富媒体功能类型
- (void)onSelectFeatureSuccess:(id<ITPRichMediaSynchronizer>)synchronizer featureIndex:(int)featureIndex;

/// 取消选择富媒体功能类型 ITPRichMediaSynchronizer的deselectFeatureAsync:成功通知
/// @param synchronizer 富媒体同步器实例
/// @param featureIndex 富媒体功能类型
- (void)onDeselectFeatureSuccess:(id<ITPRichMediaSynchronizer>)synchronizer featureIndex:(int)featureIndex;

@end

NS_ASSUME_NONNULL_END
