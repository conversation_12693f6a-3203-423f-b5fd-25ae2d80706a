/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TPFieldOrder.h
 * @brief    TPFieldOrder 枚举,枚举的值与内核对应
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/8/26
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// TPFieldOrder 枚举,枚举的值与内核对应
/// TPFieldOrderProgressive: 逐行扫描
/// TPFieldOrderTT/TPFieldOrderBB/TPFieldOrderTB/TPFieldOrderBT: 隔行扫描
typedef NS_ENUM(NSInteger, TPFieldOrder) {
    TPFieldOrderUnknown = 0,
    TPFieldOrderProgressive = 1,
    /// Top coded_first, top displayed first
    TPFieldOrderTT = 2,
    /// Bottom coded first, bottom displayed first
    TPFieldOrderBB = 3,
    /// Top coded first, bottom displayed first
    TPFieldOrderTB = 4,
    /// Bottom coded first, top displayed first
    TPFieldOrderBT = 5,
};
