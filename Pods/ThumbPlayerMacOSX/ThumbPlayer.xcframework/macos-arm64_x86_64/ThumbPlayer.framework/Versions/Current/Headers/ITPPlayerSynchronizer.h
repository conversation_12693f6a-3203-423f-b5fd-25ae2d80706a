/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPPlayerSynchronizer.h
 * @brief    播放同步器
 * <AUTHOR>
 * @version  1.0.0
 * @date     2022/11/26
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#import "ITPPlayerSyncSource.h"
#import "TPPlayerSyncStrategy.h"

NS_ASSUME_NONNULL_BEGIN

#pragma mark - ITPPlayerSynchronizerConfig
/// 播放同步器同步配置
@interface TPPlayerSynchronizerConfig : NSObject

/// 同步偏移量
@property (nonatomic, assign) int64_t offsetMs;

/// 同步策略
@property (nonatomic, strong) TPPlayerSyncStrategy *syncStrategy;

/// 同步进度检查时间间隔
@property (nonatomic, assign) int64_t intervalMs;

@end

#pragma mark - ITPPlayerSynchronizer
/// 播放器同步器
@protocol ITPPlayerSynchronizer <ITPPlayerSyncSource>

/// 设置同步配置，可在任意时刻调用更新
/// @param config 同步配置，其中：
///   offset表明自己相对于主同步器的偏移量，例如自己（从播放器）的第0秒希望对齐主播放器的第10秒，则offset=10*1000
- (void)setConfig:(TPPlayerSynchronizerConfig *)config;

/// 设置同步源
/// @param source 同步源
- (void)setSyncSource:(nullable id<ITPPlayerSyncSource>)source;

/// 移交所有同步关系
/// @param synchronizer 接收同步关系的目标同步器
- (void)handoverSync:(id<ITPPlayerSynchronizer>)synchronizer;

/// 重置所有同步关系
- (void)reset;

@end

NS_ASSUME_NONNULL_END
