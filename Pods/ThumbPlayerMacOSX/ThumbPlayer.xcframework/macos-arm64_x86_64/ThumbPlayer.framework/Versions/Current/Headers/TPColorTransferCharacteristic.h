/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TPColorTransferCharacteristic.h
 * @brief    TPColorTransferCharacteristic 枚举,枚举的值与内核对应
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/8/26
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 非线性转换曲线枚举定义. 与内核层(tp2_pixel_format.h TPColorTransferCharacteristic)枚举值保持一致，不要改动
typedef NS_ENUM(NSInteger, TPColorTransferCharacteristic) {
    TPColorTransferCharacteristicReserved0 = 0,
    /// also ITU-R BT1361
    TPColorTransferCharacteristicBT709 = 1,
    TPColorTransferCharacteristicUnspecified = 2,
    TPColorTransferCharacteristicReserved = 3,
    /// also ITU-R BT470M / ITU-R BT1700 625 PAL & SECAM
    TPColorTransferCharacteristicGAMMA22 = 4,
    /// also ITU-R BT470BG
    TPColorTransferCharacteristicGAMMA28 = 5,
    /// also ITU-R BT601-6 525 or 625 / ITU-R BT1358 525 or 625 / ITU-R BT1700 NTSC
    TPColorTransferCharacteristicSMPTE170M = 6,
    TPColorTransferCharacteristicSMPTE240M = 7,
    /// "Linear transfer characteristics"
    TPColorTransferCharacteristicLINEAR = 8,
    /// "Logarithmic transfer characteristic (100:1 range)"
    TPColorTransferCharacteristicLOG = 9,
    /// "Logarithmic transfer characteristic (100 * Sqrt(10) : 1 range)"
    TPColorTransferCharacteristicLOG_SQRT = 10,
    /// IEC 61966-2-4
    TPColorTransferCharacteristicIEC61966_2_4 = 11,
    /// ITU-R BT1361 Extended Colour Gamut
    TPColorTransferCharacteristicBT1361_ECG = 12,
    /// IEC 61966-2-1 (sRGB or sYCC)
    TPColorTransferCharacteristicIEC61966_2_1 = 13,
    /// ITU-R BT2020 for 10-bit system
    TPColorTransferCharacteristicBT2020_10 = 14,
    /// ITU-R BT2020 for 12-bit system
    TPColorTransferCharacteristicBT2020_12 = 15,
    /// SMPTE ST 2084 for 10-, 12-, 14- and 16-bit systems
    TPColorTransferCharacteristicSMPTE2084 = 16,
    TPColorTransferCharacteristicSMPTEST2084 = TPColorTransferCharacteristicSMPTE2084,
    /// SMPTE ST 428-1
    TPColorTransferCharacteristicSMPTE428 = 17,
    TPColorTransferCharacteristicSMPTEST428_1 = TPColorTransferCharacteristicSMPTE428,
    /// ARIB STD-B67, known as "Hybrid log-gamma"
    TPColorTransferCharacteristicARIB_STD_B67 = 18,
};
