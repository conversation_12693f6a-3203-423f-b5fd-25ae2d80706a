/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPSimulatedLiveMediaAsset.h
 * @brief    模拟直播资源（伪直播）。 通过不断添加下一个视频源，来模拟直播的场景
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/11/8.
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import "ITPMediaAsset.h"

NS_ASSUME_NONNULL_BEGIN

/// 模拟直播资源请求器
@protocol ITPSimulatedLiveAssetRequest <NSObject>

/// 追加asset
/// - Parameter asset: 视频asset
- (void)appendMediaAsset:(id<ITPMediaAsset>)asset;

@end

/// 模拟直播资源delegated定义
@protocol ITPSimulatedLiveDelegate <NSObject>

@required

/// 获取下一个播放资源的回调
/// - Parameter requester: 播放资源请求器
- (void)onNextAssetRequired:(id<ITPSimulatedLiveAssetRequest>)requester;

@end


/// 模拟直播资源，目前仅限用于hls类型的url资源。 通过不断添加下一个视频源，来模拟直播的场景
@protocol ITPSimulatedLiveMediaAsset <ITPMediaAsset>

/// 起始asset
/// 目前仅限用于hls类型的url资源
@property(nonatomic, strong) id<ITPMediaAsset> startAsset;

/// 模拟直播资源delegate
@property(nonatomic, weak) id<ITPSimulatedLiveDelegate> simulatedLiveDelegate;

@end

NS_ASSUME_NONNULL_END
