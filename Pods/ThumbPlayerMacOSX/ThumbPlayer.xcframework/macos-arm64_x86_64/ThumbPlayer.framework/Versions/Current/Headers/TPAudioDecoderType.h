/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPAudioDecoderType.h
 * @brief    音频解码器类型定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/23
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 解码器类型定义，与内核层数值定义一致
typedef NS_ENUM(NSInteger, TPAudioDecoderType) {
    /// 未知解码类型
    TPAudioDecoderTypeUnknown  = -1,
    
    /// audio
    /// 使用FFmpeg解码音频（软解）
    TPAudioDecoderFFmpeg       = 1,
    /// 使用AudioToolbox解码音频（硬解）
    TPAudioDecoderAudioToolbox = 3,
    /// 使用杜比解码器解码音频（软解）
    TPAudioDecoderDolby        = 4,
    /// 使用standalone 解码音频（软解）
    TPAudioDecoderStandalone   = 5,
};

