/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPLocalProxy.h
 * @brief    构建媒体资源的本地代理接口 使用传输组件生成媒体资源的本地代理地址，业务方可从该地址获取数据或直接用该地址播放
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPMediaAsset.h"
#import "TPDownloadProgressInfo.h"
#import "TPError.h"

NS_ASSUME_NONNULL_BEGIN

/// 本地代理回调
@protocol ITPLocalProxyDelegate <NSObject>

@optional

/// 下载进度更新
/// @param downloadProgressInfo 下载进度信息
- (void)onDownloadProgressUpdate:(TPDownloadProgressInfo *)downloadProgressInfo;

/// 下载完成
- (void)onDownloadSuccess;

/// 下载出错
/// @param error 错误信息
- (void)onDownloadError:(TPError *)error;

@end

/// 构建媒体资源的本地代理接口
@protocol ITPLocalProxy <NSObject>

/// @brief 构建本地代理地址
/// @param mediaAsset 播放资源（目前仅支持在线的TPUrlMediaAsset和TPDrmMediaAsset）
/// @param delegate 回调监听
/// @return 本地代理地址，如果代理失败，或者资源不合法将会返回空，重复构建本地代理地址（多次调用该接口），也会返回空
- (nullable NSString *)buildProxyUrl:(id<ITPMediaAsset>)mediaAsset delegate:(nullable id<ITPLocalProxyDelegate>)delegate;

/// @brief 释放本地代理
- (void)releaseLocalProxy;

@end

NS_ASSUME_NONNULL_END

