/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPSampleFormat.h
 * @brief    音频sample format定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 音频sample format定义，这些枚举的定义必须与内核保持一致
typedef NS_ENUM(NSInteger, TPSampleFormat) {
    /// undefined
    TPSampleFormatUnknown = -1,
    /// unsigned 8 bits
    TPSampleFormatU8   = 0,
    /// signed 16 bits
    TPSampleFormatS16  = 1,
    /// signed 32 bits
    TPSampleFormatS32  = 2,
    /// float
    TPSampleFormatFLT  = 3,
    /// double
    TPSampleFormatDBL  = 4,
    /// unsigned 8 bits, planar
    TPSampleFormatU8P  = 5,
    /// signed 16 bits, planar
    TPSampleFormatS16P = 6,
    /// signed 32 bits, planar
    TPSampleFormatS32P = 7,
    /// float, planar
    TPSampleFormatFLTP = 8,
    /// double, planar
    TPSampleFormatDBLP = 9,
    /// signed 64 bits
    TPSampleFormatS64  = 10,
    /// signed 64 bits, planar
    TPSampleFormatS64P = 11,
};
