/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPPlayerSyncClock.h
 * @brief    播放器同步时钟
 * <AUTHOR>
 * @version  1.0.0
 * @date     2025/2/11
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#include <stdint.h>

/**
 * 播放器同步时钟
 */
@interface TPPlayerSyncClock : NSObject

/** 播放器位置 */
@property (nonatomic, assign) int64_t playerPositionMs;

/** 获取 playerPositionMs 时的系统时间，since 1970 {@link [NSDate date] timeIntervalSince1970]} */
@property (nonatomic, assign) int64_t systemTimeSince1970Ms;

@end
