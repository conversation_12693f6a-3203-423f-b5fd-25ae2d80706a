/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPPlayerConstructParams.h
 * @brief    创建TPPlayer时传入的构造参数
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/17
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPPlayerCoreType.h"
#import "TPOptionalParam.h"

NS_ASSUME_NONNULL_BEGIN

/// 以下是播放器构建参数key的定义。
/// 格式如下: TPConstructID + 数据类型 (Bool/Long/Float/String/QueueInt/QueueString/Object) + 名称。
/// 举例说明：
/// TPConstructIDQueueIntCoreType表示此option id是用于设置播放器内核类型选择列表，
/// 它的value的数据类型是int[]类型。
///
/// 播放器内核类型选择列表, 自研播放器内核类型或者系统播放器内核类型，取值参考TPPlayerCoreType中播放器内核类型定义。
/// 注：设置后，ITPPlayer#stop调用后，仍然是同一个播放会话（生命周期），并不会重新或者重置播放器内核类型选择策略，
/// 会保持为当前的播放器内核类型。
/// 举例，设置播放器内核类型策略为[TPPlayerCoreTypeSelfDevPlayer, TPPlayerCoreTypeSystemAVPlayer]
/// a) 如果ITPPlayer#stop调用时，播放器没有发生内核类型切换，当前播放器内核类型为自研播放器内核类型，后面重新
///   ITPPlayer#prepareAsync后，播放器内核类型仍然是自研播放器，中间发生错误时，仍然会切到系统播放器内核类型进行重试。
/// b) 如果ITPPlayer#stop调用时，播放器已经发生了内核类型切换，从自研播放器内核已经切到了系统播放器内核类型，则后面重新
///   ITPPlayer#prepareAsync后，播放器内核类型会是当前正在使用的播放器内核类型：系统播放器内核类型，再次发生错误时，
///   将会直接报错。
/// 在ITPPlayer#reset后，会重置播放器内核类型列表为构造时传入的列表。可以认为是以初始传入的playerCoreTypes重新
/// 构造的播放器。默认值：内部决策, 默认优先只使用自研播放器内核, 如果创建自研播放器失败，则创建系统播放器。具体实现参考TPPlayerCoreChooser
FOUNDATION_EXPORT NSString *const TPConstructIDQueueIntCoreType;

/// 创建ITPPlayer实例时传入的构造参数
@interface TPPlayerConstructParams : NSObject

/// 添加构造播放器的可选参数。参数的key限定为TPPlayerConstructParams本类中的key定义
/// @param optionalParam 可选参数
- (void)addOptionalParam:(TPOptionalParam *)optionalParam;

///  获取已经添加的播放器的可选参数。
///  @return 可选参数列表
- (NSArray<TPOptionalParam *> *)optionalParams;

@end

NS_ASSUME_NONNULL_END
