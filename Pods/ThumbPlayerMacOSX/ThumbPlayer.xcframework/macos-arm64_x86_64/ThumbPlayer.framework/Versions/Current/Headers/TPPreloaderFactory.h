/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPPreloaderFactory.h
 * @brief    预加载的工厂类
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#import "ITPPreloader.h"

NS_ASSUME_NONNULL_BEGIN

/// 预加载实例的工厂类
/// @discussion 此工厂类用于创建预加载的实例。
@interface TPPreloaderFactory : NSObject

/// 创建预加载的实例
/// @return 预加载对象
+ (id<ITPPreloader>)createPreloader;

@end

NS_ASSUME_NONNULL_END
