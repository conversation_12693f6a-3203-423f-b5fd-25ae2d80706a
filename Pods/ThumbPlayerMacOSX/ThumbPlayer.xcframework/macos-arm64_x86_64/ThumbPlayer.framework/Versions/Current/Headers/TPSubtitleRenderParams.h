/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPSubtitleRenderParams.h
 * @brief    字幕渲染参数
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 参数标志位，用来标识设置了哪些参数
typedef NS_OPTIONS(int64_t, TPSubtitleParamFlag) {
    TPSubtitleParamFlagUnknown = 0,
    TPSubtitleParamFlagCanvasWidth = (1 << 0),
    TPSubtitleParamFlagCanvasHeight = (1 << 1),
    TPSubtitleParamFlagFontSize = (1 << 2),
    TPSubtitleParamFlagFontColor = (1 << 3),
    TPSubtitleParamFlagOutlineWidth = (1 << 4),
    TPSubtitleParamFlagOutlineColor = (1 << 5),
    TPSubtitleParamFlagLineSpace = (1 << 6),
    TPSubtitleParamFlagStartMargin = (1 << 7),
    TPSubtitleParamFlagEndMargin = (1 << 8),
    TPSubtitleParamFlagVerticalMargin = (1 << 9),
    TPSubtitleParamFlagFontStyleBold = (1 << 10),
    TPSubtitleParamFlagFontScale = (1 << 11),
    TPSubtitleParamFlagShadowColor = (1 << 12),
    TPSubtitleParamFlagShadowRadius = (1 << 13),
    TPSubtitleParamFlagShadowOffset = (1 << 14),
};

/// 字体样式标志位，用来标识设置了哪些字体样式，暂时只支持粗体
typedef NS_OPTIONS(NSInteger, TPSubtitleFontStyle) {
    TPSubtitleFontStyleUnknown = 0,
    TPSubtitleFontStyleBold = (1 << 0),
};

/// 字幕背景渲染参数
@interface TPSubtitleBackgroundParams : NSObject
/// 背景框的填充颜色，ARGB格式
/// 如果不设置，默认为透明(0)
@property(nonatomic, assign) uint32_t backgroundColor;
/// 背景框相对字幕文本的左右边距, 尺寸是在画布中的尺寸，即参考基准是画布的宽高canvasWidth和canvasHeight
/// 如果不设置，默认为0
@property (nonatomic, assign) float backgroundHorizontalMargin;
/// 背景框相对字幕文本的上下边距，尺寸是在画布中的尺寸，即参考基准是画布的宽高canvasWidth和canvasHeight
/// 如果不设置，默认为0
@property (nonatomic, assign) float backgroundVerticalMargin;
/// 背景框边框的颜色，ARGB格式
/// 如果不设置，默认为透明(0),即没有边框
@property (nonatomic, assign) uint32_t backgroundBorderColor;
/// 背景框边框的线宽，尺寸是在画布中的尺寸，即参考基准是画布的宽高canvasWidth和canvasHeight
/// 如果不设置，默认为0
@property (nonatomic, assign) float backgroundBorderWidth;
/// 背景框的圆角半径相对于背景高度高度的比例,取值范围【0-0.5】
/// 如果不设置，默认为0
@property (nonatomic, assign) float cornerRadiusToHeightRatio;

@end

/// 字幕装饰参数
@interface TPSubtitleDecoration : NSObject

/// 左侧图片文件路径
@property (nonatomic, copy) NSString *leftPicPath;

/// 左侧图片显示宽度（像素），0则不显示，默认为0
@property (nonatomic, assign) float leftPicWidth;

/// 左侧图片显示高度（像素），0则不显示，默认为0
@property (nonatomic, assign) float leftPicHeight;

/// 左侧图片左边距（像素），垂直居中，允许负值
@property (nonatomic, assign) float leftPicOffset;

/// 右侧图片文件路径
@property (nonatomic, copy) NSString *rightPicPath;

/// 右侧图片显示宽度（像素），0则不显示，默认为0
@property (nonatomic, assign) float rightPicWidth;

/// 右侧图片显示高度（像素），0则不显示，默认为0
@property (nonatomic, assign) float rightPicHeight;

/// 右侧图片右边距（像素），垂直居中，允许负值
@property (nonatomic, assign) float rightPicOffset;

@end

/// 字幕渲染参数
@interface TPSubtitleRenderParams : NSObject

/// canvasWidth是字幕渲染画布的宽
/// canvasWidth和canvasHeight的比例必须和视频的宽高比一致，否则渲染出的字体会变形。
/// 如果不设置，播放器会取当前视频的大小作为渲染画布的大小。
@property (nonatomic, assign) int canvasWidth;

/// canvasHeight是字幕渲染画布的高
@property (nonatomic, assign) int canvasHeight;

/// paramFlags标识设置哪些渲染参数
@property (nonatomic, assign) TPSubtitleParamFlag paramFlags;

/// paramPriorityFlags标识设置了paramFlags的参数是否优先于其他设定值, 比如vtt文件里默认的字体大小/颜色/高度等
/// 当paramFlags对应位置位后再判断该参数的对应位
@property (nonatomic, assign) TPSubtitleParamFlag paramPriorityFlags;

/// font family name
/// Android默认为"Roboto"（兼容性有待验证）
/// familyName不受paramFlags的控制，字符串不为空则认为已设置，为空则认为未设置
@property (nonatomic, copy, nullable) NSString *familyName;

/// 字体文件，用于设置自定义字体，如果设置了fontFile，优先使用fontFile创建字体，否则使用familyName创建字体
/// 如果使用fontFile创建字体失败，则使用familyName创建字体
@property (nonatomic, copy, nullable) NSString *fontFile;

/// 字体大小
/// 如果设置了fontSize，则必须设置canvasWidth和canvasHeight，否则内部不知道以什么大小为参考来渲染字体
/// 如果设置了fontSize，paramFlags须置位TPSubtitleParamFlagFontSize
/// 如果不设置fontSize，内部会使用默认的字体大小
@property (nonatomic, assign) float fontSize;

/// 字体缩放比例 vtt css专用
/// 使用fontScale乘以vtt设定的font-size: em值再适应视频宽
/// 如果设置了fontScale，paramFlags须置位TP_SUBTITLE_PARAM_FLAG_FONT_SCALE
/// 最终字体像素为fontScale * vtt em * 16 * canvas width(video width) / default width(491)
/// fontScale默认1.0, 视频宽491像素时,中文字号设定为16像素大小, 将vtt文件内字体大小设定为1em(font-size: 1.00em;)
/// 参考https://developer.mozilla.org/zh-CN/docs/Web/CSS/font-size#ems
/// 如果未设置则采用fontSize
@property (nonatomic, assign) float fontScale;

/// 字体颜色，ARGB格式
/// 如果设置了fontColor，paramFlags须置位TPSubtitleParamFlagFontColor
/// 如果不设置，默认为白色不透明(0xFFFFFFFF)
@property (nonatomic, assign) int fontColor;

/// 字体样式，bit位或，见TPSubtitleFontStyle的定义。字体样式同样需要设置paramFlags。
@property (nonatomic, assign) TPSubtitleFontStyle fontStyleFlags;

/// 描边宽度
/// 如果设置了outlineWidth，则必须设置canvasWidth和canvasHeight，否则内部不知道以什么大小为参考来渲染描边
/// 如果设置了outlineWidth，paramFlags须置位TPSubtitleParamFlagOutlineWidth
/// 如果不设置，内部会使用默认的描边宽度
@property (nonatomic, assign) float outlineWidth;

/// 描边颜色，ARGB格式
/// 如果设置了outlineColor，paramFlags须置位TPSubtitleParamFlagOutlineColor
/// 如果不设置，默认为黑色不透明(0xFF000000)
@property (nonatomic, assign) int outlineColor;

/// 行距 如果设置了lineSpace
/// 则必须设置canvasWidth和canvasHeight 如果设置了lineSpace，paramFlags须置位TPSubtitleParamFlagLineSpace
/// 如果不设置，内部会使用默认的行距
@property (nonatomic, assign) float lineSpace;

/// 以下startMargin、endMargin和verticalMargin定义字幕的绘制区域，如果不设置，则使用字幕文件中的设置，如果字幕文件也没有定义，则使用默认的
/// 注意：一旦设置了startMargin、endMargin和yMargin，而字幕文件也定义了这几个参数的一个或多个，则会覆盖字幕文件中相应的参数。
/// 下面示意图描绘了水平书写方向下这几个参数的意义，请借助每个参数的注释来理解
/// -----------------------------------------------------------------------
/// |                                                                      |
/// |                                                                      |
/// |                                                                      |
/// |                        _________________________                     |
/// |----- startMargin -----|  This is subtitle text  |------endMargin-----|
/// |                       |_________________________|                    |
/// |                                    | yMargin                         |
/// -----------------------------------------------------------------------

/// 沿着字幕文本方向的边距，根据不同的书写方向意义不同。
/// startMargin是一个比例值，取值范围[0, 1]，即相对于视频画面大小的比例。
/// 对于水平书写方向，startMargin表示字幕左边距离视频画面左边的距离，比如startMargin=0.05则边距为视频宽度的0.05倍（5%）
/// 对于垂直书写方向（无论从右到左还是从左到右），startMargin表示字幕顶部距离视频画面顶部的距离，比如startMargin=0.05则边距为视频高度的0.05倍（5%）
/// 如果设置了startMargin，paramFlags须置位TPSubtitleParamFlagStartMargin
@property(nonatomic, assign) float startMargin;

/// 沿着字幕文本方向的边距，根据不同的书写方向意义不同。
/// endMargin是一个比例值，取值范围[0, 1]，即相对于视频画面大小的比例。
/// 对于水平书写方向，endMargin表示字幕右边距离视频画面右边的距离，比如endMargin=0.05则边距为视频宽度的0.05倍（5%）
/// 对于垂直书写方向（无论从右到左还是从左到右），endMargin表示字幕底部距离视频画面底部的距离，比如endMargin=0.05则边距为视频高度的0.05倍（5%）
/// 如果设置了endMargin，paramFlags须置位TPSubtitleParamFlagEndMargin
@property(nonatomic, assign) float endMargin;

/// 垂直字幕文本方向的边距，根据不同的书写方向意义不同。 yMargin为一个比例值，取值范围[0, 1]，即相对于视频画面大小的比例
/// 对于水平书写方向，yMargin表示字幕底部距离视频画面底部的距离，比如yMargin=0.05则边距为视频高度的0.05倍（5%）
/// 对于垂直、从右至左书写方向，yMargin表示字幕右边距离视频画面右边的距离，比如yMargin=0.05则边距为视频宽度的0.05倍（5%）
/// 对于垂直、从左至右书写方向，yMargin表示字幕左边距离视频画面左边的距离，比如yMargin=0.05则边距为视频宽度的0.05倍（5%）
/// 如果设置了verticalMargin，paramFlags须置位TPSubtitleParamFlagVerticalMargin
@property(nonatomic, assign) float verticalMargin;

/// 阴影颜色，ARGB格式，如果设置了该字段，须设置标记TPSubtitleParamFlagShadowColor
@property (nonatomic, assign) uint32_t shadowColor;

/// 阴影模糊半径，单位像素，默认为0.0，即没有阴影。如果设置了该字段，须设置标记TPSubtitleParamFlagShadowRadius
@property (nonatomic, assign) float shadowRadius;

/// 阴影在水平方向的偏移，单位为像素，如果设置了该字段，须设置标记TPSubtitleParamFlagShadowOffset
@property (nonatomic, assign) float shadowXOffset;

/// 阴影在垂直方向的偏移，单位为像素，如果设置了该字段，须设置标记TPSubtitleParamFlagShadowOffset，同shadowXOffset
@property (nonatomic, assign) float shadowYOffset;

/// 背景渲染参数，默认为空, 即默认不渲染背景
@property(nonatomic, strong) TPSubtitleBackgroundParams *backgroundParams;

/// 字幕装饰，在背景之上，字幕之下的图片等装饰
@property(nonatomic, strong) TPSubtitleDecoration *decoration;

@end

NS_ASSUME_NONNULL_END
