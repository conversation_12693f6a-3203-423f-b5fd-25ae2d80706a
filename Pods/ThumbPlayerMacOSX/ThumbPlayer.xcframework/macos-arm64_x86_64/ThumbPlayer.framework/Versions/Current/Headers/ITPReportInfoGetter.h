/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     ITPReportInfoGetter.h
 * @brief    数据上报扩展数据获取接口
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/3
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 数据上报扩展数据获取接口
/// 注意事项：
/// 上报扩展数据分为两类：
/// 1.预设置的扩展key，业务方需使用{@link TPReportExtendedCommonKey}中定义的key
/// 2.业务方私有字段，由业务方定义key，但key必须以"ext_"开头，例如"ext_usr_info"
/// 3.非1/2类数据，播放器内部直接会过滤掉
@protocol ITPReportInfoGetter <NSObject>

/// 播放器在初始化阶段调用该接口获取业务方设置的上报内容
/// 该字段默认会放在通用字段中，上报的所有事件都会包含
/// @return 业务方设置的上报内容
- (nullable NSDictionary<NSString *, NSString *> *)onGetInitExtendedReportInfo;

/// 播放器播放过程，周期性调用该接口获取业务方设置的上报内容
/// 该字段默认会放在通用字段中，仅在直播的周期上报事件中包含
/// 若该上报内容中存在key与{@link #getInitExtendedReportInfo()}中的key相同
/// 则会覆盖{@link #getInitExtendedReportInfo()}中key对应的value
/// @return 业务方设置的上报内容
- (nullable NSDictionary<NSString *, NSString *> *)onGetPeriodExtendedReportInfo;

@end
