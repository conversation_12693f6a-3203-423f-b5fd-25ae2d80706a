/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPVideoDecoderType.h
 * @brief    视频解码器类型定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/23
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 视频解码器类型定义，与内核层数值定义一致
typedef NS_ENUM(NSInteger, TPVideoDecoderType) {
    /// 未知解码类型
    TPVideoDecoderTypeUnknown  = -1,
    
    /// video
    /// 使用FFmpeg解码视频（软解）
    TPVideoDecoderFFmpeg       = 101,
    /// 使用VideoToolbox解码视频（硬解）
    TPVideoDecoderVideoToolbox = 103,
    /// 使用standalone 解码视频（软解）
    TPVideoDecoderStandalone   = 104,
    /// 使用 MediaLabVR 解码视频（内部调用的VideoToolbox）
    TPVideoDecoderMediaLabVR   = 107,
};
