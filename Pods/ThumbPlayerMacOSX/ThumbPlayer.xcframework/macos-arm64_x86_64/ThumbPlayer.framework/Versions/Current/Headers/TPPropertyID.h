/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPPropertyID.h
 * @brief    property id枚举定义，用于从播放器中获取属性. 命名格式kTPPropertyID + value类型 + 名字。例如：
 *           kTPPropertyIDStringContainerFormat：value类型为string
 *           kTPPropertyIDLongAudioCodecType：value类型为long
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 封装格式，比如mp4，hls，mov等
FOUNDATION_EXPORT NSString *const TPPropertyIDStringContainerFormat;
/// 音频的codec ID，参考{@link TPAudioCodecType}
FOUNDATION_EXPORT NSString *const TPPropertyIDLongAudioCodecType;
/// 音频的码率，单位bps
FOUNDATION_EXPORT NSString *const TPPropertyIDLongAudioBitrate;
/// 音频的编码profile
FOUNDATION_EXPORT NSString *const TPPropertyIDLongAudioCodecProfile;
/// 音频的通道数
FOUNDATION_EXPORT NSString *const TPPropertyIDLongAudioChannelCount;
/// 音频的session id, id >0为合法值
FOUNDATION_EXPORT NSString *const TPPropertyIDLongAudioSessionId;
/// 音频采样率
FOUNDATION_EXPORT NSString *const TPPropertyIDLongAudioSampleRate;

/// 视频的codec id，参考{@link TPVideoCodecType}
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoCodecType;
/// 视频的码率，单位bps
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoBitrate;
/// 视频的profile
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoCodecProfile;
/// 视频的画面旋转角度
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoRotation;
/// 视频的帧率
FOUNDATION_EXPORT NSString *const TPPropertyIDFloatVideoFrameRate;
/// 视频的颜色空间，参考{@link TPColorSpace}
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoColorSpace;
/// 视频解码的帧数
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoDecodeFrameCount;
/// 视频解码的总耗时，单位us
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoDecodeTotalCostUs;
/// 视频渲染的帧数
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoRenderFrameCount;
/// 视频渲染的总耗时，单位us
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoRenderTotalCostUs;
/// 视频丢帧数
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoDroppedFrameCount;

/// 字幕的codec id，参考{@link TPSubtitleCodecType}
FOUNDATION_EXPORT NSString *const TPPropertyIDLongSubtitleCodecType;

/// 系统播放器AVPlayerSpatialFormats
FOUNDATION_EXPORT NSString *const TPPropertyIDLongAVPlayerAudioSpatialFormats;

/// 系统播放器是否因为外部保护不足而黑屏.   "1"： YES，   "0"：NO
FOUNDATION_EXPORT NSString *const TPPropertyIDLongAVPlayerOutputObscuredDueToInsufficientExternalProtection;

/// 视频宽
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoWidth;

/// 视频高
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoHeight;

/// 扫描方式，参考TPFieldOrder
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoFieldOrder;

/// 非线性转换曲线，参考TPColorTransferCharacteristic
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoColorTransferCharacteristic;

/// 像素格式，参考TPPixelFormat
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoPixelFormat;

/// 视频位深
FOUNDATION_EXPORT NSString *const TPPropertyIDLongVideoBitDepth;

/// 当前正在播放的视频帧的原始pts，单位ms
FOUNDATION_EXPORT NSString *const TPPropertyIDLongCurrentVideoOriginalPtsMs;
