/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPOnInfoParam.h
 * @brief    播放器onInfo回调携带的数据信息，建造者模式
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/2
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

NS_ASSUME_NONNULL_BEGIN

/// 播放器onInfo回调携带的数据信息
@interface TPOnInfoParam : NSObject
/// int64_t型数据载体1
@property (nonatomic, assign) int64_t longParam1;
/// int64_t型数据载体2
@property (nonatomic, assign) int64_t longParam2;
/// float型数据载体1
@property (nonatomic, assign) float floatParam1;
/// float型数据载体2
@property (nonatomic, assign) float floatParam2;
/// NSString*型数据载体1
@property (nonatomic, copy, nullable) NSString *strParam1;
/// NSString*型数据载体2
@property (nonatomic, copy, nullable) NSString *strParam2;
/// 扩展数据载体
@property (nonatomic, strong, nullable) id objParam;

@end

NS_ASSUME_NONNULL_END
