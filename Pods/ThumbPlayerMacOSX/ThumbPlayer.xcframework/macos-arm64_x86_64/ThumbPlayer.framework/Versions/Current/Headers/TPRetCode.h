/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPRetCode.h
 * @brief    返回码定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/8
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 返回码定义. 枚举值必须跟内核相应的值保持一致(TPErrorGeneral.h)。
typedef NS_ENUM(NSInteger, TPRetCode) {
    /// 成功
    TPRetCodeOk = 0,
    /// 失败
    TPRetCodeFailed = 11000001,
    /// 未初始化
    TPRetCodeNotInit = 11000010,
    /// 该功能未实现
    TPRetCodeNotImpl = 11000011,
    /// 无效的输入参数
    TPRetCodeInvalidArg = 11000012,
    /// 状态机错误
    TPRetCodeUnMatchState = 11000013
};
