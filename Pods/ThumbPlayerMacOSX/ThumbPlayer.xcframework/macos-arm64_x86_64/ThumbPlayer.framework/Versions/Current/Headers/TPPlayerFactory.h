/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPPlayerFactory.h
 * @brief    TPPlayer工厂类
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/04/18
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPPlayerProxy.h"

/// TPPlayer工厂类，用于创建播放器实例
@interface TPPlayerFactory : NSObject

/// 创建ITPPlayer实例。默认使用自研播放器内核，如果创建自研播放器失败，会创建系统播放器。
/// 如果需要指定播放器内核，请使用 TPPlayerFactory#createTPPlayerWithWorkerQueue:delegateQueue:constructParams: 接口。
/// @return ITPPlayer实例。如果创建失败，则返回nil
+ (nullable id<ITPPlayer>)createTPPlayer;

/// 创建ITPPlayer实例。默认使用自研播放器内核，如果创建自研播放器失败，会创建系统播放器。
/// 如果需要指定播放器内核，请使用 TPPlayerFactory#createTPPlayerWithWorkerQueue:delegateQueue:constructParams: 接口。
/// @param workerQueue 播放器操作所在的队列，如果外部不设置，TPPlayer会内部创建一个
/// @return ITPPlayer实例。如果创建失败，则返回nil
+ (nullable id<ITPPlayer>)createTPPlayerWithWorkerQueue:(nullable dispatch_queue_t)workerQueue;

/// 创建ITPPlayer实例。默认使用自研播放器内核，如果创建自研播放器失败，会创建系统播放器。
/// 如果需要指定播放器内核，请使用 TPPlayerFactory#createTPPlayerWithWorkerQueue:delegateQueue:constructParams: 接口。
/// @param workerQueue 播放器操作所在的队列，如果外部不设置，TPPlayer会内部创建一个
/// @param delegateQueue 播放器回调所在队列，如果外部不设置，TPPlayer会内部创建一个
/// @return ITPPlayer实例。如果创建失败，则返回nil
+ (nullable id<ITPPlayer>)createTPPlayerWithWorkerQueue:(nullable dispatch_queue_t)workerQueue
                                          delegateQueue:(nullable dispatch_queue_t)delegateQueue;

/// 创建ITPPlayer实例。如果需要指定播放器内核，请使用该接口。如果创建播放器内核失败，则返回nil。
/// @param workerQueue 播放器操作所在的队列，如果外部不设置，TPPlayer会内部创建一个
/// @param delegateQueue 播放器回调所在队列，如果外部不设置，TPPlayer会内部创建一个
/// @param constructParams 可选初始化参数。在初始化参数中可以指定播放器内核类型，参考TPPlayerConstructParams
/// @return ITPPlayer实例。如果创建失败，则返回nil
+ (nullable id<ITPPlayer>)createTPPlayerWithWorkerQueue:(nullable dispatch_queue_t)workerQueue
                                          delegateQueue:(nullable dispatch_queue_t)delegateQueue
                                        constructParams:(nullable TPPlayerConstructParams *)constructParams;

/// 设置播放器实例全局代理
/// 可用于hook播放器接口的调用，获取播放器运行态信息
/// 注意: 该全局静态方法被多次或多业务方调用时，仅会保存最新设置的代理，之前的设置会被覆盖掉
/// @param playerProxy 播放器实例代理
+ (void)setTPPlayerProxy:(nullable id<ITPPlayerProxy>)playerProxy;

@end
