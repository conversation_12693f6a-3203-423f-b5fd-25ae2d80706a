/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     ITPPlayerProxy.h
 * @brief    代理播放器接口定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/7/23
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPPlayer.h"

NS_ASSUME_NONNULL_BEGIN

/// 播放器实例代理接口
/// 业务方通过调用 [TPPlayerFactory setTPPlayerProxy:] 设置该代理接口
/// 实现对[TPPlayerFactory createTPPlayer]创建的所有播放器实例进行代理
/// 可用于hook播放器接口的调用，获取播放器运行态信息
@protocol ITPPlayerProxy <NSObject>

/// 创建代理后的播放器实例
/// @param player 原始播放器实例。注意: 该对象可能为nil，实现时需要处理该case，建议返回nil
/// @return ITPPlayer 代理后的播放器实例
- (nullable id<ITPPlayer>)createProxyInstance:(nullable id<ITPPlayer>)player;

@end

NS_ASSUME_NONNULL_END
