/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPPlayerConnection.h
 * @brief    播放器同步连接中的连接接口
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#import "ITPPlayerConnectionNode.h"
#import "TPRetCode.h"

NS_ASSUME_NONNULL_BEGIN

@protocol ITPPlayerConnection <NSObject>

/// 激活连接
/// @see TPRetCode
/// @return 返回值，激活成功返回TPRetCodeOk,否则具体错误
- (TPRetCode)activateConnection;

/// 反激活连接
/// @see TPRetCode
/// @return 返回值，反激活成功返回TPRetCodeOk,否则具体错误
- (TPRetCode)deactivateConnection;

/// 释放连接器，后续无法使用
- (void)releaseConnection;

/// 获取连接主节点，包含了对应的播放器地址和连接参数配置
/// @return 主节点
- (id<ITPPlayerConnectionNode>)masterNode;

/// 获取连接从节点，包含了对应的播放器地址和连接参数配置
/// @return: 从节点
- (id<ITPPlayerConnectionNode>)subNode;

@end

NS_ASSUME_NONNULL_END
