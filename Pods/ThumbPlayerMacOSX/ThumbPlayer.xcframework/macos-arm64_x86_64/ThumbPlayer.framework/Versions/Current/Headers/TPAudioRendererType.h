/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPAudioRendererType.h
 * @brief    音频渲染器类型定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 音频 Renderer类型定义
typedef NS_ENUM(NSInteger, TPAudioRendererType) {
    /// None，使用该配置值可以关闭渲染
    TPAudioRendererTypeNone         = -1,
    
    /// 使用AudioQueue渲染音频
    TPAudioRendererTypeAudioQueue   = 6,
};
