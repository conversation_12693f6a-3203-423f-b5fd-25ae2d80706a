/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TPReportExtendedCommonKey.h
 * @brief    数据上报扩展预设字段定义，必须以"reserved_ext_"开头
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/3
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 数据上报扩展预设字段定义，必须以"reserved_ext_"开头
/// 该预设字段可通过下面两个listener，将数据写入返回的map中
/// {@link ITPReportInfoGetter#getInitExtendedReportInfo()}
/// {@link ITPReportInfoGetter#getPeriodExtendedReportInfo()}
/// 并通过{@link ITPReportExtendedController#setReportInfoGetter(ITPReportInfoGetter)}设置给上报模块
/// 上报模块将会把数据一同进行上报

/// --------------------------------------------------------------
/// 通用预设参数，播放器中台提供字段，但是实际value不理解，只透传
/// --------------------------------------------------------------
/// 业务方根据实际业务场景，设置对应的场景id，方便区分同一包名下不同业务场景
FOUNDATION_EXPORT NSString *const TPReportExtendedKeyScenesId;
/// 业务方填写对应的guid，可用于串接业务与播放器中台的上报数据
FOUNDATION_EXPORT NSString *const TPReportExtendedKeyAppGuid;

/// --------------------------------------------------------------
/// 直播预设参数，播放器中台提供字段，但是实际value不理解，只透传
/// --------------------------------------------------------------
/// 上行编码延迟时间，单位ms
FOUNDATION_EXPORT NSString *const TPReportExtendedKeyLivePeriodEncodeDelayMs;
/// CDN延迟时间，单位ms
FOUNDATION_EXPORT NSString *const TPReportExtendedKeyLivePeriodCdnDelayMs;
/// 端到端总延迟时间，单位ms
FOUNDATION_EXPORT NSString *const TPReportExtendedKeyLivePeriodTotalDelayMs;
