/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TPVideoCodecType.h
 * @brief    codec type枚举定义，枚举值与内核TPCodec.h文件中TPCodecID的枚举值保持一致
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/23
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 视频Codec type定义，与内核层数值定义一致
/// Note:枚举的命名采用峰驼无法保留原有意义，+下划线.
typedef NS_ENUM(NSInteger, TPVideoCodecType) {
    TPVideoCodecTypeUnknown = -1,
    
#pragma mark - video codecs
    TPVideoCodecTypeMPEG1VIDEO = 0,
    /// preferred ID for MPEG-1/2 video decoding
    TPVideoCodecTypeMPEG2VIDEO = 1,
    TPVideoCodecTypeH261 = 2,
    TPVideoCodecTypeH263 = 3,
    TPVideoCodecTypeRV10 = 4,
    TPVideoCodecTypeRV20 = 5,
    TPVideoCodecTypeMJPEG = 6,
    TPVideoCodecTypeMJPEGB = 7,
    TPVideoCodecTypeLJPEG = 8,
    TPVideoCodecTypeSP5X = 9,
    TPVideoCodecTypeJPEGLS = 10,
    TPVideoCodecTypeMPEG4 = 11,
    TPVideoCodecTypeRAWVIDEO = 12,
    TPVideoCodecTypeMSMPEG4V1 = 13,
    TPVideoCodecTypeMSMPEG4V2 = 14,
    TPVideoCodecTypeMSMPEG4V3 = 15,
    TPVideoCodecTypeWMV1 = 16,
    TPVideoCodecTypeWMV2 = 17,
    TPVideoCodecTypeH263P = 18,
    TPVideoCodecTypeH263I = 19,
    TPVideoCodecTypeFLV1 = 20,
    TPVideoCodecTypeSVQ1 = 21,
    TPVideoCodecTypeSVQ3 = 22,
    TPVideoCodecTypeDVVIDEO = 23,
    TPVideoCodecTypeHUFFYUV = 24,
    TPVideoCodecTypeCYUV = 25,
    TPVideoCodecTypeH264 = 26,
    TPVideoCodecTypeINDEO3 = 27,
    TPVideoCodecTypeVP3 = 28,
    TPVideoCodecTypeTHEORA = 29,
    TPVideoCodecTypeASV1 = 30,
    TPVideoCodecTypeASV2 = 31,
    TPVideoCodecTypeFFV1 = 32,
    TPVideoCodecType4XM = 33,
    TPVideoCodecTypeVCR1 = 34,
    TPVideoCodecTypeCLJR = 35,
    TPVideoCodecTypeMDEC = 36,
    TPVideoCodecTypeROQ = 37,
    TPVideoCodecTypeINTERPLAY_VIDEO = 38,
    TPVideoCodecTypeXAN_WC3 = 39,
    TPVideoCodecTypeXAN_WC4 = 40,
    TPVideoCodecTypeRPZA = 41,
    TPVideoCodecTypeCINEPAK = 42,
    TPVideoCodecTypeWS_VQA = 43,
    TPVideoCodecTypeMSRLE = 44,
    TPVideoCodecTypeMSVIDEO1 = 45,
    TPVideoCodecTypeIDCIN = 46,
    TPVideoCodecType8BPS = 47,
    TPVideoCodecTypeSMC = 48,
    TPVideoCodecTypeFLIC = 49,
    TPVideoCodecTypeTRUEMOTION1 = 50,
    TPVideoCodecTypeVMDVIDEO = 51,
    TPVideoCodecTypeMSZH = 52,
    TPVideoCodecTypeZLIB = 53,
    TPVideoCodecTypeQTRLE = 54,
    TPVideoCodecTypeTSCC = 55,
    TPVideoCodecTypeULTI = 56,
    TPVideoCodecTypeQDRAW = 57,
    TPVideoCodecTypeVIXL = 58,
    TPVideoCodecTypeQPEG = 59,
    TPVideoCodecTypePNG = 60,
    TPVideoCodecTypePPM = 61,
    TPVideoCodecTypePBM = 62,
    TPVideoCodecTypePGM = 63,
    TPVideoCodecTypePGMYUV = 64,
    TPVideoCodecTypePAM = 65,
    TPVideoCodecTypeFFVHUFF = 66,
    TPVideoCodecTypeRV30 = 67,
    TPVideoCodecTypeRV40 = 68,
    TPVideoCodecTypeVC1 = 69,
    TPVideoCodecTypeWMV3 = 70,
    TPVideoCodecTypeLOCO = 71,
    TPVideoCodecTypeWNV1 = 72,
    TPVideoCodecTypeAASC = 73,
    TPVideoCodecTypeINDEO2 = 74,
    TPVideoCodecTypeFRAPS = 75,
    TPVideoCodecTypeTRUEMOTION2 = 76,
    TPVideoCodecTypeBMP = 77,
    TPVideoCodecTypeCSCD = 78,
    TPVideoCodecTypeMMVIDEO = 79,
    TPVideoCodecTypeZMBV = 80,
    TPVideoCodecTypeAVS = 81,
    TPVideoCodecTypeSMACKVIDEO = 82,
    TPVideoCodecTypeNUV = 83,
    TPVideoCodecTypeKMVC = 84,
    TPVideoCodecTypeFLASHSV = 85,
    TPVideoCodecTypeCAVS = 86,
    TPVideoCodecTypeJPEG2000 = 87,
    TPVideoCodecTypeVMNC = 88,
    TPVideoCodecTypeVP5 = 89,
    TPVideoCodecTypeVP6 = 90,
    TPVideoCodecTypeVP6F = 91,
    TPVideoCodecTypeTARGA = 92,
    TPVideoCodecTypeDSICINVIDEO = 93,
    TPVideoCodecTypeTIERTEXSEQVIDEO = 94,
    TPVideoCodecTypeTIFF = 95,
    TPVideoCodecTypeGIF = 96,
    TPVideoCodecTypeDXA = 97,
    TPVideoCodecTypeDNXHD = 98,
    TPVideoCodecTypeTHP = 99,
    TPVideoCodecTypeSGI = 100,
    TPVideoCodecTypeC93 = 101,
    TPVideoCodecTypeBETHSOFTVID = 102,
    TPVideoCodecTypePTX = 103,
    TPVideoCodecTypeTXD = 104,
    TPVideoCodecTypeVP6A = 105,
    TPVideoCodecTypeAMV = 106,
    TPVideoCodecTypeVB = 107,
    TPVideoCodecTypePCX = 108,
    TPVideoCodecTypeSUNRAST = 109,
    TPVideoCodecTypeINDEO4 = 110,
    TPVideoCodecTypeINDEO5 = 111,
    TPVideoCodecTypeMIMIC = 112,
    TPVideoCodecTypeRL2 = 113,
    TPVideoCodecTypeESCAPE124 = 114,
    TPVideoCodecTypeDIRAC = 115,
    TPVideoCodecTypeBFI = 116,
    TPVideoCodecTypeCMV = 117,
    TPVideoCodecTypeMOTIONPIXELS = 118,
    TPVideoCodecTypeTGV = 119,
    TPVideoCodecTypeTGQ = 120,
    TPVideoCodecTypeTQI = 121,
    TPVideoCodecTypeAURA = 122,
    TPVideoCodecTypeAURA2 = 123,
    TPVideoCodecTypeV210X = 124,
    TPVideoCodecTypeTMV = 125,
    TPVideoCodecTypeV210 = 126,
    TPVideoCodecTypeDPX = 127,
    TPVideoCodecTypeMAD = 128,
    TPVideoCodecTypeFRWU = 129,
    TPVideoCodecTypeFLASHSV2 = 130,
    TPVideoCodecTypeCDGRAPHICS = 131,
    TPVideoCodecTypeR210 = 132,
    TPVideoCodecTypeANM = 133,
    TPVideoCodecTypeBINKVIDEO = 134,
    TPVideoCodecTypeIFF_ILBM = 135,
    TPVideoCodecTypeKGV1 = 136,
    TPVideoCodecTypeYOP = 137,
    TPVideoCodecTypeVP8 = 138,
    TPVideoCodecTypePICTOR = 139,
    TPVideoCodecTypeANSI = 140,
    TPVideoCodecTypeA64_MULTI = 141,
    TPVideoCodecTypeA64_MULTI5 = 142,
    TPVideoCodecTypeR10K = 143,
    TPVideoCodecTypeMXPEG = 144,
    TPVideoCodecTypeLAGARITH = 145,
    TPVideoCodecTypePRORES = 146,
    TPVideoCodecTypeJV = 147,
    TPVideoCodecTypeDFA = 148,
    TPVideoCodecTypeWMV3IMAGE = 149,
    TPVideoCodecTypeVC1IMAGE = 150,
    TPVideoCodecTypeUTVIDEO = 151,
    TPVideoCodecTypeBMV_VIDEO = 152,
    TPVideoCodecTypeVBLE = 153,
    TPVideoCodecTypeDXTORY = 154,
    TPVideoCodecTypeV410 = 155,
    TPVideoCodecTypeXWD = 156,
    TPVideoCodecTypeCDXL = 157,
    TPVideoCodecTypeXBM = 158,
    TPVideoCodecTypeZEROCODEC = 159,
    TPVideoCodecTypeMSS1 = 160,
    TPVideoCodecTypeMSA1 = 161,
    TPVideoCodecTypeTSCC2 = 162,
    TPVideoCodecTypeMTS2 = 163,
    TPVideoCodecTypeCLLC = 164,
    TPVideoCodecTypeMSS2 = 165,
    TPVideoCodecTypeVP9 = 166,
    TPVideoCodecTypeAIC = 167,
    TPVideoCodecTypeESCAPE130 = 168,
    TPVideoCodecTypeG2M = 169,
    TPVideoCodecTypeWEBP = 170,
    TPVideoCodecTypeHNM4_VIDEO = 171,
    TPVideoCodecTypeHEVC = 172,
    TPVideoCodecTypeFIC = 173,
    TPVideoCodecTypeALIAS_PIX = 174,
    TPVideoCodecTypeBRENDER_PIX = 175,
    TPVideoCodecTypePAF_VIDEO = 176,
    TPVideoCodecTypeEXR = 177,
    TPVideoCodecTypeVP7 = 178,
    TPVideoCodecTypeSANM = 179,
    TPVideoCodecTypeSGIRLE = 180,
    TPVideoCodecTypeMVC1 = 181,
    TPVideoCodecTypeMVC2 = 182,
    TPVideoCodecTypeHQX = 183,
    TPVideoCodecTypeTDSC = 184,
    TPVideoCodecTypeHQ_HQA = 185,
    TPVideoCodecTypeHAP = 186,
    TPVideoCodecTypeDDS = 187,
    TPVideoCodecTypeDXV = 188,
    TPVideoCodecTypeSCREENPRESSO = 189,
    TPVideoCodecTypeRSCC = 190,
    TPVideoCodecTypeAVS2 = 191,
    TPVideoCodecTypeAVS3 = 192,
    TPVideoCodecTypeVVC = 193,
    
    TPVideoCodecTypeY41P = 1000,
    TPVideoCodecTypeAVRP = 1001,
    TPVideoCodecType012V = 1002,
    TPVideoCodecTypeAVUI = 1003,
    TPVideoCodecTypeAYUV = 1004,
    TPVideoCodecTypeTARGA_Y216 = 1005,
    TPVideoCodecTypeV308 = 1006,
    TPVideoCodecTypeV408 = 1007,
    TPVideoCodecTypeYUV4 = 1008,
    TPVideoCodecTypeAVRN = 1009,
    TPVideoCodecTypeCPIA = 1010,
    TPVideoCodecTypeXFACE = 1011,
    TPVideoCodecTypeSNOW = 1012,
    TPVideoCodecTypeSMVJPEG = 1013,
    TPVideoCodecTypeAPNG = 1014,
    TPVideoCodecTypeDAALA = 1015,
    TPVideoCodecTypeCFHD = 1016,
    TPVideoCodecTypeTRUEMOTION2RT = 1017,
    TPVideoCodecTypeM101 = 1018,
    TPVideoCodecTypeMAGICYUV = 1019,
    TPVideoCodecTypeSHEERVIDEO = 1020,
    TPVideoCodecTypeYLC = 1021,
    TPVideoCodecTypePSD = 1022,
    TPVideoCodecTypePIXLET = 1023,
    TPVideoCodecTypeSPEEDHQ = 1024,
    TPVideoCodecTypeFMVC = 1025,
    TPVideoCodecTypeSCPR = 1026,
    TPVideoCodecTypeCLEARVIDEO = 1027,
    TPVideoCodecTypeXPM = 1028,
    TPVideoCodecTypeAV1 = 1029,
    TPVideoCodecTypeBITPACKED = 1030,
    TPVideoCodecTypeMSCC = 1031,
    TPVideoCodecTypeSRGC = 1032,
    TPVideoCodecTypeSVG = 1033,
    TPVideoCodecTypeGDV = 1034,
    TPVideoCodecTypeFITS = 1035,
    TPVideoCodecTypeIMM4 = 1036,
    TPVideoCodecTypePROSUMER = 1037,
    TPVideoCodecTypeMWSC = 1038,
    TPVideoCodecTypeWCMV = 1039,
    TPVideoCodecTypeRASC = 1040,
};
