/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPPlayerConnectionFactory.h
 * @brief    多播放器同步连接的创建工厂
 *           可用于创建一个节点 @see ITPPlayerConnectionNode
 *           可用于创建一个连接 @see ITPPlayerConnection
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPPlayerConnectionNode.h"
#import "ITPPlayerConnection.h"
#import "ITPPlayer.h"

NS_ASSUME_NONNULL_BEGIN

/// 多播放器同步连接的创建工厂
@interface TPPlayerConnectionFactory : NSObject

/// 创建一个多播放器同步连接的节点
/// @param player 节点中的播放器实例，不可为空
/// @return 节点实例，可用于后续的播放器同步连接
+ (id<ITPPlayerConnectionNode>)createConnectionNode:(id<ITPPlayer>)player;

///  创建一个连接，如果创建失败，将会返回nil
/// @param masterNode 主节点，对外提供同步时钟，并定义了播放器的地址以及配置
/// @param subNode 从节点，对外获取同步时钟，并定义了播放器的地址以及配置
/// @return 连接实例，可以用于后续移除、激活、反激活等操作
+ (nullable id<ITPPlayerConnection>)createConnectionWithMasterNode:(id<ITPPlayerConnectionNode>)masterNode
                                                           subNode:(id<ITPPlayerConnectionNode>)subNode;

@end

NS_ASSUME_NONNULL_END
