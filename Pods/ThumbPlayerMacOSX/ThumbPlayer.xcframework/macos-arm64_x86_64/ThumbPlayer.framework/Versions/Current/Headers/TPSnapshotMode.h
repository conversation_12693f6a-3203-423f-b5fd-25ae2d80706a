/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPSnapshotMode.h
 * @brief    截图模式
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/04/06
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 截图模式
/// 该截图模式仅有iOS侧有，在字幕输出数据类型为TPSubtitleDataTypeImage时，Android侧是把字幕图片数据抛给调用方，由调用方来渲染上屏，
/// 而iOS侧是内部渲染上屏，所以iOS侧如果调用方想截取字幕，或者视频+字幕，需要从播放器取，所以提供了该截图模式供调用方选择
/// 模式可以叠加，例如想要视频+字幕，则使用TPSnapshotModeVideo|TPSnapshotModeSubtitle
/// 也可以反选，例如除了字幕其他都想要，则使用TPSnapshotModeAll&(~TPSnapshotModeSubtitle)
typedef NS_OPTIONS(NSUInteger, TPSnapshotMode) {
    /// 只截取视频
    TPSnapshotModeVideo = 1 << 0,
    /// 只截取字幕
    TPSnapshotModeSubtitle = 1 << 1,
    /// 所有图层都叠加
    TPSnapshotModeAll = NSUIntegerMax,
};
