/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPPreloader.h
 * @brief    预加载接口的定义
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

#import "ITPMediaAsset.h"
#import "TPError.h"
#import "TPDownloadProgressInfo.h"

NS_ASSUME_NONNULL_BEGIN

/// 非法的预加载id， 预加载失败将会返回
static const int kInvalidPreloadID = -1;

/// 数据预加载接口回调接口
@protocol ITPPreloadDelegate <NSObject>
@optional
 
/// 预加载成功
/// @param preloadID startPreload: 返回的preload id
- (void)onPreloadSuccess:(int)preloadID;

/// 预加载失败
/// @param preloadID preloadId startPreload: 返回的preload id
/// @param error error 错误信息
- (void)onPreloadError:(int)preloadID error:(TPError *)error;

/// 预加载进度
/// @param preloadID startPreload: 返回的preload id
/// @param progressInfo 下载进度信息
- (void)onPreloadProgressUpdate:(int)preloadID progressInfo:(TPDownloadProgressInfo *)progressInfo;

@end

/// 数据预加载接口
@protocol ITPPreloader <NSObject>

/// 预加载监听，本property要先于 startPreload: 方法才可以正常收到回调
@property (nonatomic, weak) id<ITPPreloadDelegate> _Nullable delegate;

/// 启动预加载，如果启动失败将会返回 kInvalidPreloadID
/// 本方法需要先设置delegate才可以正常收到回调
/// @param asset 媒体资源，当前只支持 ITPUrlMediaAsset ITPMultiMediaAsset
/// @return preloadID 预加载id, 停止预加载将会使用到
- (int)start:(id<ITPMediaAsset>)asset;

/// 停止预加载
/// @param preloadID startPreload: 返回的preload id
- (void)stop:(int)preloadID;
@end

NS_ASSUME_NONNULL_END
