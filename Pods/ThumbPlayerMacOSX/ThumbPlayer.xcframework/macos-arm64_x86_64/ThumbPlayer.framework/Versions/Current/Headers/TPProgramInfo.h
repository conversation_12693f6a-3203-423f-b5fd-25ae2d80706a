/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPProgramInfo.h
 * @brief    描述一路节目（Program）的信息。一路节目包含若干相关的媒体轨道，如音频，视频，字幕等。
 *           如果HLS多码率的流，每一种码率包含跟该码率对应的音频，视频，字幕，就可称为一路节目。
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 描述一路节目（Program）的信息
@interface TPProgramInfo : NSObject

/// 当前节目是否被激活（在播放）
@property (nonatomic, assign) BOOL activated;

/// 当前节目Id
@property (nonatomic, assign) int programId;

/// 当前节目数据的带宽，在父类m3u8中用于描述子类m3u8的bitrate，单位为bps，例如：7680000
@property (nonatomic, assign) int64_t bandwidth;

///当前节目Resolution，在父类m3u8中用于描述子类m3u8的宽高信息，例如："1280x720"
@property (nonatomic, copy) NSString *resolution;

/// 当前节目的url，在父类m3u8中用于描述子类m3u8的url信息
@property (nonatomic, copy) NSString *url;

@end

NS_ASSUME_NONNULL_END
