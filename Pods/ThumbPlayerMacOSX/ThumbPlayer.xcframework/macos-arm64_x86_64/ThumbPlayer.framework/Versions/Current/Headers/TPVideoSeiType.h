/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPVideoSeiType.h
 * @brief    Video Sei定义
 *           Rec. ITU-T H.264 Annex D.1.1
 *           Rec. ITU-T H.265 Annex D.2.1
 *           Rec. ITU-T H.266 Annex D.2.1
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/2/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// Video Sei定义
/// 与内核TPSeiInfo.h中的定义保持一致，不要修改这里的枚举值
typedef NS_ENUM(NSInteger, TPVideoSeiType) {
    /// unknown sei type
    TPVideoSeiTypeUnknown = -1,
    /// buffering period (H.264, D.1.1, H.265 D.2.1)
    TPVideoSeiTypeBufferingPeriod = 0,
    /// picture timing
    TPVideoSeiTypePicTiming = 1,
    /// pan-scan rectangle
    TPVideoSeiTypePanScanRect = 2,
    /// filler data
    TPVideoSeiTypeFillerPayload = 3,
    /// registered user data as specified by Rec. ITU-T T.35
    TPVideoSeiTypeUserDataRegisteredItuTT35 = 4,
    /// unregistered user data
    TPVideoSeiTypeUserDataUnregistered = 5,
    /// recovery point (frame # to decoder sync)
    TPVideoSeiTypeRecoveryPoint = 6,
    /// scene_info
    TPVideoSeiTypeSceneInfo = 9,
    /// full_frame_snapshot
    TPVideoSeiTypeFullFrameSnapshot = 15,
    /// progressive_refinement_segment_start
    TPVideoSeiTypeProgressiveRefinementSegmentStart = 16,
    /// progressive_refinement_segment_end
    TPVideoSeiTypeProgressiveRefinementSegmentEnd = 17,
    /// film_grain_characteristics
    TPVideoSeiTypeFilmGrainCharacteristics = 19,
    /// post_filter_hint
    TPVideoSeiTypePostFilterHint = 22,
    /// tone_mapping_info
    TPVideoSeiTypeToneMappingInfo = 23,
    /// frame packing arrangement
    TPVideoSeiTypeFramePacking = 45,
    /// display orientation
    TPVideoSeiTypeDisplayOrientation = 47,
    /// GreenMPEG information
    TPVideoSeiTypeGreenMetadata = 56,
    /// structure_of_pictures_info
    TPVideoSeiTypeSopInfo = 128,
    /// active_parameter_sets
    TPVideoSeiTypeActiveParameterSets = 129,
    /// decoding_unit_info
    TPVideoSeiTypeDecodingUnitInfo = 130,
    /// temporal_sub_layer_zero_idx
    TPVideoSeiTypeTemporalLevel0Index = 131,
    /// decoded_picture_hash
    TPVideoSeiTypeDecodedPictureHash = 132,
    /// scalable_nesting
    TPVideoSeiTypeScalableNesting = 133,
    /// region_refresh_info
    TPVideoSeiTypeRegionRefreshInfo = 134,
    /// mastering display properties
    TPVideoSeiTypeMasteringColourVolume = 137,
    /// content_light_level_info
    TPVideoSeiTypeContentLightLevelInfo = 144,
    /// alternative transfer
    TPVideoSeiTypeAlternativeTransferCharacteristics = 147,
};
