/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPPlayerState.h
 * @brief    播放器状态枚举
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/07
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 播放器状态枚举，状态流转可参考下列文档
/// https://iwiki.oa.tencent.com/display/ThumbPlayer/PlayerCore_player
typedef NS_ENUM(NSUInteger, TPPlayerState) {
    TPPlayerStateIdle,
    TPPlayerStateInitialized,
    TPPlayerStatePreparing,
    TPPlayerStatePrepared,
    TPPlayerStateStarted,
    TPPlayerStatePaused,
    TPPlayerStateComplete,
    TPPlayerStateStopping,
    TPPlayerStateStopped,
    TPPlayerStateError,
    TPPlayerStateReleased
};
