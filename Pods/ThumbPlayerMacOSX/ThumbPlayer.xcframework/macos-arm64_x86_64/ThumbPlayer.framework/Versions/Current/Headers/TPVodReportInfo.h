/*****************************************************************************
 * @copyright Copyright (C), 1998-2022, Tencent Tech. Co., Ltd.
 * @file     TPVodReportInfo.h
 * @brief    需要业务侧辅助上报的字段
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/2
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPDefaultReportInfo.h"

/// 点播数据上报基础数据
@interface TPVodReportInfo : TPDefaultReportInfo

/// 是否通过详情页播放, 0：通过getvinfo获取url播放；1：通过详情页获取视频下载地址前播放 2：未知
@property (nonatomic, assign) NSInteger optimizedPlay;
/// 后台getvinfo是否有下发字幕, 0：没下发字幕；1：后台有下发字幕
@property (nonatomic, assign) BOOL hasSubtitles;
/// 用于区分同一个平台号下不同业务场景, 0：默认 1：腾讯视频app内游戏场景（后续如果新增依次递增）
@property (nonatomic, assign) NSInteger bizId;
/// 视频分片数
@property (nonatomic, assign) NSInteger clipCount;
/// 视频本身状态
@property (nonatomic, assign) NSInteger videoStatus;
/// 是否当前播放, 0：不是当前播放；1：是当前播放 ；2：未知
@property (nonatomic, assign) NSInteger currentPlayState;

@end
