/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPColorSpace.h
 * @brief    颜色空间定义，枚举值必须和内核TPPixelFormat保持一致
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/3/1
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

/// 枚举值和内核是保持一致的，不能随意修改
typedef NS_ENUM(NSInteger, TPColorSpace) {
    /// order of coefficients is actually GBR, also IEC 61966-2-1 (sRGB)
    TPColorSpaceRGB = 0,
    /// also ITU-R BT1361 / IEC 61966-2-4 xvYCC709 / SMPTE RP177 Annex B
    TPColorSpaceBT709 = 1,
    /// UNSPECIFIED
    TPColorSpaceUnspecified = 2,
    /// RESERVED
    TPColorSpaceReserved = 3,
    /// FCC Title 47 Code of Federal Regulations 73.682 (a)(20)
    TPColorSpaceFCC = 4,
    /// also ITU-R BT601-6 625 / ITU-R BT1358 625 / ITU-R BT1700 625 PAL & SECAM / IEC 61966-2-4 xvYCC601
    TPColorSpaceBT470BG = 5,
    /// also ITU-R BT601-6 525 / ITU-R BT1358 525 / ITU-R BT1700 NTSC
    TPColorSpaceSMPTE170M = 6,
    /// functionally identical to above
    TPColorSpaceSMPTE240M = 7,
    /// Used by Dirac / VC-2 and H.264 FRext, see ITU-T SG16
    TPColorSpaceYCGCO = 8,
    /// ITU-R BT2020 non-constant luminance system
    TPColorSpaceBT2020NCL = 9,
    /// ITU-R BT2020 constant luminance system
    TPColorSpaceBT2020CL = 10,
    /// SMPTE 2085, Y'D'zD'x
    TPColorSpaceSMPTE2085 = 11,
    /// Chromaticity-derived non-constant luminance system
    TPColorSpaceChromaDerivedNCL = 12,
    /// Chromaticity-derived constant luminance system
    TPColorSpaceChromaDerivedCL = 13,
    /// ITU-R BT.2100-0, ICtCp
    TPColorSpaceICTCP = 14,
};
