/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TPSnapshotorFactory.h
 * @brief    截图实例工厂类
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/12/3
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "ITPSnapshotor.h"

NS_ASSUME_NONNULL_BEGIN

/// 截图生成器工厂
@interface TPSnapshotorFactory : NSObject

/// 创建截图生成器
/// 初始化失败时返回nil
/// @param asset 视频资源
/// @param delegate 截图回调
/// @return ITPSnapshotor实例
+ (nullable id<ITPSnapshotor>)createSnapshotor:(id<ITPMediaAsset>)asset
                                      delegate:(__weak id<ITPSnapshotorDelegate>)delegate;


/// 创建截图生成器
/// 初始化失败时返回nil
/// @param asset 视频资源
/// @param delegate 截图回调
/// @param delegateQueue 截图回调所在工作队列，若传入为空，则ITPSnapshotor内部会创建一个
/// @return ITPSnapshotor实例
+ (nullable id<ITPSnapshotor>)createSnapshotor:(id<ITPMediaAsset>)asset
                                      delegate:(__weak id<ITPSnapshotorDelegate>)delegate
                                 delegateQueue:(dispatch_queue_t)delegateQueue;

@end

NS_ASSUME_NONNULL_END
