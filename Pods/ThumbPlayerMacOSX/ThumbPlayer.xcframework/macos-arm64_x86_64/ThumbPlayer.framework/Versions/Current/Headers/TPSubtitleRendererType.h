/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPSubtitleRendererType.h
 * @brief    字幕渲染器类型定义. 控制字幕输出类型
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/12/11
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>

/// 视频 Renderer类型定义
typedef NS_ENUM(NSInteger, TPSubtitleRendererType) {
    /// None，使用该配置值可以关闭渲染
    TPSubtitleRendererTypeNone         = -1,
    
    /// 使用OpenGL渲染字幕
    TPSubtitleRendererTypeOpenGL       = 101,
    /// 使用Metal渲染字幕
    TPSubtitleRendererTypeMetal        = 102
};
