/*****************************************************************************
 * @copyright Copyright (C), 1998-2024, Tencent Tech. Co., Ltd.
 * @file     TPHdrCapabilityQueryParams.h
 * @brief    hdr能力查询参数
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024/4/2
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPVideoCodecType.h"
#import "TPPlayerCoreType.h"
#import "TPHdrType.h"

/// 视频能力查询参数
@interface TPHdrCapabilityQueryParams : NSObject
/// hdrType
@property (nonatomic, assign) TPHdrType hdrType;
/// codec类型
@property (nonatomic, assign) TPVideoCodecType videoCodecType;
/// 视频宽
@property (nonatomic, assign) int width;
/// 视频高
@property (nonatomic, assign) int height;
/// 帧率
@property (nonatomic, assign) float frameRate;
#if TARGET_OS_OSX
/// NSScreen。只有mac平台会用到
/// 如果不设置该参数或参数为nil，则只查询当前设备是否支持HDR（也就是本机能力），适用于苹果电脑不外接显示器的情况；支持情况详见 https://support.apple.com/en-hk/102205
/// 如果设置了该参数且参数不为nil，则查询当前设备是否支持HDR和当前screen的硬件显示器是否支持HDR，适用于苹果电脑外接显示器的情况
@property (nonatomic, strong) NSScreen *screen;
#endif
@end
