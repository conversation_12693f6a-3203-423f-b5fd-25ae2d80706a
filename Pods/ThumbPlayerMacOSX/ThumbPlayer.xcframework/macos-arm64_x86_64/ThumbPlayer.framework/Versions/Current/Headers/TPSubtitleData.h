/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     TPSubtitleData.h
 * @brief    字幕数据基类
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/03/15
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#import <Foundation/Foundation.h>
#import "TPSubtitleDataType.h"

/// 字幕数据基类，TPSubtitleFrameBuffer 和 TPSubtitleText 都继承于该类
@interface TPSubtitleData : NSObject

/// 字幕数据类型
@property (nonatomic, assign, readonly) TPSubtitleDataType dataType;

/// 当前输出的字幕的开始时间
@property (nonatomic, assign) int64_t startTimeMs;

@end

