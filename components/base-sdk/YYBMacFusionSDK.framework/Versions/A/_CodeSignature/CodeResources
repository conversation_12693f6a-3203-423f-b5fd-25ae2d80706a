<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		aqkckUkAHHEsFbOR2we64NwS+F4=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/BeaconAPI_Base.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			3eeH41ZyO4tIVqp8aGzhrduDMt8=
			</data>
			<key>requirement</key>
			<string>cdhash H"e604588d0974cc0b54962b336fe0eeab406aff05" or cdhash H"dde787e356723b8b4856aa7c686ce1addb8332df" or cdhash H"9ea03fade7c4162c139e2f82fb27d1d0a25bc2dd" or cdhash H"b149a69c74f47a788ac992e9a5a4436c35dd675e"</string>
		</dict>
		<key>Frameworks/Sparkle.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			0ByzfE438Kc1fZn8ug8N84dKQrU=
			</data>
			<key>requirement</key>
			<string>cdhash H"d01cb37c4e37f0a7357d99fcba0f0df3874a42b5" or cdhash H"63c65ea03b049d60db464a2535268bc9e41a0ed6"</string>
		</dict>
		<key>Headers/IYYBMacHttpListener.h</key>
		<dict>
			<key>hash2</key>
			<data>
			T5ouayHdRmL0iZ4UnfxTiyNVNpEdhvpV3kJz7E3gQCo=
			</data>
		</dict>
		<key>Headers/IYYBMacJsonCmdListener.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5Nc04s1oLqgBUYiZ3Q5+s8XiuGrzwm0zD+suTjZNeC0=
			</data>
		</dict>
		<key>Headers/RAFTDownloadProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			z5sPAEFvItXTGlG/YVQ+rmuw7dt2yFB40lfzUQ0NggQ=
			</data>
		</dict>
		<key>Headers/ResHubCommonDefines.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZtsdwFc9veOCrKGyWprBCUqJsL/9qKWDEz769iEiVw4=
			</data>
		</dict>
		<key>Headers/ResHubError.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/IyJMwLN8ojsKfEIa1nx09rJO/BLjck9H/cmB4oGy3A=
			</data>
		</dict>
		<key>Headers/ResHubModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			WZjf1/tG/FKqLppNLyoVDGwH0eqBRv52XTPHspPfQNg=
			</data>
		</dict>
		<key>Headers/ResHubResRefreshListener.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Y5G8qN4uTICOamZCbUJyxMKerWTXMP51yjnhMaqSeuI=
			</data>
		</dict>
		<key>Headers/ResHubSubResModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			bmvaxrJyFXq4EGaPeK00hxkxZIYMYJqENgZGQ2aO7uo=
			</data>
		</dict>
		<key>Headers/SUGrayTacticsModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nETdSI3JiSSHxSAq5VQhhsIEDhUFKTe8b5ZR05Hy254=
			</data>
		</dict>
		<key>Headers/YYBAppcastItem.h</key>
		<dict>
			<key>hash2</key>
			<data>
			J+tcqQGMJF3fZYL4r3mnuLF4cxosMM5SfGrz7qtTrxU=
			</data>
		</dict>
		<key>Headers/YYBExternalUpgradeDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			w8I5tBVNyx9sHagJf8UJTHYgo008vtBlNjZ+MIuYsnE=
			</data>
		</dict>
		<key>Headers/YYBMacBugly.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Jy5RrGWfzF5/hS6nhT+rxJinNycQ7XNXyYbGYxWa2lc=
			</data>
		</dict>
		<key>Headers/YYBMacBuglyC.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ggGsXbdGKvsDpLIsQDvtcB7yuWYJtpeZeYFgYBHuY9I=
			</data>
		</dict>
		<key>Headers/YYBMacDataReport.h</key>
		<dict>
			<key>hash2</key>
			<data>
			b75q8D3z620N9QUrS03tqhROr9Q84Lf1XV3aOjeycVM=
			</data>
		</dict>
		<key>Headers/YYBMacDataReportC.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nq72d6X06cS+/SwyZgfpzDXtO0sl52EoBlzuLFXWDrA=
			</data>
		</dict>
		<key>Headers/YYBMacDataReportDefine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			avs470HQz2b/yJDqD5WXOFFL8hhLF6VDV02MopaXObI=
			</data>
		</dict>
		<key>Headers/YYBMacDataRequest.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YqXPNi2a0ZYYbFDomNsIQ2ECgeGIP/EfU7lMxK2T5FI=
			</data>
		</dict>
		<key>Headers/YYBMacFusion.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JnBh1IF4mabUCwya0/3+fUsXc834FFH7P8l1LaWie68=
			</data>
		</dict>
		<key>Headers/YYBMacFusionSDK.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pdf7Wtxqz2Oi00ko5iAO+cSC9pycNptNGHoNOIfTNSM=
			</data>
		</dict>
		<key>Headers/YYBMacFusionSDKC.h</key>
		<dict>
			<key>hash2</key>
			<data>
			J+YLV5H9Y1Bp+u0kCXtVkE2vweVRXLI6ibp8olXDyiI=
			</data>
		</dict>
		<key>Headers/YYBMacFusionSDKConfig.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ISE997WdrKROv/kXBwrq5Fog+4nUKie5dZqKk8PIrk4=
			</data>
		</dict>
		<key>Headers/YYBMacFusionSDKConfigCpp.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HV41pyvVaEV1AiGwbwDGo74PcxZFmY3OkpVww0trZrU=
			</data>
		</dict>
		<key>Headers/YYBMacHttp.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CY8sYt4jzofMCa2EtZpfKtARX5jnw+PwUGUwy1HfV3E=
			</data>
		</dict>
		<key>Headers/YYBMacHttpC.h</key>
		<dict>
			<key>hash2</key>
			<data>
			1Gq7pFglvTVvLqspcYrKlF0+4fW2OghygJN+ebBiw6c=
			</data>
		</dict>
		<key>Headers/YYBMacHttpDefine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			QlPeXRTXY4lO4eplb+frwEfggO2taJ59fpjq9nd0dCA=
			</data>
		</dict>
		<key>Headers/YYBMacHttpRequest.h</key>
		<dict>
			<key>hash2</key>
			<data>
			wnWG4r1S+pt7xkajqV4F4+4U8el+r/5N0u1fQBLCIzU=
			</data>
		</dict>
		<key>Headers/YYBMacHttpResponse.h</key>
		<dict>
			<key>hash2</key>
			<data>
			dmXYaqZD69HqtFUuMkYx3wYtWJ2jTnxCtWjqs6/tjY8=
			</data>
		</dict>
		<key>Headers/YYBMacJsonCmdRequest.h</key>
		<dict>
			<key>hash2</key>
			<data>
			R7DmWHeUcn6m4YFQlPbRAZ/7NRUpq07w+tSAlvbkmDU=
			</data>
		</dict>
		<key>Headers/YYBMacJsonCmdResponse.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zXh3sYCacG7n48D1ZzRJb0MDPeOpHDYKbFTgzVdmHe8=
			</data>
		</dict>
		<key>Headers/YYBMacJsonRequest.h</key>
		<dict>
			<key>hash2</key>
			<data>
			xOhyWeZFY8cwYEID1LYhCxR2Z26NXSKr2xmxab+EVno=
			</data>
		</dict>
		<key>Headers/YYBMacLog.h</key>
		<dict>
			<key>hash2</key>
			<data>
			7EsElSVpvP7BspidqcU6/HE1eraIK0wGDMPY7TYDohg=
			</data>
		</dict>
		<key>Headers/YYBMacLogC.h</key>
		<dict>
			<key>hash2</key>
			<data>
			bG2orKbC5QdQc/LxxLJvyUJJ7pm5WXWzn6TSL32Mqmo=
			</data>
		</dict>
		<key>Headers/YYBMacLogUpload.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Upfc1jT9IvQDhM0gZCmdLyKb3J0xH36C6rjRqXBTVNA=
			</data>
		</dict>
		<key>Headers/YYBMacLogUploadC.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zH5rOjRM05P2TSavhIw2CjRBSnk94LqTcu4IdoDOs5A=
			</data>
		</dict>
		<key>Headers/YYBMacMMKV.h</key>
		<dict>
			<key>hash2</key>
			<data>
			x7AYX3Qf8AbrmdIaJ5zHHv4f4ksS1sAJglfjzPP7ZTY=
			</data>
		</dict>
		<key>Headers/YYBMacMMKVC.h</key>
		<dict>
			<key>hash2</key>
			<data>
			NUbk9012RHr5teaqMSgrLE7GhnXJ6AEu1MwP/jRHQ3k=
			</data>
		</dict>
		<key>Headers/YYBMacQimei.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/LbcvILXOgYEzhgV7DXLx55MoDjIUpav2sGBVvEutWg=
			</data>
		</dict>
		<key>Headers/YYBMacQimeiC.h</key>
		<dict>
			<key>hash2</key>
			<data>
			b5hwUN410acBQh2Yrksnkik7GXzrLUv1yMI6gyXX6KE=
			</data>
		</dict>
		<key>Headers/YYBMacResHub.h</key>
		<dict>
			<key>hash2</key>
			<data>
			B6ZfCVbmEZlCOI4ZaFm0nBt4c8SxD/MIDvnyDZ3reaQ=
			</data>
		</dict>
		<key>Headers/YYBMacResHubC.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HsCi6Q4eFMI7+vsdVYG2HeHLOBZXQvGHTXCfmRccQNc=
			</data>
		</dict>
		<key>Headers/YYBMacShiplyConfig.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9GEVXfc9soA0TVljVQGPHf3gtLyKsz6uQQayWzExEhM=
			</data>
		</dict>
		<key>Headers/YYBMacShiplyConfigC.h</key>
		<dict>
			<key>hash2</key>
			<data>
			2m5/cKU7PrmqZwacGJ3titsTOUC0FtMXXTsYYtbKxU8=
			</data>
		</dict>
		<key>Headers/YYBMacUpdateConfig.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nTyDCF+MnL7BiwBdKeorBYxFJhIgZi/egU+EkgNXI/s=
			</data>
		</dict>
		<key>Headers/YYBMacUpdateManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			2LG+ynN/d3emaqQN818gJafgyrAb/QxuqlHCXOI6R9o=
			</data>
		</dict>
		<key>Headers/YYBUpdater.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jXBluI70egw9vtGLynDZ89Cjn0YRnL6aBZm3v3lOT1k=
			</data>
		</dict>
		<key>Headers/YYBUpdaterParam.h</key>
		<dict>
			<key>hash2</key>
			<data>
			M1CfJcIbpQ/kVAqXlrJi1CutYPrxivEGtD6RbOyiwN0=
			</data>
		</dict>
		<key>Headers/YYBUpgradeModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			GqN4GT8m5M5rdrqhKP05ihqvXZa2rxn6b5OrG6oXMq4=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			SQpuqcnbpMxV74mJBO6dBAgPoaFE8NrTf2HR+XTbM4k=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PJ1KtfKfN12IIZOGBsiflC+U6GYP3V9MNiuRcegm3CM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
