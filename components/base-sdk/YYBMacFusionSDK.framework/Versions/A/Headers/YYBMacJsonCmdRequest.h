//  Copyright © 2020 Tencent Inc. All rights reserved. 
//
//  YYBMacJsonRequest.h
//  YYBMacJsonRequest
//
//  Created by gannicuswu on 2025/8/14.
//

#import <Foundation/Foundation.h>
#if __has_include(<YYBMacFusionSDK/YYBMacJsonRequest.h>)
#import <YYBMacFusionSDK/YYBMacJsonRequest.h>
#else
#import <YYBMacJsonRequest.h>
#endif

NS_ASSUME_NONNULL_BEGIN

@interface YYBMacJsonCmdRequest : YYBMacJsonRequest

@property (nonatomic, copy) NSString *domain;

@property (nonatomic, copy) NSString *version;

@property (nonatomic, copy) NSString *api;

@property (nonatomic, copy) NSString *scene;

//@property (nonatomic, copy) NSString *cmd;

@property (nonatomic, copy) NSDictionary *bodyValue;

- (instancetype)initV2WithApi:(NSString *)api scene:(NSString *)scene bodyValue:(NSDictionary *)bodyValue;

- (instancetype)initV3WithApi:(NSString *)api bodyValue:(NSDictionary *)bodyValue;

- (BOOL) isVersionSupported;

- (BOOL) isV2;

- (BOOL) isV3;

@end

NS_ASSUME_NONNULL_END
