//
//  YYBMacHttp.h
//  YYBMacHttp
//
//  Created by gannicuswu on 2025/8/14.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>


#if __has_include(<YYBMacFusionSDK/IYYBMacHttpListener.h>)
#import <YYBMacFusionSDK/IYYBMacHttpListener.h>
#else
#import <IYYBMacHttpListener.h>
#endif
#if __has_include(<YYBMacFusionSDK/IYYBMacJsonCmdListener.h>)
#import <YYBMacFusionSDK/IYYBMacJsonCmdListener.h>
#else
#import <IYYBMacJsonCmdListener.h>
#endif
#if __has_include(<YYBMacFusionSDK/YYBMacHttpRequest.h>)
#import <YYBMacFusionSDK/YYBMacHttpRequest.h>
#else
#import <YYBMacHttpRequest.h>
#endif
#if __has_include(<YYBMacFusionSDK/YYBMacJsonCmdRequest.h>)
#import <YYBMacFusionSDK/YYBMacJsonCmdRequest.h>
#else
#import <YYBMacJsonCmdRequest.h>
#endif

NS_ASSUME_NONNULL_BEGIN

// 添加block回调类型
typedef void (^YYBJsonCmdSuccessBlock)(YYBMacJsonCmdResponse * _Nonnull response);
typedef void (^YYBJsonCmdFailureBlock)(NSError * _Nonnull error);

@interface YYBMacHttp : NSObject

+ (instancetype)sharedInstance;
- (instancetype)init NS_UNAVAILABLE;
- (instancetype)new NS_UNAVAILABLE;

- (void)sendHttpRequest:(nonnull YYBMacHttpRequest *)request
                    listener:(nullable id<IYYBMacHttpListener>)listener;

- (void)sendYYBJsonCmd:(nonnull YYBMacJsonCmdRequest *)request
              listener:(nullable id<IYYBMacJsonCmdListener>)listener;

- (void)sendYYBJsonCmd:(nonnull YYBMacJsonCmdRequest *)request
              success:(nullable YYBJsonCmdSuccessBlock)success
              failure:(nullable YYBJsonCmdFailureBlock)failure;

@end

NS_ASSUME_NONNULL_END
