//  Copyright © 2020 Tencent Inc. All rights reserved. 
//  YYBMacHttpDefine.h
//  YYBMacHttpDefine
//
//  Created by gann<PERSON>wu on 2025/8/14.
//

#ifndef YYBMacHttpDefine_h
#define YYBMacHttpDefine_h

// 错误码定义
typedef NS_ENUM(NSInteger, YYBMacHttpErrorCode) {
    YYBMacHttpErrorCode_Success = 0,
    YYBMacHttpErrorCode_RequestNil = -10001, // request is nil
    YYBMacHttpErrorCode_VersionNoSupport = -10002, // not support version
    YYBMacHttpErrorCode_RequestNoApiParam = -10003, // request no api
    YYBMacHttpErrorCode_RequestNoSceneParam = -10004, // request no scene
    YYBMacHttpErrorCode_ResponseNil = -10005, // responseObject is nil
    YYBMacHttpErrorCode_ResponseNotDict = -10006 // responseObject is not NSDictionary
};


typedef NS_ENUM(NSUInteger, YYBMacHttpRequestMethod) {
    YYBMacHttpRequestMethod_GET, /// GET请求
    YYBMacHttpRequestMethod_POST /// POST请求
};

typedef NS_ENUM(NSUInteger, YYBMacHttpResponseType) {
    YYBMacHttpResponseType_DATA,
    YYBMacHttpResponseType_JSON
};



#endif /* YYBMacHttpDefine_h */
