//  Copyright © 2020 Tencent Inc. All rights reserved. 
//
//  YYBMacHttpRequest.h
//  YYBMacHttpRequest
//
//  Created by gannicuswu on 2025/8/14.
//

#import <Foundation/Foundation.h>
#if __has_include(<YYBMacFusionSDK/YYBMacHttpDefine.h>)
#import <YYBMacFusionSDK/YYBMacHttpDefine.h>
#else
#import <YYBMacHttpDefine.h>
#endif

NS_ASSUME_NONNULL_BEGIN

@interface YYBMacHttpRequest : NSObject

/** 
 * 请求的唯一标识
 */
@property (nonatomic, assign) NSUInteger requestId;

/** 
 * 请求地址
 */
@property (nonatomic, copy) NSString *url;

/** 
 * 请求方法,目前只支持GET,POST
 */
@property (nonatomic, assign) YYBMacHttpRequestMethod method;

/**
 * 响应数据类型
 */
@property (nonatomic, assign) YYBMacHttpResponseType responseType;

/**
 * 请求的header
 */
@property (nonatomic, copy) NSDictionary *headers;

/**
 * 超时时长,超时时长只有大于0才会生效
 */
@property (nonatomic, assign) NSTimeInterval timeout;

/**
 * 业务标签
 */
@property (nonatomic, assign) id bizTag;

/**
 * 业务命令字
 */
@property (nonatomic, assign) NSString * bizCmd;

/**
 * 业务自定义统计纬度，用于上报
 */
@property (nonatomic, copy) NSDictionary *bizStat;

- (NSString*) toAFUrl;

- (id) toAFParameters;

- (NSDictionary *) toAFHeaders;

@end

NS_ASSUME_NONNULL_END
