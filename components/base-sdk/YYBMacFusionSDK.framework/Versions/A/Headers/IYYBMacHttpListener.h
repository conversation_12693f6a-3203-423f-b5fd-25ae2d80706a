//  Copyright © 2020 Tencent Inc. All rights reserved. 
//
//  IYYBMacHttpListener.h
//  IYYBMacHttpListener
//
//  Created by gannicuswu on 2025/8/14.
//

#ifndef IYYBMacHttpListener_h
#define IYYBMacHttpListener_h

#if __has_include(<YYBMacFusionSDK/YYBMacHttpRequest.h>)
#import <YYBMacFusionSDK/YYBMacHttpRequest.h>
#else
#import <YYBMacHttpRequest.h>
#endif
#if __has_include(<YYBMacFusionSDK/YYBMacHttpResponse.h>)
#import <YYBMacFusionSDK/YYBMacHttpResponse.h>
#else
#import <YYBMacHttpResponse.h>
#endif

@protocol IYYBMacHttpListener <NSObject>

- (void)onRequestSucc:(nullable YYBMacHttpResponse *)response request:(nonnull YYBMacHttpRequest *)request;
- (void)onRequetFail:(nullable NSError *)error request:(nonnull YYBMacHttpRequest *)request;

@end

#endif /* IYYBMacHttpListener_h */
