//
//  YYBUpdaterParam.h
//  YYBMacUpdater
//
//  Created by bethahuang on 2025/8/16.
//

#import <Foundation/Foundation.h>

@protocol YYBExternalUpgradeDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface YYBUpdaterParam : NSObject

@property(nonatomic, copy, nullable) NSString* qimei36;
@property(nonatomic, copy) NSString* appId;
@property(nonatomic, copy) NSString* appKey;
@property(nonatomic, copy, nullable) NSString* bundlePath;
@property(nonatomic, assign) BOOL isRelease;
@property (nonatomic, assign) NSTimeInterval configUpdateInterval;
@property(atomic, assign) NSInteger configUpdateMode;
@property (atomic, copy, nullable) NSString *bundleId;
@property(nonatomic, strong, nullable) id<YYBExternalUpgradeDelegate> externalDelegate;
@end

NS_ASSUME_NONNULL_END
