//  Copyright © 2020 Tencent Inc. All rights reserved. 
//
//  IYYBMacJsonCmdListener.h
//  IYYBMacJsonCmdListener
//
//  Created by gann<PERSON><PERSON> on 2025/8/14.
//

#ifndef IYYBMacJsonCmdListener_h
#define IYYBMacJsonCmdListener_h

#if __has_include(<YYBMacFusionSDK/YYBMacJsonCmdRequest.h>)
#import <YYBMacFusionSDK/YYBMacJsonCmdRequest.h>
#else
#import <YYBMacJsonCmdRequest.h>
#endif
#if __has_include(<YYBMacFusionSDK/YYBMacJsonCmdResponse.h>)
#import <YYBMacFusionSDK/YYBMacJsonCmdResponse.h>
#else
#import <YYBMacJsonCmdResponse.h>
#endif

@protocol IYYBMacJsonCmdListener <NSObject>

- (void)onJsonCmdResponse:(nullable YYBMacJsonCmdResponse *)response request:(nonnull YYBMacJsonCmdRequest *)request;
- (void)onJsonCmdFail:(nullable NSError *)error request:(nonnull YYBMacJsonCmdRequest *)request;

@end

#endif /* IYYBMacJsonCmdListener_h */
