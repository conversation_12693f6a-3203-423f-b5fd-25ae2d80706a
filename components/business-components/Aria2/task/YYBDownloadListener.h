//
//  YYBDownloadListener.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/8/15.
//

#import <Foundation/Foundation.h>
#import "YYBAria2Task.h"

NS_ASSUME_NONNULL_BEGIN

/**
 *  下载监听协议，每个监听者需声明自己关心的下载唯一key（listenerKey），
 *  同时也能根据Task提取出匹配key（通常是Task的md5/pkgName等）。
 *  注册时所有Listener以listenerDownloadKey为分组聚类，事件分发时从Task提取downloadKey，精准高效。
 */
@protocol YYBDownloadListener <NSObject>

/// 监听者自身关心的下载任务唯一key，注册用key，通常为md5，或者pkgName+version等
- (NSString *)listenerDownloadKey;

/// 给定Task，提取该Listener关心的Key。可根据Task属性自定义提取规则，注意：必须和上面的listenerDownloadKey匹配一致，才能监听到对应下载任务的回调消息
+ (NSString *)downloadKeyForTask:(YYBAria2Task *)task;

/// 进度回调
- (void)onDownloadProgress:(YYBAria2Task *)task progress:(double)progress;

/// 状态变化回调
- (void)onDownloadStatusChanged:(YYBAria2Task *)task status:(YYBAria2TaskStatus)status error:(NSError *_Nullable)error;

/// 二次校验，监听文件的md5，若有则会在listenerDownloadKey基础上，再增加md5的匹配，一致才回调状态变更
@optional
- (nullable NSString *)listenerMD5Key;

@end

NS_ASSUME_NONNULL_END
