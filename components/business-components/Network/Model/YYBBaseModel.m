#import "YYBBaseModel.h"
#import <objc/runtime.h>

static NSString *yyb_snake_case_from_camel(NSString *input) {
    if (input.length == 0) return input;
    NSMutableString *result = [NSMutableString string];
    NSCharacterSet *uppercase = [NSCharacterSet uppercaseLetterCharacterSet];
    for (NSUInteger i = 0; i < input.length; i++) {
        unichar c = [input characterAtIndex:i];
        if ([uppercase characterIsMember:c]) {
            if (i != 0) [result appendString:@"_"];
            [result appendString:[[NSString stringWithCharacters:&c length:1] lowercaseString]];
        } else {
            [result appendFormat:@"%C", c];
        }
    }
    return result;
}

@implementation YYBBaseModel
+ (NSDictionary<NSString *, id> *)modelCustomPropertyMapper {
    NSMutableDictionary *mapper = [NSMutableDictionary dictionary];
    Class cls = self;
    while (cls && cls != [YYBBaseModel class]) {
        unsigned int count = 0;
        objc_property_t *properties = class_copyPropertyList(cls, &count);
        for (unsigned int i = 0; i < count; i++) {
            objc_property_t p = properties[i];
            const char *cname = property_getName(p);
            if (!cname) continue;
            NSString *prop = [NSString stringWithUTF8String:cname];
            // Skip readonly YYModel internals or dynamic
            NSString *snake = yyb_snake_case_from_camel(prop);
            if (snake.length > 0) {
                if ([snake isEqualToString:prop]) {
                    mapper[prop] = prop;
                } else {
                    mapper[prop] = @[snake, prop];
                }
            }
        }
        free(properties);
        cls = class_getSuperclass(cls);
    }
    // Merge extra
    NSDictionary *add = [self yyb_additionalCustomPropertyMapper];
    if (add.count) {
        [mapper addEntriesFromDictionary:add];
    }
    return mapper;
}
@end

@implementation YYBBaseModel (YYBAdditionalMapping)
+ (NSDictionary<NSString *,id> *)yyb_additionalCustomPropertyMapper {
    return @{};
}
@end

